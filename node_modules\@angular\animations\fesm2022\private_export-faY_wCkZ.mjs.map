{"version": 3, "file": "private_export-faY_wCkZ.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/src/animation_metadata.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/src/players/animation_player.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/src/players/animation_group_player.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/src/private_export.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Represents a set of CSS styles for use in an animation style as a generic.\n */\nexport interface ɵStyleData {\n  [key: string]: string | number;\n}\n\n/**\n * Represents a set of CSS styles for use in an animation style as a Map.\n */\nexport type ɵStyleDataMap = Map<string, string | number>;\n\n/**\n * Represents animation-step timing parameters for an animation step.\n * @see {@link animate}\n *\n * @publicApi\n */\nexport declare type AnimateTimings = {\n  /**\n   * The full duration of an animation step. A number and optional time unit,\n   * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n   * The default unit is milliseconds.\n   */\n  duration: number;\n  /**\n   * The delay in applying an animation step. A number and optional time unit.\n   * The default unit is milliseconds.\n   */\n  delay: number;\n  /**\n   * An easing style that controls how an animations step accelerates\n   * and decelerates during its run time. An easing function such as `cubic-bezier()`,\n   * or one of the following constants:\n   * - `ease-in`\n   * - `ease-out`\n   * - `ease-in-and-out`\n   */\n  easing: string | null;\n};\n\n/**\n * @description Options that control animation styling and timing.\n *\n * The following animation functions accept `AnimationOptions` data:\n *\n * - `transition()`\n * - `sequence()`\n * - `group()`\n * - `query()`\n * - `animation()`\n * - `useAnimation()`\n * - `animateChild()`\n *\n * Programmatic animations built using the `AnimationBuilder` service also\n * make use of `AnimationOptions`.\n *\n * @publicApi\n */\nexport declare interface AnimationOptions {\n  /**\n   * Sets a time-delay for initiating an animation action.\n   * A number and optional time unit, such as \"1s\" or \"10ms\" for one second\n   * and 10 milliseconds, respectively.The default unit is milliseconds.\n   * Default value is 0, meaning no delay.\n   */\n  delay?: number | string;\n  /**\n   * A set of developer-defined parameters that modify styling and timing\n   * when an animation action starts. An array of key-value pairs, where the provided value\n   * is used as a default.\n   */\n  params?: {[name: string]: any};\n}\n\n/**\n * Adds duration options to control animation styling and timing for a child animation.\n *\n * @see {@link animateChild}\n *\n * @publicApi\n */\nexport declare interface AnimateChildOptions extends AnimationOptions {\n  duration?: number | string;\n}\n\n/**\n * @description Constants for the categories of parameters that can be defined for animations.\n *\n * A corresponding function defines a set of parameters for each category, and\n * collects them into a corresponding `AnimationMetadata` object.\n *\n * @publicApi\n */\nexport enum AnimationMetadataType {\n  /**\n   * Associates a named animation state with a set of CSS styles.\n   * See [`state()`](api/animations/state)\n   */\n  State = 0,\n  /**\n   * Data for a transition from one animation state to another.\n   * See `transition()`\n   */\n  Transition = 1,\n  /**\n   * Contains a set of animation steps.\n   * See `sequence()`\n   */\n  Sequence = 2,\n  /**\n   * Contains a set of animation steps.\n   * See `group()`\n   */\n  Group = 3,\n  /**\n   * Contains an animation step.\n   * See `animate()`\n   */\n  Animate = 4,\n  /**\n   * Contains a set of animation steps.\n   * See `keyframes()`\n   */\n  Keyframes = 5,\n  /**\n   * Contains a set of CSS property-value pairs into a named style.\n   * See `style()`\n   */\n  Style = 6,\n  /**\n   * Associates an animation with an entry trigger that can be attached to an element.\n   * See `trigger()`\n   */\n  Trigger = 7,\n  /**\n   * Contains a re-usable animation.\n   * See `animation()`\n   */\n  Reference = 8,\n  /**\n   * Contains data to use in executing child animations returned by a query.\n   * See `animateChild()`\n   */\n  AnimateChild = 9,\n  /**\n   * Contains animation parameters for a re-usable animation.\n   * See `useAnimation()`\n   */\n  AnimateRef = 10,\n  /**\n   * Contains child-animation query data.\n   * See `query()`\n   */\n  Query = 11,\n  /**\n   * Contains data for staggering an animation sequence.\n   * See `stagger()`\n   */\n  Stagger = 12,\n}\n\n/**\n * Specifies automatic styling.\n *\n * @publicApi\n */\nexport const AUTO_STYLE = '*';\n\n/**\n * Base for animation data structures.\n *\n * @publicApi\n */\nexport interface AnimationMetadata {\n  type: AnimationMetadataType;\n}\n\n/**\n * Contains an animation trigger. Instantiated and returned by the\n * `trigger()` function.\n *\n * @publicApi\n */\nexport interface AnimationTriggerMetadata extends AnimationMetadata {\n  /**\n   * The trigger name, used to associate it with an element. Unique within the component.\n   */\n  name: string;\n  /**\n   * An animation definition object, containing an array of state and transition declarations.\n   */\n  definitions: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: {params?: {[name: string]: any}} | null;\n}\n\n/**\n * Encapsulates an animation state by associating a state name with a set of CSS styles.\n * Instantiated and returned by the [`state()`](api/animations/state) function.\n *\n * @publicApi\n */\nexport interface AnimationStateMetadata extends AnimationMetadata {\n  /**\n   * The state name, unique within the component.\n   */\n  name: string;\n  /**\n   *  The CSS styles associated with this state.\n   */\n  styles: AnimationStyleMetadata;\n  /**\n   * An options object containing\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation.\n   */\n  options?: {params: {[name: string]: any}};\n}\n\n/**\n * Encapsulates an animation transition. Instantiated and returned by the\n * `transition()` function.\n *\n * @publicApi\n */\nexport interface AnimationTransitionMetadata extends AnimationMetadata {\n  /**\n   * An expression that describes a state change.\n   */\n  expr:\n    | string\n    | ((\n        fromState: string,\n        toState: string,\n        element?: any,\n        params?: {[key: string]: any},\n      ) => boolean);\n  /**\n   * One or more animation objects to which this transition applies.\n   */\n  animation: AnimationMetadata | AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates a reusable animation, which is a collection of individual animation steps.\n * Instantiated and returned by the `animation()` function, and\n * passed to the `useAnimation()` function.\n *\n * @publicApi\n */\nexport interface AnimationReferenceMetadata extends AnimationMetadata {\n  /**\n   *  One or more animation step objects.\n   */\n  animation: AnimationMetadata | AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates an animation query. Instantiated and returned by\n * the `query()` function.\n *\n * @publicApi\n */\nexport interface AnimationQueryMetadata extends AnimationMetadata {\n  /**\n   *  The CSS selector for this query.\n   */\n  selector: string;\n  /**\n   * One or more animation step objects.\n   */\n  animation: AnimationMetadata | AnimationMetadata[];\n  /**\n   * A query options object.\n   */\n  options: AnimationQueryOptions | null;\n}\n\n/**\n * Encapsulates a keyframes sequence. Instantiated and returned by\n * the `keyframes()` function.\n *\n * @publicApi\n */\nexport interface AnimationKeyframesSequenceMetadata extends AnimationMetadata {\n  /**\n   * An array of animation styles.\n   */\n  steps: AnimationStyleMetadata[];\n}\n\n/**\n * Encapsulates an animation style. Instantiated and returned by\n * the `style()` function.\n *\n * @publicApi\n */\nexport interface AnimationStyleMetadata extends AnimationMetadata {\n  /**\n   * A set of CSS style properties.\n   */\n  styles: '*' | {[key: string]: string | number} | Array<{[key: string]: string | number} | '*'>;\n  /**\n   * A percentage of the total animate time at which the style is to be applied.\n   */\n  offset: number | null;\n}\n\n/**\n * Encapsulates an animation step. Instantiated and returned by\n * the `animate()` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateMetadata extends AnimationMetadata {\n  /**\n   * The timing data for the step.\n   */\n  timings: string | number | AnimateTimings;\n  /**\n   * A set of styles used in the step.\n   */\n  styles: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata | null;\n}\n\n/**\n * Encapsulates a child animation, that can be run explicitly when the parent is run.\n * Instantiated and returned by the `animateChild` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateChildMetadata extends AnimationMetadata {\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates a reusable animation.\n * Instantiated and returned by the `useAnimation()` function.\n *\n * @publicApi\n */\nexport interface AnimationAnimateRefMetadata extends AnimationMetadata {\n  /**\n   * An animation reference object.\n   */\n  animation: AnimationReferenceMetadata;\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates an animation sequence.\n * Instantiated and returned by the `sequence()` function.\n *\n * @publicApi\n */\nexport interface AnimationSequenceMetadata extends AnimationMetadata {\n  /**\n   *  An array of animation step objects.\n   */\n  steps: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates an animation group.\n * Instantiated and returned by the `group()` function.\n *\n * @publicApi\n */\nexport interface AnimationGroupMetadata extends AnimationMetadata {\n  /**\n   * One or more animation or style steps that form this group.\n   */\n  steps: AnimationMetadata[];\n  /**\n   * An options object containing a delay and\n   * developer-defined parameters that provide styling defaults and\n   * can be overridden on invocation. Default delay is 0.\n   */\n  options: AnimationOptions | null;\n}\n\n/**\n * Encapsulates animation query options.\n * Passed to the `query()` function.\n *\n * @publicApi\n */\nexport declare interface AnimationQueryOptions extends AnimationOptions {\n  /**\n   * True if this query is optional, false if it is required. Default is false.\n   * A required query throws an error if no elements are retrieved when\n   * the query is executed. An optional query does not.\n   *\n   */\n  optional?: boolean;\n  /**\n   * A maximum total number of results to return from the query.\n   * If negative, results are limited from the end of the query list towards the beginning.\n   * By default, results are not limited.\n   */\n  limit?: number;\n}\n\n/**\n * Encapsulates parameters for staggering the start times of a set of animation steps.\n * Instantiated and returned by the `stagger()` function.\n *\n * @publicApi\n **/\nexport interface AnimationStaggerMetadata extends AnimationMetadata {\n  /**\n   * The timing data for the steps.\n   */\n  timings: string | number;\n  /**\n   * One or more animation steps.\n   */\n  animation: AnimationMetadata | AnimationMetadata[];\n}\n\n/**\n * Creates a named animation trigger, containing a  list of [`state()`](api/animations/state)\n * and `transition()` entries to be evaluated when the expression\n * bound to the trigger changes.\n *\n * @param name An identifying string.\n * @param definitions  An animation definition object, containing an array of\n * [`state()`](api/animations/state) and `transition()` declarations.\n *\n * @return An object that encapsulates the trigger data.\n *\n * @usageNotes\n * Define an animation trigger in the `animations` section of `@Component` metadata.\n * In the template, reference the trigger by name and bind it to a trigger expression that\n * evaluates to a defined animation state, using the following format:\n *\n * `[@triggerName]=\"expression\"`\n *\n * Animation trigger bindings convert all values to strings, and then match the\n * previous and current values against any linked transitions.\n * Booleans can be specified as `1` or `true` and `0` or `false`.\n *\n * ### Usage Example\n *\n * The following example creates an animation trigger reference based on the provided\n * name value.\n * The provided animation value is expected to be an array consisting of state and\n * transition declarations.\n *\n * ```ts\n * @Component({\n *   selector: \"my-component\",\n *   templateUrl: \"my-component-tpl.html\",\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component makes use of the defined trigger\n * by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ### Using an inline function\n * The `transition` animation method also supports reading an inline function which can decide\n * if its associated animation should be run.\n *\n * ```ts\n * // this method is run each time the `myAnimationTrigger` trigger value changes.\n * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n string]: any}): boolean {\n *   // notice that `element` and `params` are also available here\n *   return toState == 'yes-please-animate';\n * }\n *\n * @Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger('myAnimationTrigger', [\n *       transition(myInlineMatcherFn, [\n *         // the animation sequence code\n *       ]),\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"yes-please-animate\";\n * }\n * ```\n *\n * ### Disabling Animations\n * When true, the special animation control binding `@.disabled` binding prevents\n * all animations from rendering.\n * Place the  `@.disabled` binding on an element to disable\n * animations on the element itself, as well as any inner animation triggers\n * within the element.\n *\n * The following example shows how to use this feature:\n *\n * ```angular-ts\n * @Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [@.disabled]=\"isDisabled\">\n *       <div [@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n * along with any inner animations.\n *\n * ### Disable animations application-wide\n * When an area of the template is set to have animations disabled,\n * **all** inner components have their animations disabled as well.\n * This means that you can disable all animations for an app\n * by placing a host binding set on `@.disabled` on the topmost Angular component.\n *\n * ```ts\n * import {Component, HostBinding} from '@angular/core';\n *\n * @Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   @HostBinding('@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### Overriding disablement of inner animations\n * Despite inner animations being disabled, a parent animation can `query()`\n * for inner elements located in disabled areas of the template and still animate\n * them if needed. This is also the case for when a sub animation is\n * queried by a parent and then later animated using `animateChild()`.\n *\n * ### Detecting when an animation is disabled\n * If a region of the DOM (or the entire application) has its animations disabled, the animation\n * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n * an instance of an `AnimationEvent`. If animations are disabled,\n * the `.disabled` flag on the event is true.\n *\n * @publicApi\n */\nexport function trigger(name: string, definitions: AnimationMetadata[]): AnimationTriggerMetadata {\n  return {type: AnimationMetadataType.Trigger, name, definitions, options: {}};\n}\n\n/**\n * Defines an animation step that combines styling information with timing information.\n *\n * @param timings Sets `AnimateTimings` for the parent animation.\n * A string in the format \"duration [delay] [easing]\".\n *  - Duration and delay are expressed as a number and optional time unit,\n * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n * The default unit is milliseconds.\n *  - The easing value controls how the animation accelerates and decelerates\n * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n * `ease-in-out`, or a `cubic-bezier()` function call.\n * If not supplied, no easing is applied.\n *\n * For example, the string \"1s 100ms ease-out\" specifies a duration of\n * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n * which decelerates near the end of the duration.\n * @param styles Sets AnimationStyles for the parent animation.\n * A function call to either `style()` or `keyframes()`\n * that returns a collection of CSS style entries to be applied to the parent animation.\n * When null, uses the styles from the destination state.\n * This is useful when describing an animation step that will complete an animation;\n * see \"Animating to the final state\" in `transitions()`.\n * @returns An object that encapsulates the animation step.\n *\n * @usageNotes\n * Call within an animation `sequence()`, {@link /api/animations/group group()}, or\n * `transition()` call to specify an animation step\n * that applies given style data to the parent animation for a given amount of time.\n *\n * ### Syntax Examples\n * **Timing examples**\n *\n * The following examples show various `timings` specifications.\n * - `animate(500)` : Duration is 500 milliseconds.\n * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n * milliseconds, easing according to a bezier curve.\n *\n * **Style examples**\n *\n * The following example calls `style()` to set a single CSS style.\n * ```ts\n * animate(500, style({ background: \"red\" }))\n * ```\n * The following example calls `keyframes()` to set a CSS style\n * to different values for successive keyframes.\n * ```ts\n * animate(500, keyframes(\n *  [\n *   style({ background: \"blue\" }),\n *   style({ background: \"red\" })\n *  ])\n * ```\n *\n * @publicApi\n */\nexport function animate(\n  timings: string | number,\n  styles: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata | null = null,\n): AnimationAnimateMetadata {\n  return {type: AnimationMetadataType.Animate, styles, timings};\n}\n\n/**\n * @description Defines a list of animation steps to be run in parallel.\n *\n * @param steps An array of animation step objects.\n * - When steps are defined by `style()` or `animate()`\n * function calls, each call within the group is executed instantly.\n * - To specify offset styles to be applied at a later time, define steps with\n * `keyframes()`, or use `animate()` calls with a delay value.\n * For example:\n *\n * ```ts\n * group([\n *   animate(\"1s\", style({ background: \"black\" })),\n *   animate(\"2s\", style({ color: \"white\" }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the group data.\n *\n * @usageNotes\n * Grouped animations are useful when a series of styles must be\n * animated at different starting times and closed off at different ending times.\n *\n * When called within a `sequence()` or a\n * `transition()` call, does not continue to the next\n * instruction until all of the inner animation steps have completed.\n *\n * @publicApi\n */\nexport function group(\n  steps: AnimationMetadata[],\n  options: AnimationOptions | null = null,\n): AnimationGroupMetadata {\n  return {type: AnimationMetadataType.Group, steps, options};\n}\n\n/**\n * Defines a list of animation steps to be run sequentially, one by one.\n *\n * @param steps An array of animation step objects.\n * - Steps defined by `style()` calls apply the styling data immediately.\n * - Steps defined by `animate()` calls apply the styling data over time\n *   as specified by the timing data.\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(\"1s\", style({ opacity: 1 }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the sequence data.\n *\n * @usageNotes\n * When you pass an array of steps to a\n * `transition()` call, the steps run sequentially by default.\n * Compare this to the  {@link /api/animations/group group()} call, which runs animation steps in\n *parallel.\n *\n * When a sequence is used within a  {@link /api/animations/group group()} or a `transition()` call,\n * execution continues to the next instruction only after each of the inner animation\n * steps have completed.\n *\n * @publicApi\n **/\nexport function sequence(\n  steps: AnimationMetadata[],\n  options: AnimationOptions | null = null,\n): AnimationSequenceMetadata {\n  return {type: AnimationMetadataType.Sequence, steps, options};\n}\n\n/**\n * Declares a key/value object containing CSS properties/styles that\n * can then be used for an animation [`state`](api/animations/state), within an animation\n *`sequence`, or as styling data for calls to `animate()` and `keyframes()`.\n *\n * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n * The value can be any of the following:\n * - A key-value style pair associating a CSS property with a value.\n * - An array of key-value style pairs.\n * - An asterisk (*), to use auto-styling, where styles are derived from the element\n * being animated and applied to the animation when it starts.\n *\n * Auto-styling can be used to define a state that depends on layout or other\n * environmental factors.\n *\n * @return An object that encapsulates the style data.\n *\n * @usageNotes\n * The following examples create animation styles that collect a set of\n * CSS property values:\n *\n * ```ts\n * // string values for CSS properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical pixel values\n * style({ width: 100, height: 0 })\n * ```\n *\n * The following example uses auto-styling to allow an element to animate from\n * a height of 0 up to its full height:\n *\n * ```ts\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * @publicApi\n **/\nexport function style(\n  tokens: '*' | {[key: string]: string | number} | Array<'*' | {[key: string]: string | number}>,\n): AnimationStyleMetadata {\n  return {type: AnimationMetadataType.Style, styles: tokens, offset: null};\n}\n\n/**\n * Declares an animation state within a trigger attached to an element.\n *\n * @param name One or more names for the defined state in a comma-separated string.\n * The following reserved state names can be supplied to define a style for specific use\n * cases:\n *\n * - `void` You can associate styles with this name to be used when\n * the element is detached from the application. For example, when an `ngIf` evaluates\n * to false, the state of the associated element is void.\n *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n * to be used as the fallback when the state that is being animated is not declared\n * within the trigger.\n *\n * @param styles A set of CSS styles associated with this state, created using the\n * `style()` function.\n * This set of styles persists on the element once the state has been reached.\n * @param options Parameters that can be passed to the state when it is invoked.\n * 0 or more key-value pairs.\n * @return An object that encapsulates the new state data.\n *\n * @usageNotes\n * Use the `trigger()` function to register states to an animation trigger.\n * Use the `transition()` function to animate between states.\n * When a state is active within a component, its associated styles persist on the element,\n * even when the animation ends.\n *\n * @publicApi\n **/\nexport function state(\n  name: string,\n  styles: AnimationStyleMetadata,\n  options?: {params: {[name: string]: any}},\n): AnimationStateMetadata {\n  return {type: AnimationMetadataType.State, name, styles, options};\n}\n\n/**\n * Defines a set of animation styles, associating each style with an optional `offset` value.\n *\n * @param steps A set of animation styles with optional offset data.\n * The optional `offset` value for a style specifies a percentage of the total animation\n * time at which that style is applied.\n * @returns An object that encapsulates the keyframes data.\n *\n * @usageNotes\n * Use with the `animate()` call. Instead of applying animations\n * from the current state\n * to the destination state, keyframes describe how each style entry is applied and at what point\n * within the animation arc.\n * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n *\n * ### Usage\n *\n * In the following example, the offset values describe\n * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n * blue when 20% of the total time has elapsed.\n *\n * ```ts\n * // the provided offset values\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * If there are no `offset` values specified in the style entries, the offsets\n * are calculated automatically.\n *\n * ```ts\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n *```\n\n * @publicApi\n */\nexport function keyframes(steps: AnimationStyleMetadata[]): AnimationKeyframesSequenceMetadata {\n  return {type: AnimationMetadataType.Keyframes, steps};\n}\n\n/**\n * Declares an animation transition which is played when a certain specified condition is met.\n *\n * @param stateChangeExpr A string with a specific format or a function that specifies when the\n * animation transition should occur (see [State Change Expression](#state-change-expression)).\n *\n * @param steps One or more animation objects that represent the animation's instructions.\n *\n * @param options An options object that can be used to specify a delay for the animation or provide\n * custom parameters for it.\n *\n * @returns An object that encapsulates the transition data.\n *\n * @usageNotes\n *\n * ### State Change Expression\n *\n * The State Change Expression instructs Angular when to run the transition's animations, it can\n *either be\n *  - a string with a specific syntax\n *  - or a function that compares the previous and current state (value of the expression bound to\n *    the element's trigger) and returns `true` if the transition should occur or `false` otherwise\n *\n * The string format can be:\n *  - `fromState => toState`, which indicates that the transition's animations should occur then the\n *    expression bound to the trigger's element goes from `fromState` to `toState`\n *\n *    _Example:_\n *      ```ts\n *        transition('open => closed', animate('.5s ease-out', style({ height: 0 }) ))\n *      ```\n *\n *  - `fromState <=> toState`, which indicates that the transition's animations should occur then\n *    the expression bound to the trigger's element goes from `fromState` to `toState` or vice versa\n *\n *    _Example:_\n *      ```ts\n *        transition('enabled <=> disabled', animate('1s cubic-bezier(0.8,0.3,0,1)'))\n *      ```\n *\n *  - `:enter`/`:leave`, which indicates that the transition's animations should occur when the\n *    element enters or exists the DOM\n *\n *    _Example:_\n *      ```ts\n *        transition(':enter', [\n *          style({ opacity: 0 }),\n *          animate('500ms', style({ opacity: 1 }))\n *        ])\n *      ```\n *\n *  - `:increment`/`:decrement`, which indicates that the transition's animations should occur when\n *    the numerical expression bound to the trigger's element has increased in value or decreased\n *\n *    _Example:_\n *      ```ts\n *        transition(':increment', query('@counter', animateChild()))\n *      ```\n *\n *  - a sequence of any of the above divided by commas, which indicates that transition's animations\n *    should occur whenever one of the state change expressions matches\n *\n *    _Example:_\n *      ```ts\n *        transition(':increment, * => enabled, :enter', animate('1s ease', keyframes([\n *          style({ transform: 'scale(1)', offset: 0}),\n *          style({ transform: 'scale(1.1)', offset: 0.7}),\n *          style({ transform: 'scale(1)', offset: 1})\n *        ]))),\n *      ```\n *\n * Also note that in such context:\n *  - `void` can be used to indicate the absence of the element\n *  - asterisks can be used as wildcards that match any state\n *  - (as a consequence of the above, `void => *` is equivalent to `:enter` and `* => void` is\n *    equivalent to `:leave`)\n *  - `true` and `false` also match expression values of `1` and `0` respectively (but do not match\n *    _truthy_ and _falsy_ values)\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n *  Be careful about entering end leaving elements as their transitions present a common\n *  pitfall for developers.\n *\n *  Note that when an element with a trigger enters the DOM its `:enter` transition always\n *  gets executed, but its `:leave` transition will not be executed if the element is removed\n *  alongside its parent (as it will be removed \"without warning\" before its transition has\n *  a chance to be executed, the only way that such transition can occur is if the element\n *  is exiting the DOM on its own).\n *\n *\n * </div>\n *\n * ### Animating to a Final State\n *\n * If the final step in a transition is a call to `animate()` that uses a timing value\n * with no `style` data, that step is automatically considered the final animation arc,\n * for the element to reach the final state, in such case Angular automatically adds or removes\n * CSS styles to ensure that the element is in the correct final state.\n *\n *\n * ### Usage Examples\n *\n *  - Transition animations applied based on\n *    the trigger's expression value\n *\n *   ```html\n *   <div [@myAnimationTrigger]=\"myStatusExp\">\n *    ...\n *   </div>\n *   ```\n *\n *   ```ts\n *   trigger(\"myAnimationTrigger\", [\n *     ..., // states\n *     transition(\"on => off, open => closed\", animate(500)),\n *     transition(\"* <=> error\", query('.indicator', animateChild()))\n *   ])\n *   ```\n *\n *  - Transition animations applied based on custom logic dependent\n *    on the trigger's expression value and provided parameters\n *\n *    ```html\n *    <div [@myAnimationTrigger]=\"{\n *     value: stepName,\n *     params: { target: currentTarget }\n *    }\">\n *     ...\n *    </div>\n *    ```\n *\n *    ```ts\n *    trigger(\"myAnimationTrigger\", [\n *      ..., // states\n *      transition(\n *        (fromState, toState, _element, params) =>\n *          ['firststep', 'laststep'].includes(fromState.toLowerCase())\n *          && toState === params?.['target'],\n *        animate('1s')\n *      )\n *    ])\n *    ```\n *\n * @publicApi\n **/\nexport function transition(\n  stateChangeExpr:\n    | string\n    | ((\n        fromState: string,\n        toState: string,\n        element?: any,\n        params?: {[key: string]: any},\n      ) => boolean),\n  steps: AnimationMetadata | AnimationMetadata[],\n  options: AnimationOptions | null = null,\n): AnimationTransitionMetadata {\n  return {type: AnimationMetadataType.Transition, expr: stateChangeExpr, animation: steps, options};\n}\n\n/**\n * Produces a reusable animation that can be invoked in another animation or sequence,\n * by calling the `useAnimation()` function.\n *\n * @param steps One or more animation objects, as returned by the `animate()`\n * or `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional developer-defined parameters.\n * Provided values for additional parameters are used as defaults,\n * and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the animation data.\n *\n * @usageNotes\n * The following example defines a reusable animation, providing some default parameter\n * values.\n *\n * ```ts\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *   style({ opacity: '{{ end }}'}))\n *   ],\n *   { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * The following invokes the defined animation with a call to `useAnimation()`,\n * passing in override parameter values.\n *\n * ```js\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If any of the passed-in parameter values are missing from this call,\n * the default values are used. If one or more parameter values are missing before a step is\n * animated, `useAnimation()` throws an error.\n *\n * @publicApi\n */\nexport function animation(\n  steps: AnimationMetadata | AnimationMetadata[],\n  options: AnimationOptions | null = null,\n): AnimationReferenceMetadata {\n  return {type: AnimationMetadataType.Reference, animation: steps, options};\n}\n\n/**\n * Executes a queried inner animation element within an animation sequence.\n *\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional override values for developer-defined parameters.\n * @return An object that encapsulates the child animation data.\n *\n * @usageNotes\n * Each time an animation is triggered in Angular, the parent animation\n * has priority and any child animations are blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations, and run them using this function.\n *\n * Note that this feature is designed to be used with `query()` and it will only work\n * with animations that are assigned using the Angular animation library. CSS keyframes\n * and transitions are not handled by this API.\n *\n * @publicApi\n */\nexport function animateChild(\n  options: AnimateChildOptions | null = null,\n): AnimationAnimateChildMetadata {\n  return {type: AnimationMetadataType.AnimateChild, options};\n}\n\n/**\n * Starts a reusable animation that is created using the `animation()` function.\n *\n * @param animation The reusable animation to start.\n * @param options An options object that can contain a delay value for the start of\n * the animation, and additional override values for developer-defined parameters.\n * @return An object that contains the animation parameters.\n *\n * @publicApi\n */\nexport function useAnimation(\n  animation: AnimationReferenceMetadata,\n  options: AnimationOptions | null = null,\n): AnimationAnimateRefMetadata {\n  return {type: AnimationMetadataType.AnimateRef, animation, options};\n}\n\n/**\n * Finds one or more inner elements within the current element that is\n * being animated within a sequence. Use with `animate()`.\n *\n * @param selector The element to query, or a set of elements that contain Angular-specific\n * characteristics, specified with one or more of the following tokens.\n *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements (not\n *     all elements can be queried via these tokens, see\n *     [Entering and Leaving Elements](#entering-and-leaving-elements))\n *  - `query(\":animating\")` : Query all currently animating elements.\n *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n *  - `query(\":self\")` : Include the current element into the animation sequence.\n *\n * @param animation One or more animation steps to apply to the queried element or elements.\n * An array is treated as an animation sequence.\n * @param options An options object. Use the 'limit' field to limit the total number of\n * items to collect.\n * @return An object that encapsulates the query data.\n *\n * @usageNotes\n *\n * ### Multiple Tokens\n *\n * Tokens can be merged into a combined query selector string. For example:\n *\n * ```ts\n *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n * ```\n *\n * The `query()` function collects multiple elements and works internally by using\n * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n * the total number of items to be collected. For example:\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * By default, throws an error when zero items are found. Set the\n * `optional` flag to ignore this error. For example:\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Entering and Leaving Elements\n *\n * Not all elements can be queried via the `:enter` and `:leave` tokens, the only ones\n * that can are those that Angular assumes can enter/leave based on their own logic\n * (if their insertion/removal is simply a consequence of that of their parent they\n * should be queried via a different token in their parent's `:enter`/`:leave` transitions).\n *\n * The only elements Angular assumes can enter/leave based on their own logic (thus the only\n * ones that can be queried via the `:enter` and `:leave` tokens) are:\n *  - Those inserted dynamically (via `ViewContainerRef`)\n *  - Those that have a structural directive (which, under the hood, are a subset of the above ones)\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n *  Note that elements will be successfully queried via `:enter`/`:leave` even if their\n *  insertion/removal is not done manually via `ViewContainerRef`or caused by their structural\n *  directive (e.g. they enter/exit alongside their parent).\n *\n * </div>\n *\n * <div class=\"docs-alert docs-alert-important\">\n *\n *  There is an exception to what previously mentioned, besides elements entering/leaving based on\n *  their own logic, elements with an animation trigger can always be queried via `:leave` when\n * their parent is also leaving.\n *\n * </div>\n *\n * ### Usage Example\n *\n * The following example queries for inner elements and animates them\n * individually using `animate()`.\n *\n * ```angular-ts\n * @Component({\n *   selector: 'inner',\n *   template: `\n *     <div [@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 }))),\n *        query('.content', animate(1000, style({ opacity: 1 }))),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport function query(\n  selector: string,\n  animation: AnimationMetadata | AnimationMetadata[],\n  options: AnimationQueryOptions | null = null,\n): AnimationQueryMetadata {\n  return {type: AnimationMetadataType.Query, selector, animation, options};\n}\n\n/**\n * Use within an animation `query()` call to issue a timing gap after\n * each queried item is animated.\n *\n * @param timings A delay value.\n * @param animation One ore more animation steps.\n * @returns An object that encapsulates the stagger data.\n *\n * @usageNotes\n * In the following example, a container element wraps a list of items stamped out\n * by an `ngFor`. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * Each time items are added, the opacity fade-in animation runs,\n * and each removed item is faded out.\n * When either of these animations occur, the stagger effect is\n * applied after each item's animation is started.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n *\n * Here is the component code:\n *\n * ```ts\n * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n * @Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *     ...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *    }\n *  }\n * ```\n *\n * Here is the animation trigger code:\n *\n * ```ts\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * @publicApi\n */\nexport function stagger(\n  timings: string | number,\n  animation: AnimationMetadata | AnimationMetadata[],\n): AnimationStaggerMetadata {\n  return {type: AnimationMetadataType.Stagger, timings, animation};\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Provides programmatic control of a reusable animation sequence,\n * built using the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n * method which returns an `AnimationFactory`, whose\n * <code>[create](api/animations/AnimationFactory#create)()</code> method instantiates and\n * initializes this interface.\n *\n * @see {@link AnimationBuilder}\n * @see {@link AnimationFactory}\n * @see {@link animate}\n *\n * @publicApi\n */\nexport interface AnimationPlayer {\n  /**\n   * Provides a callback to invoke when the animation finishes.\n   * @param fn The callback function.\n   * @see {@link #finish}\n   */\n  onDone(fn: () => void): void;\n  /**\n   * Provides a callback to invoke when the animation starts.\n   * @param fn The callback function.\n   * @see {@link #play}\n   */\n  onStart(fn: () => void): void;\n  /**\n   * Provides a callback to invoke after the animation is destroyed.\n   * @param fn The callback function.\n   * @see {@link #destroy}\n   * @see {@link #beforeDestroy}\n   */\n  onDestroy(fn: () => void): void;\n  /**\n   * Initializes the animation.\n   */\n  init(): void;\n  /**\n   * Reports whether the animation has started.\n   * @returns True if the animation has started, false otherwise.\n   */\n  hasStarted(): boolean;\n  /**\n   * Runs the animation, invoking the `onStart()` callback.\n   */\n  play(): void;\n  /**\n   * Pauses the animation.\n   */\n  pause(): void;\n  /**\n   * Restarts the paused animation.\n   */\n  restart(): void;\n  /**\n   * Ends the animation, invoking the `onDone()` callback.\n   */\n  finish(): void;\n  /**\n   * Destroys the animation, after invoking the `beforeDestroy()` callback.\n   * Calls the `onDestroy()` callback when destruction is completed.\n   */\n  destroy(): void;\n  /**\n   * Resets the animation to its initial state.\n   */\n  reset(): void;\n  /**\n   * Sets the position of the animation.\n   * @param position A fractional value, representing the progress through the animation.\n   */\n  setPosition(position: number): void;\n  /**\n   * Reports the current position of the animation.\n   * @returns A fractional value, representing the progress through the animation.\n   */\n  getPosition(): number;\n  /**\n   * The parent of this player, if any.\n   */\n  parentPlayer: AnimationPlayer | null;\n  /**\n   * The total run time of the animation, in milliseconds.\n   */\n  readonly totalTime: number;\n  /**\n   * Provides a callback to invoke before the animation is destroyed.\n   */\n  beforeDestroy?: () => any;\n  /**\n   * @internal\n   * Internal\n   */\n  triggerCallback?: (phaseName: string) => void;\n  /**\n   * @internal\n   * Internal\n   */\n  disabled?: boolean;\n}\n\n/**\n * An empty programmatic controller for reusable animations.\n * Used internally when animations are disabled, to avoid\n * checking for the null case when an animation player is expected.\n *\n * @see {@link animate}\n * @see {@link AnimationPlayer}\n *\n * @publicApi\n */\nexport class NoopAnimationPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n  private _originalOnDoneFns: Function[] = [];\n  private _originalOnStartFns: Function[] = [];\n  private _started = false;\n  private _destroyed = false;\n  private _finished = false;\n  private _position = 0;\n  public parentPlayer: AnimationPlayer | null = null;\n  public readonly totalTime: number;\n  constructor(duration: number = 0, delay: number = 0) {\n    this.totalTime = duration + delay;\n  }\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach((fn) => fn());\n      this._onDoneFns = [];\n    }\n  }\n  onStart(fn: () => void): void {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn: () => void): void {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn: () => void): void {\n    this._onDestroyFns.push(fn);\n  }\n  hasStarted(): boolean {\n    return this._started;\n  }\n  init(): void {}\n  play(): void {\n    if (!this.hasStarted()) {\n      this._onStart();\n      this.triggerMicrotask();\n    }\n    this._started = true;\n  }\n\n  /** @internal */\n  triggerMicrotask() {\n    queueMicrotask(() => this._onFinish());\n  }\n\n  private _onStart() {\n    this._onStartFns.forEach((fn) => fn());\n    this._onStartFns = [];\n  }\n\n  pause(): void {}\n  restart(): void {}\n  finish(): void {\n    this._onFinish();\n  }\n  destroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      if (!this.hasStarted()) {\n        this._onStart();\n      }\n      this.finish();\n      this._onDestroyFns.forEach((fn) => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  reset(): void {\n    this._started = false;\n    this._finished = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  setPosition(position: number): void {\n    this._position = this.totalTime ? position * this.totalTime : 1;\n  }\n  getPosition(): number {\n    return this.totalTime ? this._position / this.totalTime : 1;\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach((fn) => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {AnimationPlayer} from './animation_player';\n\n/**\n * A programmatic controller for a group of reusable animations.\n * Used internally to control animations.\n *\n * @see {@link AnimationPlayer}\n * @see {@link animations/group group}\n *\n */\nexport class AnimationGroupPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _finished = false;\n  private _started = false;\n  private _destroyed = false;\n  private _onDestroyFns: Function[] = [];\n\n  public parentPlayer: AnimationPlayer | null = null;\n  public totalTime: number = 0;\n  public readonly players: AnimationPlayer[];\n\n  constructor(_players: AnimationPlayer[]) {\n    this.players = _players;\n    let doneCount = 0;\n    let destroyCount = 0;\n    let startCount = 0;\n    const total = this.players.length;\n\n    if (total == 0) {\n      queueMicrotask(() => this._onFinish());\n    } else {\n      this.players.forEach((player) => {\n        player.onDone(() => {\n          if (++doneCount == total) {\n            this._onFinish();\n          }\n        });\n        player.onDestroy(() => {\n          if (++destroyCount == total) {\n            this._onDestroy();\n          }\n        });\n        player.onStart(() => {\n          if (++startCount == total) {\n            this._onStart();\n          }\n        });\n      });\n    }\n\n    this.totalTime = this.players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n  }\n\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach((fn) => fn());\n      this._onDoneFns = [];\n    }\n  }\n\n  init(): void {\n    this.players.forEach((player) => player.init());\n  }\n\n  onStart(fn: () => void): void {\n    this._onStartFns.push(fn);\n  }\n\n  private _onStart() {\n    if (!this.hasStarted()) {\n      this._started = true;\n      this._onStartFns.forEach((fn) => fn());\n      this._onStartFns = [];\n    }\n  }\n\n  onDone(fn: () => void): void {\n    this._onDoneFns.push(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    this._onDestroyFns.push(fn);\n  }\n\n  hasStarted() {\n    return this._started;\n  }\n\n  play() {\n    if (!this.parentPlayer) {\n      this.init();\n    }\n    this._onStart();\n    this.players.forEach((player) => player.play());\n  }\n\n  pause(): void {\n    this.players.forEach((player) => player.pause());\n  }\n\n  restart(): void {\n    this.players.forEach((player) => player.restart());\n  }\n\n  finish(): void {\n    this._onFinish();\n    this.players.forEach((player) => player.finish());\n  }\n\n  destroy(): void {\n    this._onDestroy();\n  }\n\n  private _onDestroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._onFinish();\n      this.players.forEach((player) => player.destroy());\n      this._onDestroyFns.forEach((fn) => fn());\n      this._onDestroyFns = [];\n    }\n  }\n\n  reset(): void {\n    this.players.forEach((player) => player.reset());\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n\n  setPosition(p: number): void {\n    const timeAtPosition = p * this.totalTime;\n    this.players.forEach((player) => {\n      const position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n      player.setPosition(position);\n    });\n  }\n\n  getPosition(): number {\n    const longestPlayer = this.players.reduce(\n      (longestSoFar: AnimationPlayer | null, player: AnimationPlayer) => {\n        const newPlayerIsLongest =\n          longestSoFar === null || player.totalTime > longestSoFar.totalTime;\n        return newPlayerIsLongest ? player : longestSoFar;\n      },\n      null,\n    );\n    return longestPlayer != null ? longestPlayer.getPosition() : 0;\n  }\n\n  beforeDestroy(): void {\n    this.players.forEach((player) => {\n      if (player.beforeDestroy) {\n        player.beforeDestroy();\n      }\n    });\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach((fn) => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nexport {BrowserAnimationBuilder as ɵBrowserAnimationBuilder} from './animation_builder';\nexport {RuntimeErrorCode as ɵRuntimeErrorCode} from './errors';\nexport {AnimationGroupPlayer as ɵAnimationGroupPlayer} from './players/animation_group_player';\n\nexport const ɵPRE_STYLE = '!';\n"], "names": [], "mappings": ";;;;;;AA8FA;;;;;;;AAOG;IACS;AAAZ,CAAA,UAAY,qBAAqB,EAAA;AAC/B;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AACd;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,GAAA,YAAe;AACf;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAU;AACV;;;AAGG;AACH,IAAA,qBAAA,CAAA,qBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAY;AACd,CAAC,EAlEW,qBAAqB,KAArB,qBAAqB,GAkEhC,EAAA,CAAA,CAAA;AAED;;;;AAIG;AACI,MAAM,UAAU,GAAG;AA8R1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJG;AACa,SAAA,OAAO,CAAC,IAAY,EAAE,WAAgC,EAAA;AACpE,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAC;AAC9E;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDG;SACa,OAAO,CACrB,OAAwB,EACxB,SAA6E,IAAI,EAAA;IAEjF,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAC;AAC/D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;SACa,KAAK,CACnB,KAA0B,EAC1B,UAAmC,IAAI,EAAA;IAEvC,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAC;AAC5D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCI;SACY,QAAQ,CACtB,KAA0B,EAC1B,UAAmC,IAAI,EAAA;IAEvC,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAC;AAC/D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCI;AACE,SAAU,KAAK,CACnB,MAA8F,EAAA;AAE9F,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAC;AAC1E;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BI;SACY,KAAK,CACnB,IAAY,EACZ,MAA8B,EAC9B,OAAyC,EAAA;AAEzC,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAC;AACnE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CG;AACG,SAAU,SAAS,CAAC,KAA+B,EAAA;IACvD,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,KAAK,EAAC;AACvD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJI;AACE,SAAU,UAAU,CACxB,eAOiB,EACjB,KAA8C,EAC9C,UAAmC,IAAI,EAAA;AAEvC,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAC;AACnG;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CG;SACa,SAAS,CACvB,KAA8C,EAC9C,UAAmC,IAAI,EAAA;AAEvC,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAC;AAC3E;AAEA;;;;;;;;;;;;;;;;;;AAkBG;AACa,SAAA,YAAY,CAC1B,OAAA,GAAsC,IAAI,EAAA;IAE1C,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,YAAY,EAAE,OAAO,EAAC;AAC5D;AAEA;;;;;;;;;AASG;SACa,YAAY,CAC1B,SAAqC,EACrC,UAAmC,IAAI,EAAA;IAEvC,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,EAAC;AACrE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHG;AACG,SAAU,KAAK,CACnB,QAAgB,EAChB,SAAkD,EAClD,UAAwC,IAAI,EAAA;AAE5C,IAAA,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAC;AAC1E;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EG;AACa,SAAA,OAAO,CACrB,OAAwB,EACxB,SAAkD,EAAA;IAElD,OAAO,EAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAC;AAClE;;AChuCA;;;;;;;;;AASG;MACU,mBAAmB,CAAA;IACtB,UAAU,GAAe,EAAE;IAC3B,WAAW,GAAe,EAAE;IAC5B,aAAa,GAAe,EAAE;IAC9B,kBAAkB,GAAe,EAAE;IACnC,mBAAmB,GAAe,EAAE;IACpC,QAAQ,GAAG,KAAK;IAChB,UAAU,GAAG,KAAK;IAClB,SAAS,GAAG,KAAK;IACjB,SAAS,GAAG,CAAC;IACd,YAAY,GAA2B,IAAI;AAClC,IAAA,SAAS;AACzB,IAAA,WAAA,CAAY,QAAmB,GAAA,CAAC,EAAE,KAAA,GAAgB,CAAC,EAAA;AACjD,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,KAAK;;IAE3B,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;AAGxB,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;AAE3B,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;;AAE1B,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;;IAE7B,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ;;AAEtB,IAAA,IAAI;IACJ,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,gBAAgB,EAAE;;AAEzB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;IAItB,gBAAgB,GAAA;QACd,cAAc,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;;IAGhC,QAAQ,GAAA;AACd,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE;;AAGvB,IAAA,KAAK;AACL,IAAA,OAAO;IACP,MAAM,GAAA;QACJ,IAAI,CAAC,SAAS,EAAE;;IAElB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,QAAQ,EAAE;;YAEjB,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACxC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;IAG3B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB;;AAE3C,IAAA,WAAW,CAAC,QAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC;;IAEjE,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC;;;AAI7D,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;QACzE,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAC7B,QAAA,OAAO,CAAC,MAAM,GAAG,CAAC;;AAErB;;ACvMD;;;;;;;AAOG;MACU,oBAAoB,CAAA;IACvB,UAAU,GAAe,EAAE;IAC3B,WAAW,GAAe,EAAE;IAC5B,SAAS,GAAG,KAAK;IACjB,QAAQ,GAAG,KAAK;IAChB,UAAU,GAAG,KAAK;IAClB,aAAa,GAAe,EAAE;IAE/B,YAAY,GAA2B,IAAI;IAC3C,SAAS,GAAW,CAAC;AACZ,IAAA,OAAO;AAEvB,IAAA,WAAA,CAAY,QAA2B,EAAA;AACrC,QAAA,IAAI,CAAC,OAAO,GAAG,QAAQ;QACvB,IAAI,SAAS,GAAG,CAAC;QACjB,IAAI,YAAY,GAAG,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC;AAClB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;AAEjC,QAAA,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,cAAc,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;;aACjC;YACL,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC9B,gBAAA,MAAM,CAAC,MAAM,CAAC,MAAK;AACjB,oBAAA,IAAI,EAAE,SAAS,IAAI,KAAK,EAAE;wBACxB,IAAI,CAAC,SAAS,EAAE;;AAEpB,iBAAC,CAAC;AACF,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,oBAAA,IAAI,EAAE,YAAY,IAAI,KAAK,EAAE;wBAC3B,IAAI,CAAC,UAAU,EAAE;;AAErB,iBAAC,CAAC;AACF,gBAAA,MAAM,CAAC,OAAO,CAAC,MAAK;AAClB,oBAAA,IAAI,EAAE,UAAU,IAAI,KAAK,EAAE;wBACzB,IAAI,CAAC,QAAQ,EAAE;;AAEnB,iBAAC,CAAC;AACJ,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;;IAGrF,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;IAIxB,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;;AAGjD,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;IAGnB,QAAQ,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACtB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACtC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;;;AAIzB,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;;AAG1B,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;;IAG7B,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ;;IAGtB,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,IAAI,EAAE;;QAEb,IAAI,CAAC,QAAQ,EAAE;AACf,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;;IAGjD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC;;IAGlD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;;IAGpD,MAAM,GAAA;QACJ,IAAI,CAAC,SAAS,EAAE;AAChB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;;IAGnD,OAAO,GAAA;QACL,IAAI,CAAC,UAAU,EAAE;;IAGX,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;YACtB,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;AAClD,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACxC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;IAI3B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAGvB,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;YAC9B,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;AACtF,YAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC9B,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,YAAoC,EAAE,MAAuB,KAAI;AAChE,YAAA,MAAM,kBAAkB,GACtB,YAAY,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS;YACpE,OAAO,kBAAkB,GAAG,MAAM,GAAG,YAAY;SAClD,EACD,IAAI,CACL;AACD,QAAA,OAAO,aAAa,IAAI,IAAI,GAAG,aAAa,CAAC,WAAW,EAAE,GAAG,CAAC;;IAGhE,aAAa,GAAA;QACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC9B,YAAA,IAAI,MAAM,CAAC,aAAa,EAAE;gBACxB,MAAM,CAAC,aAAa,EAAE;;AAE1B,SAAC,CAAC;;;AAIJ,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,OAAO,GAAG,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;QACzE,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAC7B,QAAA,OAAO,CAAC,MAAM,GAAG,CAAC;;AAErB;;ACnKM,MAAM,UAAU,GAAG;;;;"}