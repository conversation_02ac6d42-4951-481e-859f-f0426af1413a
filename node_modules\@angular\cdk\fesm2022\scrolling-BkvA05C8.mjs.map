{"version": 3, "file": "scrolling-BkvA05C8.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/scrolling.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nexport enum RtlScrollAxisType {\n  /**\n   * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n   * all the way right.\n   */\n  NORMAL,\n  /**\n   * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  NEGATED,\n  /**\n   * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  INVERTED,\n}\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType: RtlScrollAxisType | undefined;\n\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported: boolean | undefined;\n\n/** Check whether the browser supports scroll behaviors. */\nexport function supportsScrollBehavior(): boolean {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement!.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction: Function | undefined = Element.prototype.scrollTo;\n\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n\n  return scrollBehaviorSupported;\n}\n\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nexport function getRtlScrollAxisType(): RtlScrollAxisType {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return RtlScrollAxisType.NORMAL;\n  }\n\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n\n    rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType =\n        scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n    }\n\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\n"], "names": [], "mappings": "AAQA;IACY;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP;;;AAGG;AACH,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACV,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,GAgB5B,EAAA,CAAA,CAAA;AAED;AACA,IAAI,iBAAgD;AAEpD;AACA,IAAI,uBAA4C;AAEhD;SACgB,sBAAsB,GAAA;AACpC,IAAA,IAAI,uBAAuB,IAAI,IAAI,EAAE;;;AAGnC,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,IAAI,OAAO,OAAO,KAAK,UAAU,IAAI,CAAC,OAAO,EAAE;YAC1F,uBAAuB,GAAG,KAAK;AAC/B,YAAA,OAAO,uBAAuB;;;QAIhC,IAAI,gBAAgB,IAAI,QAAQ,CAAC,eAAgB,CAAC,KAAK,EAAE;YACvD,uBAAuB,GAAG,IAAI;;aACzB;;;AAGL,YAAA,MAAM,gBAAgB,GAAyB,OAAO,CAAC,SAAS,CAAC,QAAQ;YAEzE,IAAI,gBAAgB,EAAE;;;;;gBAKpB,uBAAuB,GAAG,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;;iBACnF;gBACL,uBAAuB,GAAG,KAAK;;;;AAKrC,IAAA,OAAO,uBAAuB;AAChC;AAEA;;;AAGG;SACa,oBAAoB,GAAA;;IAElC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;QAC7C,OAAO,iBAAiB,CAAC,MAAM;;AAGjC,IAAA,IAAI,iBAAiB,IAAI,IAAI,EAAE;;QAE7B,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACrD,QAAA,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK;AAC5C,QAAA,eAAe,CAAC,GAAG,GAAG,KAAK;AAC3B,QAAA,cAAc,CAAC,KAAK,GAAG,KAAK;AAC5B,QAAA,cAAc,CAAC,QAAQ,GAAG,MAAM;AAChC,QAAA,cAAc,CAAC,UAAU,GAAG,QAAQ;AACpC,QAAA,cAAc,CAAC,aAAa,GAAG,MAAM;AACrC,QAAA,cAAc,CAAC,QAAQ,GAAG,UAAU;QAEpC,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC7C,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK;AAClC,QAAA,YAAY,CAAC,KAAK,GAAG,KAAK;AAC1B,QAAA,YAAY,CAAC,MAAM,GAAG,KAAK;AAE3B,QAAA,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;AAE1C,QAAA,iBAAiB,GAAG,iBAAiB,CAAC,MAAM;;;;AAK5C,QAAA,IAAI,eAAe,CAAC,UAAU,KAAK,CAAC,EAAE;;;;;AAKpC,YAAA,eAAe,CAAC,UAAU,GAAG,CAAC;YAC9B,iBAAiB;AACf,gBAAA,eAAe,CAAC,UAAU,KAAK,CAAC,GAAG,iBAAiB,CAAC,OAAO,GAAG,iBAAiB,CAAC,QAAQ;;QAG7F,eAAe,CAAC,MAAM,EAAE;;AAE1B,IAAA,OAAO,iBAAiB;AAC1B;;;;"}