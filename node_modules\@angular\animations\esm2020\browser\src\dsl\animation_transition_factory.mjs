import { getOrSetAsInMap } from '../render/shared';
import { copyObj, interpolateParams, iteratorToArray } from '../util';
import { buildAnimationTimelines } from './animation_timeline_builder';
import { createTransitionInstruction } from './animation_transition_instruction';
const EMPTY_OBJECT = {};
export class AnimationTransitionFactory {
    constructor(_triggerName, ast, _stateStyles) {
        this._triggerName = _triggerName;
        this.ast = ast;
        this._stateStyles = _stateStyles;
    }
    match(currentState, nextState, element, params) {
        return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);
    }
    buildStyles(stateName, params, errors) {
        const backupStateStyler = this._stateStyles['*'];
        const stateStyler = this._stateStyles[stateName];
        const backupStyles = backupStateStyler ? backupStateStyler.buildStyles(params, errors) : {};
        return stateStyler ? stateStyler.buildStyles(params, errors) : backupStyles;
    }
    build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {
        const errors = [];
        const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;
        const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;
        const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);
        const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;
        const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);
        const queriedElements = new Set();
        const preStyleMap = new Map();
        const postStyleMap = new Map();
        const isRemoval = nextState === 'void';
        const animationOptions = { params: { ...transitionAnimationParams, ...nextAnimationParams } };
        const timelines = skipAstBuild ?
            [] :
            buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);
        let totalTime = 0;
        timelines.forEach(tl => {
            totalTime = Math.max(tl.duration + tl.delay, totalTime);
        });
        if (errors.length) {
            return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);
        }
        timelines.forEach(tl => {
            const elm = tl.element;
            const preProps = getOrSetAsInMap(preStyleMap, elm, {});
            tl.preStyleProps.forEach(prop => preProps[prop] = true);
            const postProps = getOrSetAsInMap(postStyleMap, elm, {});
            tl.postStyleProps.forEach(prop => postProps[prop] = true);
            if (elm !== element) {
                queriedElements.add(elm);
            }
        });
        const queriedElementsList = iteratorToArray(queriedElements.values());
        return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap, totalTime);
    }
}
function oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {
    return matchFns.some(fn => fn(currentState, nextState, element, params));
}
export class AnimationStateStyles {
    constructor(styles, defaultParams, normalizer) {
        this.styles = styles;
        this.defaultParams = defaultParams;
        this.normalizer = normalizer;
    }
    buildStyles(params, errors) {
        const finalStyles = {};
        const combinedParams = copyObj(this.defaultParams);
        Object.keys(params).forEach(key => {
            const value = params[key];
            if (value != null) {
                combinedParams[key] = value;
            }
        });
        this.styles.styles.forEach(value => {
            if (typeof value !== 'string') {
                const styleObj = value;
                Object.keys(styleObj).forEach(prop => {
                    let val = styleObj[prop];
                    if (val.length > 1) {
                        val = interpolateParams(val, combinedParams, errors);
                    }
                    const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);
                    val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);
                    finalStyles[normalizedProp] = val;
                });
            }
        });
        return finalStyles;
    }
}
//# sourceMappingURL=data:application/json;base64,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