export { _ as _CdkPrivateStyleLoader } from '../style-loader.d-BXZfQZTF.js';
import * as i0 from '@angular/core';

/**
 * Component used to load the .cdk-visually-hidden styles.
 * @docs-private
 */
declare class _VisuallyHiddenLoader {
    static ɵfac: i0.ɵɵFactoryDeclaration<_VisuallyHiddenLoader, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<_VisuallyHiddenLoader, "ng-component", ["cdkVisuallyHidden"], {}, {}, never, never, true, never>;
}

export { _VisuallyHiddenLoader };
