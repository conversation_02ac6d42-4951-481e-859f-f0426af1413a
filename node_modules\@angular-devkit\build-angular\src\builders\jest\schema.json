{"$schema": "http://json-schema.org/draft-07/schema", "title": "Jest browser schema for Build Facade.", "description": "Jest target options", "type": "object", "properties": {"include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "polyfills": {"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false, "required": ["tsConfig"]}