import { allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes } from '../../util';
import { containsElement, hypenatePropsObject, invokeQuery, validateStyleProperty } from '../shared';
import { packageNonAnimatableStyles } from '../special_cased_styles';
import { CssKeyframesPlayer } from './css_keyframes_player';
import { DirectStylePlayer } from './direct_style_player';
const KEYFRAMES_NAME_PREFIX = 'gen_css_kf_';
const TAB_SPACE = ' ';
export class CssKeyframesDriver {
    constructor() {
        this._count = 0;
    }
    validateStyleProperty(prop) {
        return validateStyleProperty(prop);
    }
    matchesElement(_element, _selector) {
        // This method is deprecated and no longer in use so we return false.
        return false;
    }
    containsElement(elm1, elm2) {
        return containsElement(elm1, elm2);
    }
    query(element, selector, multi) {
        return invokeQuery(element, selector, multi);
    }
    computeStyle(element, prop, defaultValue) {
        return window.getComputedStyle(element)[prop];
    }
    buildKeyframeElement(element, name, keyframes) {
        keyframes = keyframes.map(kf => hypenatePropsObject(kf));
        let keyframeStr = `@keyframes ${name} {\n`;
        let tab = '';
        keyframes.forEach(kf => {
            tab = TAB_SPACE;
            const offset = parseFloat(kf['offset']);
            keyframeStr += `${tab}${offset * 100}% {\n`;
            tab += TAB_SPACE;
            Object.keys(kf).forEach(prop => {
                const value = kf[prop];
                switch (prop) {
                    case 'offset':
                        return;
                    case 'easing':
                        if (value) {
                            keyframeStr += `${tab}animation-timing-function: ${value};\n`;
                        }
                        return;
                    default:
                        keyframeStr += `${tab}${prop}: ${value};\n`;
                        return;
                }
            });
            keyframeStr += `${tab}}\n`;
        });
        keyframeStr += `}\n`;
        const kfElm = document.createElement('style');
        kfElm.textContent = keyframeStr;
        return kfElm;
    }
    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {
        if ((typeof ngDevMode === 'undefined' || ngDevMode) && scrubberAccessRequested) {
            notifyFaultyScrubber();
        }
        const previousCssKeyframePlayers = previousPlayers.filter(player => player instanceof CssKeyframesPlayer);
        const previousStyles = {};
        if (allowPreviousPlayerStylesMerge(duration, delay)) {
            previousCssKeyframePlayers.forEach(player => {
                let styles = player.currentSnapshot;
                Object.keys(styles).forEach(prop => previousStyles[prop] = styles[prop]);
            });
        }
        keyframes = balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles);
        const finalStyles = flattenKeyframesIntoStyles(keyframes);
        // if there is no animation then there is no point in applying
        // styles and waiting for an event to get fired. This causes lag.
        // It's better to just directly apply the styles to the element
        // via the direct styling animation player.
        if (duration == 0) {
            return new DirectStylePlayer(element, finalStyles);
        }
        const animationName = `${KEYFRAMES_NAME_PREFIX}${this._count++}`;
        const kfElm = this.buildKeyframeElement(element, animationName, keyframes);
        const nodeToAppendKfElm = findNodeToAppendKeyframeElement(element);
        nodeToAppendKfElm.appendChild(kfElm);
        const specialStyles = packageNonAnimatableStyles(element, keyframes);
        const player = new CssKeyframesPlayer(element, keyframes, animationName, duration, delay, easing, finalStyles, specialStyles);
        player.onDestroy(() => removeElement(kfElm));
        return player;
    }
}
function findNodeToAppendKeyframeElement(element) {
    const rootNode = element.getRootNode?.();
    if (typeof ShadowRoot !== 'undefined' && rootNode instanceof ShadowRoot) {
        return rootNode;
    }
    return document.head;
}
function flattenKeyframesIntoStyles(keyframes) {
    let flatKeyframes = {};
    if (keyframes) {
        const kfs = Array.isArray(keyframes) ? keyframes : [keyframes];
        kfs.forEach(kf => {
            Object.keys(kf).forEach(prop => {
                if (prop == 'offset' || prop == 'easing')
                    return;
                flatKeyframes[prop] = kf[prop];
            });
        });
    }
    return flatKeyframes;
}
function removeElement(node) {
    node.parentNode.removeChild(node);
}
let warningIssued = false;
function notifyFaultyScrubber() {
    if (warningIssued)
        return;
    console.warn('@angular/animations: please load the web-animations.js polyfill to allow programmatic access...\n', '  visit https://bit.ly/IWukam to learn more about using the web-animation-js polyfill.');
    warningIssued = true;
}
//# sourceMappingURL=data:application/json;base64,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