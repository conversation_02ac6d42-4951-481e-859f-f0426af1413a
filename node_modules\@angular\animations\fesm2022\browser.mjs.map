{"version": 3, "file": "browser.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/animation_driver.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/style_normalization/animation_style_normalizer.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/style_normalization/web_animations_style_normalizer.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/warning_helpers.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_transition_expr.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_ast_builder.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_timeline_instruction.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/element_instruction_map.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_timeline_builder.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_transition_instruction.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_transition_factory.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation_trigger.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/timeline_animation_engine.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/transition_animation_engine.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/animation_engine_next.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/special_cased_styles.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/web_animations/web_animations_player.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/web_animations/web_animations_driver.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/create_engine.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/dsl/animation.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/renderer.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/animation_renderer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationPlayer, NoopAnimationPlayer} from '../../../src/animations';\nimport {Injectable} from '@angular/core';\n\nimport {containsElement, getParentElement, invokeQuery, validateStyleProperty} from './shared';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\n@Injectable()\nexport class NoopAnimationDriver implements AnimationDriver {\n  /**\n   * @returns Whether `prop` is a valid CSS property\n   */\n  validateStyleProperty(prop: string): boolean {\n    return validateStyleProperty(prop);\n  }\n\n  /**\n   *\n   * @returns Whether elm1 contains elm2.\n   */\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  /**\n   * @returns Rhe parent of the given element or `null` if the element is the `document`\n   */\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  /**\n   * @returns The result of the query selector on the element. The array will contain up to 1 item\n   *     if `multi` is  `false`.\n   */\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  /**\n   * @returns The `defaultValue` or empty string\n   */\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  /**\n   * @returns An `NoopAnimationPlayer`\n   */\n  animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: any[] = [],\n    scrubberAccessRequested?: boolean,\n  ): AnimationPlayer {\n    return new NoopAnimationPlayer(duration, delay);\n  }\n}\n\n/**\n * @publicApi\n */\nexport abstract class AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static NOOP: AnimationDriver = /* @__PURE__ */ new NoopAnimationDriver();\n\n  abstract validateStyleProperty(prop: string): boolean;\n\n  abstract validateAnimatableStyleProperty?: (prop: string) => boolean;\n\n  abstract containsElement(elm1: any, elm2: any): boolean;\n\n  /**\n   * Obtains the parent element, if any. `null` is returned if the element does not have a parent.\n   */\n  abstract getParentElement(element: unknown): unknown;\n\n  abstract query(element: any, selector: string, multi: boolean): any[];\n\n  abstract computeStyle(element: any, prop: string, defaultValue?: string): string;\n\n  abstract animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing?: string | null,\n    previousPlayers?: any[],\n    scrubberAccessRequested?: boolean,\n  ): any;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport abstract class AnimationStyleNormalizer {\n  abstract normalizePropertyName(propertyName: string, errors: Error[]): string;\n  abstract normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string;\n}\n\nexport class NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName: string, errors: Error[]): string {\n    return propertyName;\n  }\n\n  normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string {\n    return <any>value;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {invalidCssUnitValue} from '../../error_helpers';\nimport {dashCaseToCamelCase} from '../../util';\n\nimport {AnimationStyleNormalizer} from './animation_style_normalizer';\n\nconst DIMENSIONAL_PROP_SET = new Set([\n  'width',\n  'height',\n  'minWidth',\n  'minHeight',\n  'maxWidth',\n  'maxHeight',\n  'left',\n  'top',\n  'bottom',\n  'right',\n  'fontSize',\n  'outlineWidth',\n  'outlineOffset',\n  'paddingTop',\n  'paddingLeft',\n  'paddingBottom',\n  'paddingRight',\n  'marginTop',\n  'marginLeft',\n  'marginBottom',\n  'marginRight',\n  'borderRadius',\n  'borderWidth',\n  'borderTopWidth',\n  'borderLeftWidth',\n  'borderRightWidth',\n  'borderBottomWidth',\n  'textIndent',\n  'perspective',\n]);\n\nexport class WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  override normalizePropertyName(propertyName: string, errors: Error[]): string {\n    return dashCaseToCamelCase(propertyName);\n  }\n\n  override normalizeStyleValue(\n    userProvidedProperty: string,\n    normalizedProperty: string,\n    value: string | number,\n    errors: Error[],\n  ): string {\n    let unit: string = '';\n    const strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nfunction createListOfWarnings(warnings: string[]): string {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings\n    .filter(Boolean)\n    .map((warning) => warning)\n    .join(LINE_START)}`;\n}\n\nexport function warnValidation(warnings: string[]): void {\n  console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\n\nexport function warnTriggerBuild(name: string, warnings: string[]): void {\n  console.warn(\n    `The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(\n      warnings,\n    )}`,\n  );\n}\n\nexport function warnRegister(warnings: string[]): void {\n  console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\n\nexport function triggerParsingWarnings(name: string, warnings: string[]): void {\n  console.warn(\n    `Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(\n      warnings,\n    )}`,\n  );\n}\n\nexport function pushUnrecognizedPropertiesWarning(warnings: string[], props: string[]): void {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {invalidExpression, invalidTransitionAlias} from '../error_helpers';\n\nexport const ANY_STATE = '*';\nexport declare type TransitionMatcherFn = (\n  fromState: any,\n  toState: any,\n  element: any,\n  params: {[key: string]: any},\n) => boolean;\n\nexport function parseTransitionExpr(\n  transitionValue: string | TransitionMatcherFn,\n  errors: Error[],\n): TransitionMatcherFn[] {\n  const expressions: TransitionMatcherFn[] = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue\n      .split(/\\s*,\\s*/)\n      .forEach((str) => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(<TransitionMatcherFn>transitionValue);\n  }\n  return expressions;\n}\n\nfunction parseInnerTransitionStr(\n  eventStr: string,\n  expressions: TransitionMatcherFn[],\n  errors: Error[],\n) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\n\nfunction parseAnimationAlias(alias: string, errors: Error[]): string | TransitionMatcherFn {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState: any, toState: any): boolean => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set<string>(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set<string>(['false', '0']);\n\nfunction makeLambdaFromStates(lhs: string, rhs: string): TransitionMatcherFn {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n\n  return (fromState: any, toState: any): boolean => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimateTimings,\n  AnimationAnimateChildMetadata,\n  AnimationAnimateMetadata,\n  AnimationAnimateRefMetadata,\n  AnimationGroupMetadata,\n  AnimationKeyframesSequenceMetadata,\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationQueryMetadata,\n  AnimationQueryOptions,\n  AnimationReferenceMetadata,\n  AnimationSequenceMetadata,\n  AnimationStaggerMetadata,\n  AnimationStateMetadata,\n  AnimationStyleMetadata,\n  AnimationTransitionMetadata,\n  AnimationTriggerMetadata,\n  AUTO_STYLE,\n  style,\n  ɵStyleDataMap,\n} from '../../../src/animations';\n\nimport {\n  invalidDefinition,\n  invalidKeyframes,\n  invalidOffset,\n  invalidParallelAnimation,\n  invalidProperty,\n  invalidStagger,\n  invalidState,\n  invalidStyleValue,\n  invalidTrigger,\n  keyframeOffsetsOutOfOrder,\n  keyframesMissingOffsets,\n} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetDefaultValue} from '../render/shared';\nimport {\n  extractStyleParams,\n  NG_ANIMATING_SELECTOR,\n  NG_TRIGGER_SELECTOR,\n  normalizeAnimationEntry,\n  resolveTiming,\n  SUBSTITUTION_EXPR_START,\n  validateStyleParams,\n  visitDslNode,\n} from '../util';\nimport {pushUnrecognizedPropertiesWarning} from '../warning_helpers';\n\nimport {\n  AnimateAst,\n  AnimateChildAst,\n  AnimateRefAst,\n  Ast,\n  DynamicTimingAst,\n  GroupAst,\n  KeyframesAst,\n  QueryAst,\n  ReferenceAst,\n  SequenceAst,\n  StaggerAst,\n  StateAst,\n  StyleAst,\n  TimingAst,\n  TransitionAst,\n  TriggerAst,\n} from './animation_ast';\nimport {AnimationDslVisitor} from './animation_dsl_visitor';\nimport {parseTransitionExpr} from './animation_transition_expr';\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */ new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nexport function buildAnimationAst(\n  driver: AnimationDriver,\n  metadata: AnimationMetadata | AnimationMetadata[],\n  errors: Error[],\n  warnings: string[],\n): Ast<AnimationMetadataType> {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\n\nconst ROOT_SELECTOR = '';\n\nexport class AnimationAstBuilderVisitor implements AnimationDslVisitor {\n  constructor(private _driver: AnimationDriver) {}\n\n  build(\n    metadata: AnimationMetadata | AnimationMetadata[],\n    errors: Error[],\n    warnings: string[],\n  ): Ast<AnimationMetadataType> {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = <Ast<AnimationMetadataType>>(\n      visitDslNode(this, normalizeAnimationEntry(metadata), context)\n    );\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [\n          ...context.unsupportedCSSPropertiesFound.keys(),\n        ]);\n      }\n    }\n\n    return ast;\n  }\n\n  private _resetContextStyleTimingState(context: AnimationAstBuilderContext) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map<string, Map<string, StyleTimeTuple>>();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n\n  visitTrigger(\n    metadata: AnimationTriggerMetadata,\n    context: AnimationAstBuilderContext,\n  ): TriggerAst {\n    let queryCount = (context.queryCount = 0);\n    let depCount = (context.depCount = 0);\n    const states: StateAst[] = [];\n    const transitions: TransitionAst[] = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n\n    metadata.definitions.forEach((def) => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def as AnimationStateMetadata;\n        const name = stateDef.name;\n        name\n          .toString()\n          .split(/\\s*,\\s*/)\n          .forEach((n) => {\n            stateDef.name = n;\n            states.push(this.visitState(stateDef, context));\n          });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def as AnimationTransitionMetadata, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null,\n    };\n  }\n\n  visitState(metadata: AnimationStateMetadata, context: AnimationAstBuilderContext): StateAst {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = (metadata.options && metadata.options.params) || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set<string>();\n      const params = astParams || {};\n      styleAst.styles.forEach((style) => {\n        if (style instanceof Map) {\n          style.forEach((value) => {\n            extractStyleParams(value).forEach((sub) => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {params: astParams} : null,\n    };\n  }\n\n  visitTransition(\n    metadata: AnimationTransitionMetadata,\n    context: AnimationAstBuilderContext,\n  ): TransitionAst {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitSequence(\n    metadata: AnimationSequenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): SequenceAst {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map((s) => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitGroup(metadata: AnimationGroupMetadata, context: AnimationAstBuilderContext): GroupAst {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map((step) => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimate(\n    metadata: AnimationAnimateMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateAst {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst: StyleAst | KeyframesAst;\n    let styleMetadata: AnimationStyleMetadata | AnimationKeyframesSequenceMetadata = metadata.styles\n      ? metadata.styles\n      : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata as AnimationKeyframesSequenceMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles as AnimationStyleMetadata;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData: {[prop: string]: string | number} = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null,\n    };\n  }\n\n  visitStyle(metadata: AnimationStyleMetadata, context: AnimationAstBuilderContext): StyleAst {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n\n  private _makeStyleAst(\n    metadata: AnimationStyleMetadata,\n    context: AnimationAstBuilderContext,\n  ): StyleAst {\n    const styles: Array<ɵStyleDataMap | string> = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n\n    let containsDynamicStyles = false;\n    let collectedEasing: string | null = null;\n    styles.forEach((styleData) => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing') as string;\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value!.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null,\n    };\n  }\n\n  private _validateStyleAst(ast: StyleAst, context: AnimationAstBuilderContext): void {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach((tuple) => {\n      if (typeof tuple === 'string') return;\n\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector!)!;\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (\n            startTime != endTime &&\n            startTime >= collectedEntry.startTime &&\n            endTime <= collectedEntry.endTime\n          ) {\n            context.errors.push(\n              invalidParallelAnimation(\n                prop,\n                collectedEntry.startTime,\n                collectedEntry.endTime,\n                startTime,\n                endTime,\n              ),\n            );\n            updateCollectedStyle = false;\n          }\n\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {startTime, endTime});\n        }\n\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n\n  visitKeyframes(\n    metadata: AnimationKeyframesSequenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): KeyframesAst {\n    const ast: KeyframesAst = {type: AnimationMetadataType.Keyframes, styles: [], options: null};\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n\n    const MAX_KEYFRAME_OFFSET = 1;\n\n    let totalKeyframesWithOffsets = 0;\n    const offsets: number[] = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset: number = 0;\n\n    const keyframes: StyleAst[] = metadata.steps.map((styles) => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal: number | null =\n        style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset: number = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings!;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? (i == limit ? 1 : generatedOffset * i) : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n\n      ast.styles.push(kf);\n    });\n\n    return ast;\n  }\n\n  visitReference(\n    metadata: AnimationReferenceMetadata,\n    context: AnimationAstBuilderContext,\n  ): ReferenceAst {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimateChild(\n    metadata: AnimationAnimateChildMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateChildAst {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitAnimateRef(\n    metadata: AnimationAnimateRefMetadata,\n    context: AnimationAstBuilderContext,\n  ): AnimateRefAst {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitQuery(metadata: AnimationQueryMetadata, context: AnimationAstBuilderContext): QueryAst {\n    const parentSelector = context.currentQuerySelector!;\n    const options = (metadata.options || {}) as AnimationQueryOptions;\n\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length\n      ? parentSelector + ' ' + selector\n      : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options),\n    };\n  }\n\n  visitStagger(\n    metadata: AnimationStaggerMetadata,\n    context: AnimationAstBuilderContext,\n  ): StaggerAst {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings =\n      metadata.timings === 'full'\n        ? {duration: 0, delay: 0, easing: 'full'}\n        : resolveTiming(metadata.timings, context.errors, true);\n\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null,\n    };\n  }\n}\n\nfunction normalizeSelector(selector: string): [string, boolean] {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find((token) => token == SELF_TOKEN)\n    ? true\n    : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector\n    .replace(/@\\*/g, NG_TRIGGER_SELECTOR)\n    .replace(/@\\w+/g, (match) => NG_TRIGGER_SELECTOR + '-' + match.slice(1))\n    .replace(/:animating/g, NG_ANIMATING_SELECTOR);\n\n  return [selector, hasAmpersand];\n}\n\nfunction normalizeParams(obj: {[key: string]: any} | any): {[key: string]: any} | null {\n  return obj ? {...obj} : null;\n}\n\nexport type StyleTimeTuple = {\n  startTime: number;\n  endTime: number;\n};\n\nexport class AnimationAstBuilderContext {\n  public queryCount: number = 0;\n  public depCount: number = 0;\n  public currentTransition: AnimationTransitionMetadata | null = null;\n  public currentQuery: AnimationQueryMetadata | null = null;\n  public currentQuerySelector: string | null = null;\n  public currentAnimateTimings: TimingAst | null = null;\n  public currentTime: number = 0;\n  public collectedStyles = new Map<string, Map<string, StyleTimeTuple>>();\n  public options: AnimationOptions | null = null;\n  public unsupportedCSSPropertiesFound: Set<string> = new Set<string>();\n  constructor(public errors: Error[]) {}\n}\n\ntype OffsetStyles = string | ɵStyleDataMap;\n\nfunction consumeOffset(styles: OffsetStyles | Array<OffsetStyles>): number | null {\n  if (typeof styles == 'string') return null;\n\n  let offset: number | null = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach((styleTuple) => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple as ɵStyleDataMap;\n        offset = parseFloat(obj.get('offset') as string);\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset') as string);\n    obj.delete('offset');\n  }\n  return offset;\n}\n\nfunction constructTimingAst(value: string | number | AnimateTimings, errors: Error[]) {\n  if (value.hasOwnProperty('duration')) {\n    return value as AnimateTimings;\n  }\n\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n\n  const strValue = value as string;\n  const isDynamic = strValue.split(/\\s+/).some((v) => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '') as any;\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast as DynamicTimingAst;\n  }\n\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n\nfunction normalizeAnimationOptions(options: AnimationOptions | null): AnimationOptions {\n  if (options) {\n    options = {...options};\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params'])!;\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\n\nfunction makeTimingAst(duration: number, delay: number, easing: string | null): TimingAst {\n  return {duration, delay, easing};\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {ɵStyleDataMap} from '../../../src/animations';\n\nimport {\n  AnimationEngineInstruction,\n  AnimationTransitionInstructionType,\n} from '../render/animation_engine_instruction';\n\nexport interface AnimationTimelineInstruction extends AnimationEngineInstruction {\n  element: any;\n  keyframes: Array<ɵStyleDataMap>;\n  preStyleProps: string[];\n  postStyleProps: string[];\n  duration: number;\n  delay: number;\n  totalTime: number;\n  easing: string | null;\n  stretchStartingKeyframe?: boolean;\n  subTimeline: boolean;\n}\n\nexport function createTimelineInstruction(\n  element: any,\n  keyframes: Array<ɵStyleDataMap>,\n  preStyleProps: string[],\n  postStyleProps: string[],\n  duration: number,\n  delay: number,\n  easing: string | null = null,\n  subTimeline: boolean = false,\n): AnimationTimelineInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TimelineAnimation,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport class ElementInstructionMap {\n  private _map = new Map<any, AnimationTimelineInstruction[]>();\n\n  get(element: any): AnimationTimelineInstruction[] {\n    return this._map.get(element) || [];\n  }\n\n  append(element: any, instructions: AnimationTimelineInstruction[]) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, (existingInstructions = []));\n    }\n    existingInstructions.push(...instructions);\n  }\n\n  has(element: any): boolean {\n    return this._map.has(element);\n  }\n\n  clear() {\n    this._map.clear();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimateChildOptions,\n  AnimateTimings,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationQueryOptions,\n  AUTO_STYLE,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '../../../src/animations';\n\nimport {invalidQuery} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {interpolateParams, resolveTiming, resolveTimingValue, visitDslNode} from '../util';\n\nimport {\n  AnimateAst,\n  AnimateChildAst,\n  AnimateRefAst,\n  Ast,\n  AstVisitor,\n  DynamicTimingAst,\n  GroupAst,\n  KeyframesAst,\n  QueryAst,\n  ReferenceAst,\n  SequenceAst,\n  StaggerAst,\n  StateAst,\n  StyleAst,\n  TimingAst,\n  TransitionAst,\n  TriggerAst,\n} from './animation_ast';\nimport {\n  AnimationTimelineInstruction,\n  createTimelineInstruction,\n} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */ new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */ new RegExp(LEAVE_TOKEN, 'g');\n\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nexport function buildAnimationTimelines(\n  driver: AnimationDriver,\n  rootElement: any,\n  ast: Ast<AnimationMetadataType>,\n  enterClassName: string,\n  leaveClassName: string,\n  startingStyles: ɵStyleDataMap = new Map(),\n  finalStyles: ɵStyleDataMap = new Map(),\n  options: AnimationOptions,\n  subInstructions?: ElementInstructionMap,\n  errors: Error[] = [],\n): AnimationTimelineInstruction[] {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(\n    driver,\n    rootElement,\n    ast,\n    enterClassName,\n    leaveClassName,\n    startingStyles,\n    finalStyles,\n    options,\n    subInstructions,\n    errors,\n  );\n}\n\nexport class AnimationTimelineBuilderVisitor implements AstVisitor {\n  buildKeyframes(\n    driver: AnimationDriver,\n    rootElement: any,\n    ast: Ast<AnimationMetadataType>,\n    enterClassName: string,\n    leaveClassName: string,\n    startingStyles: ɵStyleDataMap,\n    finalStyles: ɵStyleDataMap,\n    options: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n    errors: Error[] = [],\n  ): AnimationTimelineInstruction[] {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(\n      driver,\n      rootElement,\n      subInstructions,\n      enterClassName,\n      leaveClassName,\n      errors,\n      [],\n    );\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n\n    visitDslNode(this, ast, context);\n\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter((timeline) => timeline.containsAnimation());\n\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline: TimelineBuilder | undefined;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length\n      ? timelines.map((timeline) => timeline.buildKeyframes())\n      : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n\n  visitTrigger(ast: TriggerAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitState(ast: StateAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitTransition(ast: TransitionAst, context: AnimationTimelineContext): any {\n    // these values are not visited in this AST\n  }\n\n  visitAnimateChild(ast: AnimateChildAst, context: AnimationTimelineContext): any {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(\n        elementInstructions,\n        innerContext,\n        innerContext.options as AnimateChildOptions,\n      );\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n\n  visitAnimateRef(ast: AnimateRefAst, context: AnimationTimelineContext): any {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n\n  private _applyAnimationRefDelays(\n    animationsRefsOptions: (AnimationOptions | null)[],\n    context: AnimationTimelineContext,\n    innerContext: AnimationTimelineContext,\n  ) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue =\n          typeof animationDelay === 'number'\n            ? animationDelay\n            : resolveTimingValue(\n                interpolateParams(\n                  animationDelay,\n                  animationRefOptions?.params ?? {},\n                  context.errors,\n                ),\n              );\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n\n  private _visitSubInstructions(\n    instructions: AnimationTimelineInstruction[],\n    context: AnimationTimelineContext,\n    options: AnimateChildOptions,\n  ): number {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach((instruction) => {\n        const instructionTimings = context.appendInstructionToTimeline(\n          instruction,\n          duration,\n          delay,\n        );\n        furthestTime = Math.max(\n          furthestTime,\n          instructionTimings.duration + instructionTimings.delay,\n        );\n      });\n    }\n\n    return furthestTime;\n  }\n\n  visitReference(ast: ReferenceAst, context: AnimationTimelineContext) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n\n  visitSequence(ast: SequenceAst, context: AnimationTimelineContext) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach((s) => visitDslNode(this, s, ctx));\n\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitGroup(ast: GroupAst, context: AnimationTimelineContext) {\n    const innerTimelines: TimelineBuilder[] = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n\n    ast.steps.forEach((s) => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach((timeline) =>\n      context.currentTimeline.mergeTimelineCollectedStyles(timeline),\n    );\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n\n  private _visitTiming(ast: TimingAst, context: AnimationTimelineContext): AnimateTimings {\n    if ((ast as DynamicTimingAst).dynamic) {\n      const strValue = (ast as DynamicTimingAst).strValue;\n      const timingValue = context.params\n        ? interpolateParams(strValue, context.params, context.errors)\n        : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {duration: ast.duration, delay: ast.delay, easing: ast.easing};\n    }\n  }\n\n  visitAnimate(ast: AnimateAst, context: AnimationTimelineContext) {\n    const timings = (context.currentAnimateTimings = this._visitTiming(ast.timings, context));\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style as StyleAst, context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n\n  visitStyle(ast: StyleAst, context: AnimationTimelineContext) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings!;\n\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n\n    const easing = (timings && timings.easing) || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitKeyframes(ast: KeyframesAst, context: AnimationTimelineContext) {\n    const currentAnimateTimings = context.currentAnimateTimings!;\n    const startTime = context.currentTimeline!.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n\n    ast.styles.forEach((step) => {\n      const offset: number = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n\n  visitQuery(ast: QueryAst, context: AnimationTimelineContext) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = (ast.options || {}) as AnimationQueryOptions;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (\n      delay &&\n      (context.previousNode.type === AnimationMetadataType.Style ||\n        (startTime == 0 && context.currentTimeline.hasCurrentStyleProperties()))\n    ) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(\n      ast.selector,\n      ast.originalSelector,\n      ast.limit,\n      ast.includeSelf,\n      options.optional ? true : false,\n      context.errors,\n    );\n\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline: TimelineBuilder | null = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      visitDslNode(this, ast.animation, innerContext);\n\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitStagger(ast: StaggerAst, context: AnimationTimelineContext) {\n    const parentContext = context.parentContext!;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime =\n      tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\n\nexport declare type StyleAtTime = {\n  time: number;\n  value: string | number;\n};\n\nconst DEFAULT_NOOP_PREVIOUS_NODE = <Ast<AnimationMetadataType>>{};\nexport class AnimationTimelineContext {\n  public parentContext: AnimationTimelineContext | null = null;\n  public currentTimeline: TimelineBuilder;\n  public currentAnimateTimings: AnimateTimings | null = null;\n  public previousNode: Ast<AnimationMetadataType> = DEFAULT_NOOP_PREVIOUS_NODE;\n  public subContextCount = 0;\n  public options: AnimationOptions = {};\n  public currentQueryIndex: number = 0;\n  public currentQueryTotal: number = 0;\n  public currentStaggerTime: number = 0;\n\n  constructor(\n    private _driver: AnimationDriver,\n    public element: any,\n    public subInstructions: ElementInstructionMap,\n    private _enterClassName: string,\n    private _leaveClassName: string,\n    public errors: Error[],\n    public timelines: TimelineBuilder[],\n    initialTimeline?: TimelineBuilder,\n  ) {\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n\n  get params() {\n    return this.options.params;\n  }\n\n  updateOptions(options: AnimationOptions | null, skipIfExists?: boolean) {\n    if (!options) return;\n\n    const newOptions = options as any;\n    let optionsToUpdate = this.options;\n\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      (optionsToUpdate as any).duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate: {[name: string]: any} = optionsToUpdate.params!;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach((name) => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n\n  private _copyOptions() {\n    const options: AnimationOptions = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params: {[name: string]: any} = (options['params'] = {});\n        Object.keys(oldParams).forEach((name) => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n\n  createSubContext(\n    options: AnimationOptions | null = null,\n    element?: any,\n    newTime?: number,\n  ): AnimationTimelineContext {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(\n      this._driver,\n      target,\n      this.subInstructions,\n      this._enterClassName,\n      this._leaveClassName,\n      this.errors,\n      this.timelines,\n      this.currentTimeline.fork(target, newTime || 0),\n    );\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n\n  transformIntoNewTimeline(newTime?: number) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n\n  appendInstructionToTimeline(\n    instruction: AnimationTimelineInstruction,\n    duration: number | null,\n    delay: number | null,\n  ): AnimateTimings {\n    const updatedTimings: AnimateTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: '',\n    };\n    const builder = new SubTimelineBuilder(\n      this._driver,\n      instruction.element,\n      instruction.keyframes,\n      instruction.preStyleProps,\n      instruction.postStyleProps,\n      updatedTimings,\n      instruction.stretchStartingKeyframe,\n    );\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n\n  incrementTime(time: number) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n\n  delayNextStep(delay: number) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n\n  invokeQuery(\n    selector: string,\n    originalSelector: string,\n    limit: number,\n    includeSelf: boolean,\n    optional: boolean,\n    errors: Error[],\n  ): any[] {\n    let results: any[] = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements =\n          limit < 0\n            ? elements.slice(elements.length + limit, elements.length)\n            : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\n\nexport class TimelineBuilder {\n  public duration: number = 0;\n  public easing: string | null = null;\n  private _previousKeyframe: ɵStyleDataMap = new Map();\n  private _currentKeyframe: ɵStyleDataMap = new Map();\n  private _keyframes = new Map<number, ɵStyleDataMap>();\n  private _styleSummary = new Map<string, StyleAtTime>();\n  private _localTimelineStyles: ɵStyleDataMap = new Map();\n  private _globalTimelineStyles: ɵStyleDataMap;\n  private _pendingStyles: ɵStyleDataMap = new Map();\n  private _backFill: ɵStyleDataMap = new Map();\n  private _currentEmptyStepKeyframe: ɵStyleDataMap | null = null;\n\n  constructor(\n    private _driver: AnimationDriver,\n    public element: any,\n    public startTime: number,\n    private _elementTimelineStylesLookup?: Map<any, ɵStyleDataMap>,\n  ) {\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map<any, ɵStyleDataMap>();\n    }\n\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element)!;\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n\n  containsAnimation(): boolean {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n\n  hasCurrentStyleProperties(): boolean {\n    return this._currentKeyframe.size > 0;\n  }\n\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n\n  delayNextStep(delay: number) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n\n  fork(element: any, currentTime?: number): TimelineBuilder {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(\n      this._driver,\n      element,\n      currentTime || this.currentTime,\n      this._elementTimelineStylesLookup,\n    );\n  }\n\n  private _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration)!;\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n\n  forwardTime(time: number) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n\n  private _updateStyle(prop: string, value: string | number) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {time: this.currentTime, value});\n  }\n\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n\n  applyEmptyStep(easing: string | null) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n\n  setStyles(\n    input: Array<ɵStyleDataMap | string>,\n    easing: string | null,\n    errors: Error[],\n    options?: AnimationOptions,\n  ) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = (options && options.params) || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n\n  get properties() {\n    const properties: string[] = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n\n  mergeTimelineCollectedStyles(timeline: TimelineBuilder) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n\n  buildKeyframes(): AnimationTimelineInstruction {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set<string>();\n    const postStyleProps = new Set<string>();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n\n    let finalKeyframes: Array<ɵStyleDataMap> = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const preProps: string[] = [...preStyleProps.values()];\n    const postProps: string[] = [...postStyleProps.values()];\n\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(\n      this.element,\n      finalKeyframes,\n      preProps,\n      postProps,\n      this.duration,\n      this.startTime,\n      this.easing,\n      false,\n    );\n  }\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\n  public timings: AnimateTimings;\n\n  constructor(\n    driver: AnimationDriver,\n    element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public preStyleProps: string[],\n    public postStyleProps: string[],\n    timings: AnimateTimings,\n    private _stretchStartingKeyframe: boolean = false,\n  ) {\n    super(driver, element, timings.delay);\n    this.timings = {duration: timings.duration, delay: timings.delay, easing: timings.easing};\n  }\n\n  override containsAnimation(): boolean {\n    return this.keyframes.length > 1;\n  }\n\n  override buildKeyframes(): AnimationTimelineInstruction {\n    let keyframes = this.keyframes;\n    let {delay, duration, easing} = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes: Array<ɵStyleDataMap> = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n\n        delay=1000, duration=1000, keyframes = 0 .5 1\n\n        turns into\n\n        delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset') as number;\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(\n      this.element,\n      keyframes,\n      this.preStyleProps,\n      this.postStyleProps,\n      duration,\n      delay,\n      easing,\n      true,\n    );\n  }\n}\n\nfunction roundOffset(offset: number, decimalPoints = 3): number {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n\nfunction flattenStyles(input: Array<ɵStyleDataMap | string>, allStyles: ɵStyleDataMap) {\n  const styles: ɵStyleDataMap = new Map();\n  let allProperties: string[] | IterableIterator<string>;\n  input.forEach((token) => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token as ɵStyleDataMap) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵStyleDataMap} from '../../../src/animations';\n\nimport {\n  AnimationEngineInstruction,\n  AnimationTransitionInstructionType,\n} from '../render/animation_engine_instruction';\n\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\n\nexport interface AnimationTransitionInstruction extends AnimationEngineInstruction {\n  element: any;\n  triggerName: string;\n  isRemovalTransition: boolean;\n  fromState: string;\n  fromStyles: ɵStyleDataMap;\n  toState: string;\n  toStyles: ɵStyleDataMap;\n  timelines: AnimationTimelineInstruction[];\n  queriedElements: any[];\n  preStyleProps: Map<any, Set<string>>;\n  postStyleProps: Map<any, Set<string>>;\n  totalTime: number;\n  errors?: Error[];\n}\n\nexport function createTransitionInstruction(\n  element: any,\n  triggerName: string,\n  fromState: string,\n  toState: string,\n  isRemovalTransition: boolean,\n  fromStyles: ɵStyleDataMap,\n  toStyles: ɵStyleDataMap,\n  timelines: AnimationTimelineInstruction[],\n  queriedElements: any[],\n  preStyleProps: Map<any, Set<string>>,\n  postStyleProps: Map<any, Set<string>>,\n  totalTime: number,\n  errors?: Error[],\n): AnimationTransitionInstruction {\n  return {\n    type: AnimationTransitionInstructionType.TransitionAnimation,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationOptions, ɵStyleDataMap} from '../../../src/animations';\n\nimport {AnimationDriver} from '../render/animation_driver';\nimport {getOrSetDefaultValue} from '../render/shared';\nimport {interpolateParams} from '../util';\n\nimport {StyleAst, TransitionAst} from './animation_ast';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {TransitionMatcherFn} from './animation_transition_expr';\nimport {\n  AnimationTransitionInstruction,\n  createTransitionInstruction,\n} from './animation_transition_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\nimport {AnimationStyleNormalizer} from './style_normalization/animation_style_normalizer';\n\nconst EMPTY_OBJECT = {};\n\nexport class AnimationTransitionFactory {\n  constructor(\n    private _triggerName: string,\n    public ast: TransitionAst,\n    private _stateStyles: Map<string, AnimationStateStyles>,\n  ) {}\n\n  match(currentState: any, nextState: any, element: any, params: {[key: string]: any}): boolean {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n\n  buildStyles(\n    stateName: string | boolean | undefined,\n    params: {[key: string]: any},\n    errors: Error[],\n  ): ɵStyleDataMap {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n\n  build(\n    driver: AnimationDriver,\n    element: any,\n    currentState: any,\n    nextState: any,\n    enterClassName: string,\n    leaveClassName: string,\n    currentOptions?: AnimationOptions,\n    nextOptions?: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n    skipAstBuild?: boolean,\n  ): AnimationTransitionInstruction {\n    const errors: Error[] = [];\n\n    const transitionAnimationParams = (this.ast.options && this.ast.options.params) || EMPTY_OBJECT;\n    const currentAnimationParams = (currentOptions && currentOptions.params) || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = (nextOptions && nextOptions.params) || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n\n    const queriedElements = new Set<any>();\n    const preStyleMap = new Map<any, Set<string>>();\n    const postStyleMap = new Map<any, Set<string>>();\n    const isRemoval = nextState === 'void';\n\n    const animationOptions: AnimationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay,\n    };\n\n    const timelines = skipAstBuild\n      ? []\n      : buildAnimationTimelines(\n          driver,\n          element,\n          this.ast.animation,\n          enterClassName,\n          leaveClassName,\n          currentStateStyles,\n          nextStateStyles,\n          animationOptions,\n          subInstructions,\n          errors,\n        );\n\n    let totalTime = 0;\n    timelines.forEach((tl) => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n\n    if (errors.length) {\n      return createTransitionInstruction(\n        element,\n        this._triggerName,\n        currentState,\n        nextState,\n        isRemoval,\n        currentStateStyles,\n        nextStateStyles,\n        [],\n        [],\n        preStyleMap,\n        postStyleMap,\n        totalTime,\n        errors,\n      );\n    }\n\n    timelines.forEach((tl) => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set<string>());\n      tl.preStyleProps.forEach((prop) => preProps.add(prop));\n\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set<string>());\n      tl.postStyleProps.forEach((prop) => postProps.add(prop));\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n\n    return createTransitionInstruction(\n      element,\n      this._triggerName,\n      currentState,\n      nextState,\n      isRemoval,\n      currentStateStyles,\n      nextStateStyles,\n      timelines,\n      [...queriedElements.values()],\n      preStyleMap,\n      postStyleMap,\n      totalTime,\n    );\n  }\n}\n\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(\n  timelines: AnimationTimelineInstruction[],\n  triggerName: string,\n  driver: AnimationDriver,\n): void {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n\n  const allowedNonAnimatableProps = new Set<string>([\n    // 'easing' is a utility/synthetic prop we use to represent\n    // easing functions, it represents a property of the animation\n    // which is not animatable but different values can be used\n    // in different steps\n    'easing',\n  ]);\n\n  const invalidNonAnimatableProps = new Set<string>();\n\n  timelines.forEach(({keyframes}) => {\n    const nonAnimatablePropsInitialValues = new Map<string, string | number>();\n    keyframes.forEach((keyframe) => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(\n        ([prop]) => !allowedNonAnimatableProps.has(prop),\n      );\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty!(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(\n      `Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` +\n        ' not animatable properties: ' +\n        Array.from(invalidNonAnimatableProps).join(', ') +\n        '\\n' +\n        '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)',\n    );\n  }\n}\n\nfunction oneOrMoreTransitionsMatch(\n  matchFns: TransitionMatcherFn[],\n  currentState: any,\n  nextState: any,\n  element: any,\n  params: {[key: string]: any},\n): boolean {\n  return matchFns.some((fn) => fn(currentState, nextState, element, params));\n}\n\nfunction applyParamDefaults(userParams: Record<string, any>, defaults: Record<string, any>) {\n  const result: Record<string, any> = {...defaults};\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\nexport class AnimationStateStyles {\n  constructor(\n    private styles: StyleAst,\n    private defaultParams: {[key: string]: any},\n    private normalizer: AnimationStyleNormalizer,\n  ) {}\n\n  buildStyles(params: {[key: string]: any}, errors: Error[]): ɵStyleDataMap {\n    const finalStyles: ɵStyleDataMap = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach((value) => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationMetadataType, ɵStyleDataMap} from '../../../src/animations';\n\nimport {SequenceAst, TransitionAst, TriggerAst} from './animation_ast';\nimport {AnimationStateStyles, AnimationTransitionFactory} from './animation_transition_factory';\nimport {AnimationStyleNormalizer} from './style_normalization/animation_style_normalizer';\n\nexport function buildTrigger(\n  name: string,\n  ast: TriggerAst,\n  normalizer: AnimationStyleNormalizer,\n): AnimationTrigger {\n  return new AnimationTrigger(name, ast, normalizer);\n}\n\nexport class AnimationTrigger {\n  public transitionFactories: AnimationTransitionFactory[] = [];\n  public fallbackTransition: AnimationTransitionFactory;\n  public states = new Map<string, AnimationStateStyles>();\n\n  constructor(\n    public name: string,\n    public ast: TriggerAst,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {\n    ast.states.forEach((ast) => {\n      const defaultParams = (ast.options && ast.options.params) || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n\n    ast.transitions.forEach((ast) => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);\n  }\n\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n\n  matchTransition(\n    currentState: any,\n    nextState: any,\n    element: any,\n    params: {[key: string]: any},\n  ): AnimationTransitionFactory | null {\n    const entry = this.transitionFactories.find((f) =>\n      f.match(currentState, nextState, element, params),\n    );\n    return entry || null;\n  }\n\n  matchStyles(currentState: any, params: {[key: string]: any}, errors: Error[]): ɵStyleDataMap {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\n\nfunction createFallbackTransition(\n  triggerName: string,\n  states: Map<string, AnimationStateStyles>,\n  normalizer: AnimationStyleNormalizer,\n): AnimationTransitionFactory {\n  const matchers = [(fromState: any, toState: any) => true];\n  const animation: SequenceAst = {type: AnimationMetadataType.Sequence, steps: [], options: null};\n  const transition: TransitionAst = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0,\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n\nfunction balanceProperties(\n  stateMap: Map<string, AnimationStateStyles>,\n  key1: string,\n  key2: string,\n) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1)!);\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2)!);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  AnimationPlayer,\n  AUTO_STYLE,\n  ɵStyleDataMap,\n} from '../../../src/animations';\n\nimport {Ast} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {buildAnimationTimelines} from '../dsl/animation_timeline_builder';\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {\n  createAnimationFailed,\n  missingOrDestroyedAnimation,\n  missingPlayer,\n  registerFailed,\n} from '../error_helpers';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME} from '../util';\nimport {warnRegister} from '../warning_helpers';\n\nimport {AnimationDriver} from './animation_driver';\nimport {\n  getOrSetDefaultValue,\n  listenOnPlayer,\n  makeAnimationEvent,\n  normalizeKeyframes,\n  optimizeGroupPlayer,\n} from './shared';\n\nconst EMPTY_INSTRUCTION_MAP = /* @__PURE__ */ new ElementInstructionMap();\n\nexport class TimelineAnimationEngine {\n  private _animations = new Map<string, Ast<AnimationMetadataType>>();\n  private _playersById = new Map<string, AnimationPlayer>();\n  public players: AnimationPlayer[] = [];\n\n  constructor(\n    public bodyNode: any,\n    private _driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {}\n\n  register(id: string, metadata: AnimationMetadata | AnimationMetadata[]) {\n    const errors: Error[] = [];\n    const warnings: string[] = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnRegister(warnings);\n        }\n      }\n      this._animations.set(id, ast);\n    }\n  }\n\n  private _buildPlayer(\n    i: AnimationTimelineInstruction,\n    preStyles: ɵStyleDataMap,\n    postStyles?: ɵStyleDataMap,\n  ): AnimationPlayer {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n\n  create(id: string, element: any, options: AnimationOptions = {}): AnimationPlayer {\n    const errors: Error[] = [];\n    const ast = this._animations.get(id);\n    let instructions: AnimationTimelineInstruction[];\n\n    const autoStylesMap = new Map<any, ɵStyleDataMap>();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(\n        this._driver,\n        element,\n        ast,\n        ENTER_CLASSNAME,\n        LEAVE_CLASSNAME,\n        new Map(),\n        new Map(),\n        options,\n        EMPTY_INSTRUCTION_MAP,\n        errors,\n      );\n      instructions.forEach((inst) => {\n        const styles = getOrSetDefaultValue(\n          autoStylesMap,\n          inst.element,\n          new Map<string, string | number | null>(),\n        );\n        inst.postStyleProps.forEach((prop) => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n\n    const players = instructions.map((i) => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n\n    this.players.push(player);\n    return player;\n  }\n\n  destroy(id: string) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n\n  private _getPlayer(id: string): AnimationPlayer {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n\n  listen(\n    id: string,\n    element: string,\n    eventName: string,\n    callback: (event: any) => any,\n  ): () => void {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n\n  command(id: string, element: any, command: string, args: any[]): void {\n    if (command == 'register') {\n      this.register(id, args[0] as AnimationMetadata | AnimationMetadata[]);\n      return;\n    }\n\n    if (command == 'create') {\n      const options = (args[0] || {}) as AnimationOptions;\n      this.create(id, element, options);\n      return;\n    }\n\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0] as string));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationOptions,\n  AnimationPlayer,\n  AUTO_STYLE,\n  NoopAnimationPlayer,\n  ɵAnimationGroupPlayer as AnimationGroupPlayer,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '../../../src/animations';\nimport {ɵWritable as Writable} from '@angular/core';\n\nimport {AnimationTimelineInstruction} from '../dsl/animation_timeline_instruction';\nimport {AnimationTransitionFactory} from '../dsl/animation_transition_factory';\nimport {AnimationTransitionInstruction} from '../dsl/animation_transition_instruction';\nimport {AnimationTrigger} from '../dsl/animation_trigger';\nimport {ElementInstructionMap} from '../dsl/element_instruction_map';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {\n  missingEvent,\n  missingTrigger,\n  transitionFailed,\n  triggerTransitionsFailed,\n  unregisteredTrigger,\n  unsupportedTriggerEvent,\n} from '../error_helpers';\nimport {\n  ENTER_CLASSNAME,\n  eraseStyles,\n  LEAVE_CLASSNAME,\n  NG_ANIMATING_CLASSNAME,\n  NG_ANIMATING_SELECTOR,\n  NG_TRIGGER_CLASSNAME,\n  NG_TRIGGER_SELECTOR,\n  setStyles,\n} from '../util';\n\nimport {AnimationDriver} from './animation_driver';\nimport {\n  getOrSetDefaultValue,\n  listenOnPlayer,\n  makeAnimationEvent,\n  normalizeKeyframes,\n  optimizeGroupPlayer,\n} from './shared';\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\n\nconst EMPTY_PLAYER_ARRAY: TransitionAnimationPlayer[] = [];\nconst NULL_REMOVAL_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false,\n};\nconst NULL_REMOVED_QUERIED_STATE: ElementAnimationState = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true,\n};\n\ninterface TriggerListener {\n  name: string;\n  phase: string;\n  callback: (event: any) => any;\n}\n\ninterface QueueInstruction {\n  element: any;\n  triggerName: string;\n  fromState: StateValue;\n  toState: StateValue;\n  transition: AnimationTransitionFactory;\n  player: TransitionAnimationPlayer;\n  isFallbackTransition: boolean;\n}\n\nconst REMOVAL_FLAG = '__ng_removed';\n\ninterface ElementAnimationState {\n  setForRemoval: boolean;\n  setForMove: boolean;\n  hasAnimation: boolean;\n  namespaceId: string;\n  removedBeforeQueried: boolean;\n  previousTriggersValues?: Map<string, string>;\n}\n\nclass StateValue {\n  public value: string;\n  public options: AnimationOptions;\n\n  get params(): {[key: string]: any} {\n    return this.options.params as {[key: string]: any};\n  }\n\n  constructor(\n    input: any,\n    public namespaceId: string = '',\n  ) {\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {value, ...options} = input;\n      this.options = options as AnimationOptions;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n\n  absorbOptions(options: AnimationOptions) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params!;\n      Object.keys(newParams).forEach((prop) => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\n\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /* @__PURE__ */ new StateValue(VOID_VALUE);\n\nclass AnimationTransitionNamespace {\n  public players: TransitionAnimationPlayer[] = [];\n\n  private _triggers = new Map<string, AnimationTrigger>();\n  private _queue: QueueInstruction[] = [];\n\n  private _elementListeners = new Map<any, TriggerListener[]>();\n\n  private _hostClassName: string;\n\n  constructor(\n    public id: string,\n    public hostElement: any,\n    private _engine: TransitionAnimationEngine,\n  ) {\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n\n  listen(element: any, name: string, phase: string, callback: (event: any) => boolean): () => any {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {name, phase, callback};\n    listeners.push(data);\n\n    const triggersWithStates = getOrSetDefaultValue(\n      this._engine.statesByElement,\n      element,\n      new Map<string, StateValue>(),\n    );\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n\n  register(name: string, ast: AnimationTrigger): boolean {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n\n  private _getTrigger(name: string) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n\n  trigger(\n    element: any,\n    triggerName: string,\n    value: any,\n    defaultToFallback: boolean = true,\n  ): TransitionAnimationPlayer | undefined {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(\n        element,\n        (triggersWithStates = new Map<string, StateValue>()),\n      );\n    }\n\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates.set(triggerName, toState);\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n\n    const isRemoval = toState.value === VOID_VALUE;\n\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors: Error[] = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n\n    const playersOnElement: TransitionAnimationPlayer[] = getOrSetDefaultValue(\n      this._engine.playersByElement,\n      element,\n      [],\n    );\n    playersOnElement.forEach((player) => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n\n    let transition = trigger.matchTransition(\n      fromState.value,\n      toState.value,\n      element,\n      toState.params,\n    );\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition,\n    });\n\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n\n    this.players.push(player);\n    playersOnElement.push(player);\n\n    return player;\n  }\n\n  deregister(name: string) {\n    this._triggers.delete(name);\n\n    this._engine.statesByElement.forEach((stateMap) => stateMap.delete(name));\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(\n        element,\n        listeners.filter((entry) => {\n          return entry.name != name;\n        }),\n      );\n    });\n  }\n\n  clearElementCache(element: any) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach((player) => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n\n  private _signalRemovalForInnerTriggers(rootElement: any, context: any) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach((elm) => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach((ns) => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() =>\n      elements.forEach((elm) => this.clearElementCache(elm)),\n    );\n  }\n\n  triggerLeaveAnimation(\n    element: any,\n    context: any,\n    destroyAfterComplete?: boolean,\n    defaultToFallback?: boolean,\n  ): boolean {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map<string, string>();\n    if (triggerStates) {\n      const players: TransitionAnimationPlayer[] = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  prepareLeaveAnimationListeners(element: any) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set<string>();\n      listeners.forEach((listener) => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const trigger = this._triggers.get(triggerName)!;\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true,\n        });\n      });\n    }\n  }\n\n  removeNode(element: any, context: any): void {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length\n        ? engine.playersByQueriedElement.get(element)\n        : [];\n\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while ((parent = parent.parentNode)) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n\n  insertNode(element: any, parent: any): void {\n    addClass(element, this._hostClassName);\n  }\n\n  drainQueuedTransitions(microtaskId: number): QueueInstruction[] {\n    const instructions: QueueInstruction[] = [];\n    this._queue.forEach((entry) => {\n      const player = entry.player;\n      if (player.destroyed) return;\n\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach((listener: TriggerListener) => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(\n              element,\n              entry.triggerName,\n              entry.fromState.value,\n              entry.toState.value,\n            );\n            (baseEvent as any)['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n\n  destroy(context: any) {\n    this.players.forEach((p) => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\n\ninterface QueuedTransition {\n  element: any;\n  instruction: AnimationTransitionInstruction;\n  player: TransitionAnimationPlayer;\n}\n\nexport class TransitionAnimationEngine {\n  public players: TransitionAnimationPlayer[] = [];\n  public newHostElements = new Map<any, AnimationTransitionNamespace>();\n  public playersByElement = new Map<any, TransitionAnimationPlayer[]>();\n  public playersByQueriedElement = new Map<any, TransitionAnimationPlayer[]>();\n  public statesByElement = new Map<any, Map<string, StateValue>>();\n  public disabledNodes = new Set<any>();\n\n  public totalAnimations = 0;\n  public totalQueuedPlayers = 0;\n\n  private _namespaceLookup: {[id: string]: AnimationTransitionNamespace} = {};\n  private _namespaceList: AnimationTransitionNamespace[] = [];\n  private _flushFns: (() => any)[] = [];\n  private _whenQuietFns: (() => any)[] = [];\n\n  public namespacesByHostElement = new Map<any, AnimationTransitionNamespace>();\n  public collectedEnterElements: any[] = [];\n  public collectedLeaveElements: any[] = [];\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  /** @internal */\n  _onRemovalComplete(element: any, context: any) {\n    this.onRemovalComplete(element, context);\n  }\n\n  constructor(\n    public bodyNode: any,\n    public driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {}\n\n  get queuedPlayers(): TransitionAnimationPlayer[] {\n    const players: TransitionAnimationPlayer[] = [];\n    this._namespaceList.forEach((ns) => {\n      ns.players.forEach((player) => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n\n  createNamespace(namespaceId: string, hostElement: any) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return (this._namespaceLookup[namespaceId] = ns);\n  }\n\n  private _balanceNamespaceList(ns: AnimationTransitionNamespace, hostElement: any) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n\n  registerTrigger(namespaceId: string, name: string, trigger: AnimationTrigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n\n  destroy(namespaceId: string, context: any) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n\n  private _fetchNamespace(id: string) {\n    return this._namespaceLookup[id];\n  }\n\n  fetchNamespacesByElement(element: any): Set<AnimationTransitionNamespace> {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set<AnimationTransitionNamespace>();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n\n  trigger(namespaceId: string, element: any, name: string, value: any): boolean {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n\n  insertNode(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    if (!isElementNode(element)) return;\n\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n\n  collectEnterElement(element: any) {\n    this.collectedEnterElements.push(element);\n  }\n\n  markElementAsDisabled(element: any, value: boolean) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n\n  removeNode(namespaceId: string, element: any, context: any): void {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n\n  markElementAsRemoved(\n    namespaceId: string,\n    element: any,\n    hasAnimation?: boolean,\n    context?: any,\n    previousTriggersValues?: Map<string, string>,\n  ) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues,\n    };\n  }\n\n  listen(\n    namespaceId: string,\n    element: any,\n    name: string,\n    phase: string,\n    callback: (event: any) => boolean,\n  ): () => any {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n\n  private _buildInstruction(\n    entry: QueueInstruction,\n    subTimelines: ElementInstructionMap,\n    enterClassName: string,\n    leaveClassName: string,\n    skipBuildAst?: boolean,\n  ) {\n    return entry.transition.build(\n      this.driver,\n      entry.element,\n      entry.fromState.value,\n      entry.toState.value,\n      enterClassName,\n      leaveClassName,\n      entry.fromState.options,\n      entry.toState.options,\n      subTimelines,\n      skipBuildAst,\n    );\n  }\n\n  destroyInnerAnimations(containerElement: any) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach((element) => this.destroyActiveAnimationsForElement(element));\n\n    if (this.playersByQueriedElement.size == 0) return;\n\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach((element) => this.finishActiveQueriedAnimationOnElement(element));\n  }\n\n  destroyActiveAnimationsForElement(element: any) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach((player) => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n\n  finishActiveQueriedAnimationOnElement(element: any) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach((player) => player.finish());\n    }\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return new Promise<void>((resolve) => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n\n  processLeaveNode(element: any) {\n    const details = element[REMOVAL_FLAG] as ElementAnimationState;\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach((node) => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n\n  flush(microtaskId: number = -1) {\n    let players: AnimationPlayer[] = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n\n    if (\n      this._namespaceList.length &&\n      (this.totalQueuedPlayers || this.collectedLeaveElements.length)\n    ) {\n      const cleanupFns: Function[] = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach((fn) => fn());\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach((fn) => fn());\n        });\n      } else {\n        quietFns.forEach((fn) => fn());\n      }\n    }\n  }\n\n  reportError(errors: Error[]) {\n    throw triggerTransitionsFailed(errors);\n  }\n\n  private _flushAnimations(\n    cleanupFns: Function[],\n    microtaskId: number,\n  ): TransitionAnimationPlayer[] {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers: TransitionAnimationPlayer[] = [];\n    const skippedPlayersMap = new Map<any, AnimationPlayer[]>();\n    const queuedInstructions: QueuedTransition[] = [];\n    const queriedElements = new Map<any, TransitionAnimationPlayer[]>();\n    const allPreStyleElements = new Map<any, Set<string>>();\n    const allPostStyleElements = new Map<any, Set<string>>();\n\n    const disabledElementsSet = new Set<any>();\n    this.disabledNodes.forEach((node) => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map<any, string>();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach((node) => addClass(node, className));\n    });\n\n    const allLeaveNodes: any[] = [];\n    const mergedLeaveNodes = new Set<any>();\n    const leaveNodesWithoutAnimations = new Set<any>();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver\n            .query(element, STAR_SELECTOR, true)\n            .forEach((elm) => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n\n    const leaveNodeMapIds = new Map<any, string>();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach((node) => addClass(node, className));\n    });\n\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root)!;\n        nodes.forEach((node) => removeClass(node, className));\n      });\n\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root)!;\n        nodes.forEach((node) => removeClass(node, className));\n      });\n\n      allLeaveNodes.forEach((element) => {\n        this.processLeaveNode(element);\n      });\n    });\n\n    const allPlayers: TransitionAnimationPlayer[] = [];\n    const erroneousTransitions: AnimationTransitionInstruction[] = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach((entry) => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG] as ElementAnimationState;\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (\n              details.previousTriggersValues &&\n              details.previousTriggersValues.has(entry.triggerName)\n            ) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName) as string;\n\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName)!;\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n\n            player.destroy();\n            return;\n          }\n        }\n\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element)!;\n        const enterClassName = enterNodeMapIds.get(element)!;\n        const instruction = this._buildInstruction(\n          entry,\n          subTimelines,\n          enterClassName,\n          leaveClassName,\n          nodeIsOrphaned,\n        )!;\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines: AnimationTimelineInstruction[] = [];\n        instruction.timelines.forEach((tl) => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n\n        subTimelines.append(element, instruction.timelines);\n\n        const tuple = {instruction, player, element};\n\n        queuedInstructions.push(tuple);\n\n        instruction.queriedElements.forEach((element) =>\n          getOrSetDefaultValue(queriedElements, element, []).push(player),\n        );\n\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal: Set<string> = allPreStyleElements.get(element)!;\n            if (!setVal) {\n              allPreStyleElements.set(element, (setVal = new Set<string>()));\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal: Set<string> = allPostStyleElements.get(element)!;\n          if (!setVal) {\n            allPostStyleElements.set(element, (setVal = new Set<string>()));\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n\n    if (erroneousTransitions.length) {\n      const errors: Error[] = [];\n      erroneousTransitions.forEach((instruction) => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors!));\n      });\n\n      allPlayers.forEach((player) => player.destroy());\n      this.reportError(errors);\n    }\n\n    const allPreviousPlayersMap = new Map<any, TransitionAnimationPlayer[]>();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map<any, any>();\n    queuedInstructions.forEach((entry) => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(\n          entry.player.namespaceId,\n          entry.instruction,\n          allPreviousPlayersMap,\n        );\n      }\n    });\n\n    skippedPlayers.forEach((player) => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(\n        element,\n        false,\n        player.namespaceId,\n        player.triggerName,\n        null,\n      );\n      previousPlayers.forEach((prevPlayer) => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter((node) => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map<any, ɵStyleDataMap>();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(\n      postStylesMap,\n      this.driver,\n      leaveNodesWithoutAnimations,\n      allPostStyleElements,\n      AUTO_STYLE,\n    );\n\n    allLeaveQueriedNodes.forEach((node) => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map<any, ɵStyleDataMap>();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(\n        preStylesMap,\n        this.driver,\n        new Set(nodes),\n        allPreStyleElements,\n        PRE_STYLE,\n      );\n    });\n\n    replaceNodes.forEach((node) => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n\n    const rootPlayers: TransitionAnimationPlayer[] = [];\n    const subPlayers: TransitionAnimationPlayer[] = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach((entry) => {\n      const {element, player, instruction} = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation: any = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd: any[] = [];\n          while ((elm = elm.parentNode)) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach((parent) => animationElementMap.set(parent, parentWithAnimation));\n        }\n\n        const innerPlayer = this._buildAnimation(\n          player.namespaceId,\n          instruction,\n          allPreviousPlayersMap,\n          skippedPlayersMap,\n          preStylesMap,\n          postStylesMap,\n        );\n\n        player.setRealPlayer(innerPlayer);\n\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach((player) => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach((player) => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG] as ElementAnimationState;\n      removeClass(element, LEAVE_CLASSNAME);\n\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n\n      let players: TransitionAnimationPlayer[] = [];\n\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n\n      const activePlayers = players.filter((p) => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n\n    rootPlayers.forEach((player) => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n\n    return rootPlayers;\n  }\n\n  afterFlush(callback: () => any) {\n    this._flushFns.push(callback);\n  }\n\n  afterFlushAnimationsDone(callback: () => any) {\n    this._whenQuietFns.push(callback);\n  }\n\n  private _getPreviousPlayers(\n    element: string,\n    isQueriedElement: boolean,\n    namespaceId?: string,\n    triggerName?: string,\n    toStateValue?: any,\n  ): TransitionAnimationPlayer[] {\n    let players: TransitionAnimationPlayer[] = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach((player) => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter((player) => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n\n  private _beforeAnimationBuild(\n    namespaceId: string,\n    instruction: AnimationTransitionInstruction,\n    allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n  ) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId: string | undefined = instruction.isRemovalTransition\n      ? undefined\n      : namespaceId;\n    const targetTriggerName: string | undefined = instruction.isRemovalTransition\n      ? undefined\n      : triggerName;\n\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(\n        element,\n        isQueriedElement,\n        targetNameSpaceId,\n        targetTriggerName,\n        instruction.toState,\n      );\n      previousPlayers.forEach((player) => {\n        const realPlayer = (player as TransitionAnimationPlayer).getRealPlayer() as any;\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n\n  private _buildAnimation(\n    namespaceId: string,\n    instruction: AnimationTransitionInstruction,\n    allPreviousPlayersMap: Map<any, TransitionAnimationPlayer[]>,\n    skippedPlayersMap: Map<any, AnimationPlayer[]>,\n    preStylesMap: Map<any, ɵStyleDataMap>,\n    postStylesMap: Map<any, ɵStyleDataMap>,\n  ): AnimationPlayer {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers: TransitionAnimationPlayer[] = [];\n    const allConsumedElements = new Set<any>();\n    const allSubElements = new Set<any>();\n    const allNewPlayers = instruction.timelines.map((timelineInstruction) => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried)\n        return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers(\n        (allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map((p) => p.getRealPlayer()),\n      ).filter((p) => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p as any;\n        return pp.element ? pp.element === element : false;\n      });\n\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n\n      const keyframes = normalizeKeyframes(\n        this._normalizer,\n        timelineInstruction.keyframes,\n        preStyles,\n        postStyles,\n      );\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n\n    allQueriedPlayers.forEach((player) => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n\n    allConsumedElements.forEach((element) => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach((element) => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach((element) => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n\n    return player;\n  }\n\n  private _buildPlayer(\n    instruction: AnimationTimelineInstruction,\n    keyframes: Array<ɵStyleDataMap>,\n    previousPlayers: AnimationPlayer[],\n  ): AnimationPlayer {\n    if (keyframes.length > 0) {\n      return this.driver.animate(\n        instruction.element,\n        keyframes,\n        instruction.duration,\n        instruction.delay,\n        instruction.easing,\n        previousPlayers,\n      );\n    }\n\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\n\nexport class TransitionAnimationPlayer implements AnimationPlayer {\n  private _player: AnimationPlayer = new NoopAnimationPlayer();\n  private _containsRealPlayer = false;\n\n  private _queuedCallbacks = new Map<string, ((event: any) => any)[]>();\n  public readonly destroyed = false;\n  public parentPlayer: AnimationPlayer | null = null;\n\n  public markedForDestroy: boolean = false;\n  public disabled = false;\n\n  readonly queued: boolean = true;\n  public readonly totalTime: number = 0;\n\n  constructor(\n    public namespaceId: string,\n    public triggerName: string,\n    public element: any,\n  ) {}\n\n  setRealPlayer(player: AnimationPlayer) {\n    if (this._containsRealPlayer) return;\n\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach((callback) => listenOnPlayer(player, phase, undefined, callback));\n    });\n\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    (this as Writable<this>).queued = false;\n  }\n\n  getRealPlayer() {\n    return this._player;\n  }\n\n  overrideTotalTime(totalTime: number) {\n    (this as any).totalTime = totalTime;\n  }\n\n  syncPlayerEvents(player: AnimationPlayer) {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback!('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n\n  private _queueEvent(name: string, callback: (event: any) => any): void {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n\n  onDone(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n\n  onStart(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n\n  init(): void {\n    this._player.init();\n  }\n\n  hasStarted(): boolean {\n    return this.queued ? false : this._player.hasStarted();\n  }\n\n  play(): void {\n    !this.queued && this._player.play();\n  }\n\n  pause(): void {\n    !this.queued && this._player.pause();\n  }\n\n  restart(): void {\n    !this.queued && this._player.restart();\n  }\n\n  finish(): void {\n    this._player.finish();\n  }\n\n  destroy(): void {\n    (this as {destroyed: boolean}).destroyed = true;\n    this._player.destroy();\n  }\n\n  reset(): void {\n    !this.queued && this._player.reset();\n  }\n\n  setPosition(p: number): void {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n\n  getPosition(): number {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const p = this._player as any;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\n\nfunction deleteOrUnsetInMap<T, V>(map: Map<T, V[]>, key: T, value: V) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\n\nfunction normalizeTriggerValue(value: any): any {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\n\nfunction isElementNode(node: any) {\n  return node && node['nodeType'] === 1;\n}\n\nfunction isTriggerEventValid(eventName: string): boolean {\n  return eventName == 'start' || eventName == 'done';\n}\n\nfunction cloakElement(element: any, value?: string) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n\nfunction cloakAndComputeStyles(\n  valuesMap: Map<any, ɵStyleDataMap>,\n  driver: AnimationDriver,\n  elements: Set<any>,\n  elementPropsMap: Map<any, Set<string>>,\n  defaultStyle: string,\n): any[] {\n  const cloakVals: string[] = [];\n  elements.forEach((element) => cloakVals.push(cloakElement(element)));\n\n  const failedElements: any[] = [];\n\n  elementPropsMap.forEach((props: Set<string>, element: any) => {\n    const styles: ɵStyleDataMap = new Map();\n    props.forEach((prop) => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach((element) => cloakElement(element, cloakVals[i++]));\n\n  return failedElements;\n}\n\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots: any[], nodes: any[]): Map<any, any[]> {\n  const rootMap = new Map<any, any[]>();\n  roots.forEach((root) => rootMap.set(root, []));\n\n  if (nodes.length == 0) return rootMap;\n\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map<any, any>();\n\n  function getRoot(node: any): any {\n    if (!node) return NULL_NODE;\n\n    let root = localRootMap.get(node);\n    if (root) return root;\n\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n\n    localRootMap.set(node, root);\n    return root;\n  }\n\n  nodes.forEach((node) => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root)!.push(node);\n    }\n  });\n\n  return rootMap;\n}\n\nfunction addClass(element: any, className: string) {\n  element.classList?.add(className);\n}\n\nfunction removeClass(element: any, className: string) {\n  element.classList?.remove(className);\n}\n\nfunction removeNodesAfterAnimationDone(\n  engine: TransitionAnimationEngine,\n  element: any,\n  players: AnimationPlayer[],\n) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n\nfunction flattenGroupPlayers(players: AnimationPlayer[]): AnimationPlayer[] {\n  const finalPlayers: AnimationPlayer[] = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\n\nfunction _flattenGroupPlayersRecur(players: AnimationPlayer[], finalPlayers: AnimationPlayer[]) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\n\nfunction objEquals(a: {[key: string]: any}, b: {[key: string]: any}): boolean {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\n\nfunction replacePostStylesAsPre(\n  element: any,\n  allPreStyleElements: Map<any, Set<string>>,\n  allPostStyleElements: Map<any, Set<string>>,\n): boolean {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach((data) => preEntry!.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n\n  allPostStyleElements.delete(element);\n  return true;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationMetadata,\n  AnimationPlayer,\n  AnimationTriggerMetadata,\n} from '../../../src/animations';\n\nimport {TriggerAst} from '../dsl/animation_ast';\nimport {buildAnimationAst} from '../dsl/animation_ast_builder';\nimport {AnimationTrigger, buildTrigger} from '../dsl/animation_trigger';\nimport {AnimationStyleNormalizer} from '../dsl/style_normalization/animation_style_normalizer';\nimport {triggerBuildFailed} from '../error_helpers';\nimport {warnTriggerBuild} from '../warning_helpers';\n\nimport {AnimationDriver} from './animation_driver';\nimport {parseTimelineCommand} from './shared';\nimport {TimelineAnimationEngine} from './timeline_animation_engine';\nimport {TransitionAnimationEngine} from './transition_animation_engine';\n\nexport class AnimationEngine {\n  private _transitionEngine: TransitionAnimationEngine;\n  private _timelineEngine: TimelineAnimationEngine;\n\n  private _triggerCache: {[key: string]: AnimationTrigger} = {};\n\n  // this method is designed to be overridden by the code that uses this engine\n  public onRemovalComplete = (element: any, context: any) => {};\n\n  constructor(\n    doc: Document,\n    private _driver: AnimationDriver,\n    private _normalizer: AnimationStyleNormalizer,\n  ) {\n    this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n\n    this._transitionEngine.onRemovalComplete = (element: any, context: any) =>\n      this.onRemovalComplete(element, context);\n  }\n\n  registerTrigger(\n    componentId: string,\n    namespaceId: string,\n    hostElement: any,\n    name: string,\n    metadata: AnimationTriggerMetadata,\n  ): void {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors: Error[] = [];\n      const warnings: string[] = [];\n      const ast = buildAnimationAst(\n        this._driver,\n        metadata as AnimationMetadata,\n        errors,\n        warnings,\n      ) as TriggerAst;\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnTriggerBuild(name, warnings);\n        }\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n\n  register(namespaceId: string, hostElement: any) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n\n  destroy(namespaceId: string, context: any) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n\n  onInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n\n  onRemove(namespaceId: string, element: any, context: any): void {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n\n  disableAnimations(element: any, disable: boolean) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n\n  process(namespaceId: string, element: any, property: string, value: any) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value as any[];\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n\n  listen(\n    namespaceId: string,\n    element: any,\n    eventName: string,\n    eventPhase: string,\n    callback: (event: any) => any,\n  ): () => any {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n\n  flush(microtaskId: number = -1): void {\n    this._transitionEngine.flush(microtaskId);\n  }\n\n  get players(): AnimationPlayer[] {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return this._transitionEngine.whenRenderingDone();\n  }\n\n  afterFlushAnimationsDone(cb: VoidFunction): void {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {ɵStyleDataMap} from '../../../src/animations';\n\nimport {eraseStyles, setStyles} from '../util';\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nexport function packageNonAnimatableStyles(\n  element: any,\n  styles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n): SpecialCasedStyles | null {\n  let startStyles: ɵStyleDataMap | null = null;\n  let endStyles: ɵStyleDataMap | null = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nexport class SpecialCasedStyles {\n  static initialStylesByElement = /* @__PURE__ */ new WeakMap<any, ɵStyleDataMap>();\n\n  private _state = SpecialCasedStylesState.Pending;\n  private _initialStyles!: ɵStyleDataMap;\n\n  constructor(\n    private _element: any,\n    private _startStyles: ɵStyleDataMap | null,\n    private _endStyles: ɵStyleDataMap | null,\n  ) {\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, (initialStyles = new Map()));\n    }\n    this._initialStyles = initialStyles;\n  }\n\n  start() {\n    if (this._state < SpecialCasedStylesState.Started) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  finish() {\n    this.start();\n    if (this._state < SpecialCasedStylesState.Finished) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = SpecialCasedStylesState.Started;\n    }\n  }\n\n  destroy() {\n    this.finish();\n    if (this._state < SpecialCasedStylesState.Destroyed) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = SpecialCasedStylesState.Destroyed;\n    }\n  }\n}\n\n/**\n * An enum of states reflective of what the status of `SpecialCasedStyles` is.\n *\n * Depending on how `SpecialCasedStyles` is interacted with, the start and end\n * styles may not be applied in the same way. This enum ensures that if and when\n * the ending styles are applied then the starting styles are applied. It is\n * also used to reflect what the current status of the special cased styles are\n * which helps prevent the starting/ending styles not be applied twice. It is\n * also used to cleanup the styles once `SpecialCasedStyles` is destroyed.\n */\nconst enum SpecialCasedStylesState {\n  Pending = 0,\n  Started = 1,\n  Finished = 2,\n  Destroyed = 3,\n}\n\nfunction filterNonAnimatableStyles(styles: ɵStyleDataMap): ɵStyleDataMap | null {\n  let result: ɵStyleDataMap | null = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\n\nfunction isNonAnimatableStyle(prop: string) {\n  return prop === 'display' || prop === 'position';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationPlayer, ɵStyleDataMap} from '../../../../src/animations';\n\nimport {computeStyle} from '../../util';\nimport {SpecialCasedStyles} from '../special_cased_styles';\n\nexport class WebAnimationsPlayer implements AnimationPlayer {\n  private _onDoneFns: Function[] = [];\n  private _onStartFns: Function[] = [];\n  private _onDestroyFns: Function[] = [];\n  private _duration: number;\n  private _delay: number;\n  private _initialized = false;\n  private _finished = false;\n  private _started = false;\n  private _destroyed = false;\n  private _finalKeyframe?: ɵStyleDataMap;\n\n  // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n  // and are used to reset the fns to their original values upon reset()\n  // (since the _onStartFns and _onDoneFns get deleted after they are called)\n  private _originalOnDoneFns: Function[] = [];\n  private _originalOnStartFns: Function[] = [];\n\n  // using non-null assertion because it's re(set) by init();\n  public readonly domPlayer!: Animation;\n  public time = 0;\n\n  public parentPlayer: AnimationPlayer | null = null;\n  public currentSnapshot: ɵStyleDataMap = new Map();\n\n  constructor(\n    public element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public options: {[key: string]: string | number},\n    private _specialStyles?: SpecialCasedStyles | null,\n  ) {\n    this._duration = <number>options['duration'];\n    this._delay = <number>options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n\n  private _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach((fn) => fn());\n      this._onDoneFns = [];\n    }\n  }\n\n  init(): void {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n\n  private _buildPlayer(): void {\n    if (this._initialized) return;\n    this._initialized = true;\n\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n\n  private _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n\n  private _convertKeyframesToObject(keyframes: Array<ɵStyleDataMap>): any[] {\n    const kfs: any[] = [];\n    keyframes.forEach((frame) => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n\n  /** @internal */\n  _triggerWebAnimation(\n    element: HTMLElement,\n    keyframes: Array<ɵStyleDataMap>,\n    options: any,\n  ): Animation {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n\n  onStart(fn: () => void): void {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n\n  onDone(fn: () => void): void {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    this._onDestroyFns.push(fn);\n  }\n\n  play(): void {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach((fn) => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n\n  pause(): void {\n    this.init();\n    this.domPlayer.pause();\n  }\n\n  finish(): void {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n\n  reset(): void {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n\n  private _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n\n  restart(): void {\n    this.reset();\n    this.play();\n  }\n\n  hasStarted(): boolean {\n    return this._started;\n  }\n\n  destroy(): void {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach((fn) => fn());\n      this._onDestroyFns = [];\n    }\n  }\n\n  setPosition(p: number): void {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n\n  getPosition(): number {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n\n  get totalTime(): number {\n    return this._delay + this._duration;\n  }\n\n  beforeDestroy() {\n    const styles: ɵStyleDataMap = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe!;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n\n    this.currentSnapshot = styles;\n  }\n\n  /** @internal */\n  triggerCallback(phaseName: string): void {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach((fn) => fn());\n    methods.length = 0;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationPlayer, ɵStyleDataMap} from '../../../../src/animations';\n\nimport {\n  allowPreviousPlayerStylesMerge,\n  balancePreviousStylesIntoKeyframes,\n  camelCaseToDashCase,\n  computeStyle,\n  normalizeKeyframes,\n} from '../../util';\nimport {AnimationDriver} from '../animation_driver';\nimport {\n  containsElement,\n  getParentElement,\n  invokeQuery,\n  validateStyleProperty,\n  validateWebAnimatableStyleProperty,\n} from '../shared';\nimport {packageNonAnimatableStyles} from '../special_cased_styles';\n\nimport {WebAnimationsPlayer} from './web_animations_player';\n\nexport class WebAnimationsDriver implements AnimationDriver {\n  validateStyleProperty(prop: string): boolean {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n\n  validateAnimatableStyleProperty(prop: string): boolean {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return computeStyle(element, prop);\n  }\n\n  animate(\n    element: any,\n    keyframes: Array<Map<string, string | number>>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: AnimationPlayer[] = [],\n  ): AnimationPlayer {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions: {[key: string]: string | number} = {duration, delay, fill};\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const previousStyles: ɵStyleDataMap = new Map();\n    const previousWebAnimationPlayers = <WebAnimationsPlayer[]>(\n      previousPlayers.filter((player) => player instanceof WebAnimationsPlayer)\n    );\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach((player) => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n\n    let _keyframes = normalizeKeyframes(keyframes).map((styles) => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NoopAnimationStyleNormalizer} from './dsl/style_normalization/animation_style_normalizer';\nimport {WebAnimationsStyleNormalizer} from './dsl/style_normalization/web_animations_style_normalizer';\nimport {NoopAnimationDriver} from './render/animation_driver';\nimport {AnimationEngine} from './render/animation_engine_next';\nimport {WebAnimationsDriver} from './render/web_animations/web_animations_driver';\n\nexport function createEngine(type: 'animations' | 'noop', doc: Document): AnimationEngine {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n  }\n\n  return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  ɵStyleDataMap,\n} from '../../../src/animations';\n\nimport {buildingFailed, validationFailed} from '../error_helpers';\nimport {AnimationDriver} from '../render/animation_driver';\nimport {ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles} from '../util';\nimport {warnValidation} from '../warning_helpers';\n\nimport {Ast} from './animation_ast';\nimport {buildAnimationAst} from './animation_ast_builder';\nimport {buildAnimationTimelines} from './animation_timeline_builder';\nimport {AnimationTimelineInstruction} from './animation_timeline_instruction';\nimport {ElementInstructionMap} from './element_instruction_map';\n\nexport class Animation {\n  private _animationAst: Ast<AnimationMetadataType>;\n  constructor(\n    private _driver: AnimationDriver,\n    input: AnimationMetadata | AnimationMetadata[],\n  ) {\n    const errors: Error[] = [];\n    const warnings: string[] = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (warnings.length) {\n        warnValidation(warnings);\n      }\n    }\n    this._animationAst = ast;\n  }\n\n  buildTimelines(\n    element: any,\n    startingStyles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n    destinationStyles: ɵStyleDataMap | Array<ɵStyleDataMap>,\n    options: AnimationOptions,\n    subInstructions?: ElementInstructionMap,\n  ): AnimationTimelineInstruction[] {\n    const start = Array.isArray(startingStyles)\n      ? normalizeStyles(startingStyles)\n      : <ɵStyleDataMap>startingStyles;\n    const dest = Array.isArray(destinationStyles)\n      ? normalizeStyles(destinationStyles)\n      : <ɵStyleDataMap>destinationStyles;\n    const errors: any = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(\n      this._driver,\n      element,\n      this._animationAst,\n      ENTER_CLASSNAME,\n      LEAVE_CLASSNAME,\n      start,\n      dest,\n      options,\n      subInstructions,\n      errors,\n    );\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\n\nimport {\n  Renderer2,\n  RendererFactory2,\n  RendererStyleFlags2,\n  ɵAnimationRendererType as AnimationRendererType,\n  type ListenerOptions,\n} from '@angular/core';\nimport type {AnimationEngine} from './animation_engine_next';\n\ntype AnimationFactoryWithListenerCallback = RendererFactory2 & {\n  scheduleListenerCallback: (count: number, fn: (e: any) => any, data: any) => void;\n};\n\nexport class BaseAnimationRenderer implements Renderer2 {\n  // We need to explicitly type this property because of an api-extractor bug\n  // See https://github.com/microsoft/rushstack/issues/4390\n  readonly ɵtype: AnimationRendererType.Regular = AnimationRendererType.Regular;\n\n  constructor(\n    protected namespaceId: string,\n    public delegate: Ren<PERSON>er2,\n    public engine: <PERSON><PERSON><PERSON><PERSON>,\n    private _onDestroy?: () => void,\n  ) {}\n\n  get data() {\n    return this.delegate.data;\n  }\n\n  destroyNode(node: any): void {\n    this.delegate.destroyNode?.(node);\n  }\n\n  destroy(): void {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n\n    this._onDestroy?.();\n  }\n\n  createElement(name: string, namespace?: string | null | undefined) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value: string) {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value: string) {\n    return this.delegate.createText(value);\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any, isMove: boolean = true): void {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n\n  removeChild(parent: any, oldChild: any, isHostElement?: boolean): void {\n    // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n    // doesn't have a parent. There appears to be some animation-specific downstream logic that\n    // depends on the null check happening before the animation engine. This check keeps the old\n    // behavior while allowing `core` to not have to check for the parent element anymore.\n    if (this.parentNode(oldChild)) {\n      this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n  }\n\n  selectRootElement(selectorOrNode: any, preserveContent?: boolean) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node: any) {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node: any) {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string | null | undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string | null | undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el: any, name: string): void {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el: any, name: string): void {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el: any, style: string, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  setValue(node: any, value: string): void {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(\n    target: any,\n    eventName: string,\n    callback: (event: any) => boolean | void,\n    options?: ListenerOptions,\n  ): () => void {\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n\n  protected disableAnimations(element: any, value: boolean) {\n    this.engine.disableAnimations(element, value);\n  }\n}\n\nexport class AnimationRenderer extends BaseAnimationRenderer implements Renderer2 {\n  constructor(\n    public factory: AnimationFactoryWithListenerCallback,\n    namespaceId: string,\n    delegate: Renderer2,\n    engine: AnimationEngine,\n    onDestroy?: () => void,\n  ) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.namespaceId = namespaceId;\n  }\n\n  override setProperty(el: any, name: string, value: any): void {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value as boolean);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  override listen(\n    target: 'window' | 'document' | 'body' | any,\n    eventName: string,\n    callback: (event: any) => any,\n    options?: ListenerOptions,\n  ): () => void {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, (event) => {\n        const countId = (event as any)['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n}\n\nfunction resolveElementFromTarget(target: 'window' | 'document' | 'body' | any): any {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\n\nfunction parseTriggerCallbackName(triggerName: string) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {AnimationTriggerMetadata} from '../../../src/animations';\nimport type {NgZone, Renderer2, RendererFactory2, RendererType2} from '@angular/core';\n\nimport {AnimationEngine} from './animation_engine_next';\nimport {AnimationRenderer, BaseAnimationRenderer} from './renderer';\n\n// Define a recursive type to allow for nested arrays of `AnimationTriggerMetadata`. Note that an\n// interface declaration is used as TypeScript prior to 3.7 does not support recursive type\n// references, see https://github.com/microsoft/TypeScript/pull/33050 for details.\ntype NestedAnimationTriggerMetadata = AnimationTriggerMetadata | RecursiveAnimationTriggerMetadata;\ninterface RecursiveAnimationTriggerMetadata extends Array<NestedAnimationTriggerMetadata> {}\n\nexport class AnimationRendererFactory implements RendererFactory2 {\n  private _currentId: number = 0;\n  private _microtaskId: number = 1;\n  private _animationCallbacksBuffer: [(e: any) => any, any][] = [];\n  private _rendererCache = new Map<Renderer2, BaseAnimationRenderer>();\n  private _cdRecurDepth = 0;\n\n  constructor(\n    private delegate: RendererFactory2,\n    private engine: AnimationEngine,\n    private _zone: NgZone,\n  ) {\n    engine.onRemovalComplete = (element: any, delegate: Renderer2 | null) => {\n      delegate?.removeChild(null, element);\n    };\n  }\n\n  createRenderer(hostElement: any, type: RendererType2): BaseAnimationRenderer {\n    const EMPTY_NAMESPACE_ID = '';\n\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer: BaseAnimationRenderer | undefined = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(\n          EMPTY_NAMESPACE_ID,\n          delegate,\n          this.engine,\n          onRendererDestroy,\n        );\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n\n    this.engine.register(namespaceId, hostElement);\n\n    const registerTrigger = (trigger: NestedAnimationTriggerMetadata) => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'] as NestedAnimationTriggerMetadata[];\n    animationTriggers.forEach(registerTrigger);\n\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n\n  private _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n\n  /** @internal */\n  scheduleListenerCallback(count: number, fn: (e: any) => any, data: any) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach((tuple) => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n\n  end() {\n    this._cdRecurDepth--;\n\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n\n  whenRenderingDone(): Promise<any> {\n    return this.engine.whenRenderingDone();\n  }\n\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  protected componentReplaced(componentId: string) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this.engine.flush();\n    (this.delegate as {componentReplaced?: (id: string) => void}).componentReplaced?.(componentId);\n  }\n}\n"], "names": ["PRE_STYLE", "normalizeKeyframes"], "mappings": ";;;;;;;;;;;AAYA;;;;AAIG;MAEU,mBAAmB,CAAA;AAC9B;;AAEG;AACH,IAAA,qBAAqB,CAAC,IAAY,EAAA;AAChC,QAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC;;AAGpC;;;AAGG;IACH,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;;AAGpC;;AAEG;AACH,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC;;AAGlC;;;AAGG;AACH,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAG9C;;AAEG;AACH,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;QAC5D,OAAO,YAAY,IAAI,EAAE;;AAG3B;;AAEG;AACH,IAAA,OAAO,CACL,OAAY,EACZ,SAA8C,EAC9C,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAyB,GAAA,EAAE,EAC3B,uBAAiC,EAAA;AAEjC,QAAA,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;;kHAlDtC,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAnB,mBAAmB,EAAA,CAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B;;AAuDD;;AAEG;MACmB,eAAe,CAAA;AACnC;;AAEG;AACH,IAAA,OAAO,IAAI,GAAoC,IAAI,mBAAmB,EAAE;;;MCvEpD,wBAAwB,CAAA;AAQ7C;MAEY,4BAA4B,CAAA;IACvC,qBAAqB,CAAC,YAAoB,EAAE,MAAe,EAAA;AACzD,QAAA,OAAO,YAAY;;AAGrB,IAAA,mBAAmB,CACjB,oBAA4B,EAC5B,kBAA0B,EAC1B,KAAsB,EACtB,MAAe,EAAA;AAEf,QAAA,OAAY,KAAK;;AAEpB;;ACnBD,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IACnC,OAAO;IACP,QAAQ;IACR,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,UAAU;IACV,cAAc;IACd,eAAe;IACf,YAAY;IACZ,aAAa;IACb,eAAe;IACf,cAAc;IACd,WAAW;IACX,YAAY;IACZ,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,aAAa;AACd,CAAA,CAAC;AAEI,MAAO,4BAA6B,SAAQ,wBAAwB,CAAA;IAC/D,qBAAqB,CAAC,YAAoB,EAAE,MAAe,EAAA;AAClE,QAAA,OAAO,mBAAmB,CAAC,YAAY,CAAC;;AAGjC,IAAA,mBAAmB,CAC1B,oBAA4B,EAC5B,kBAA0B,EAC1B,KAAsB,EACtB,MAAe,EAAA;QAEf,IAAI,IAAI,GAAW,EAAE;QACrB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE;AAEtC,QAAA,IAAI,oBAAoB,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;AAChF,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,GAAG,IAAI;;iBACN;gBACL,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC;gBAC/D,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;oBACzD,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;;;;QAInE,OAAO,MAAM,GAAG,IAAI;;AAEvB;;AC9DD,SAAS,oBAAoB,CAAC,QAAkB,EAAA;IAC9C,MAAM,UAAU,GAAG,OAAO;IAC1B,OAAO,CAAA,EAAG,UAAU,CAAA,EAAG;SACpB,MAAM,CAAC,OAAO;AACd,SAAA,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO;AACxB,SAAA,IAAI,CAAC,UAAU,CAAC,CAAA,CAAE;AACvB;AAEM,SAAU,cAAc,CAAC,QAAkB,EAAA;IAC/C,OAAO,CAAC,IAAI,CAAC,CAAiC,8BAAA,EAAA,oBAAoB,CAAC,QAAQ,CAAC,CAAE,CAAA,CAAC;AACjF;AAEgB,SAAA,gBAAgB,CAAC,IAAY,EAAE,QAAkB,EAAA;AAC/D,IAAA,OAAO,CAAC,IAAI,CACV,CAAA,uBAAA,EAA0B,IAAI,CAAA,wCAAA,EAA2C,oBAAoB,CAC3F,QAAQ,CACT,CAAE,CAAA,CACJ;AACH;AAEM,SAAU,YAAY,CAAC,QAAkB,EAAA;IAC7C,OAAO,CAAC,IAAI,CAAC,CAA+C,4CAAA,EAAA,oBAAoB,CAAC,QAAQ,CAAC,CAAE,CAAA,CAAC;AAC/F;AAUgB,SAAA,iCAAiC,CAAC,QAAkB,EAAE,KAAe,EAAA;AACnF,IAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,QAAA,QAAQ,CAAC,IAAI,CAAC,CAAA,sDAAA,EAAyD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAAC;;AAE9F;;AClCO,MAAM,SAAS,GAAG,GAAG;AAQZ,SAAA,mBAAmB,CACjC,eAA6C,EAC7C,MAAe,EAAA;IAEf,MAAM,WAAW,GAA0B,EAAE;AAC7C,IAAA,IAAI,OAAO,eAAe,IAAI,QAAQ,EAAE;QACtC;aACG,KAAK,CAAC,SAAS;AACf,aAAA,OAAO,CAAC,CAAC,GAAG,KAAK,uBAAuB,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;;SACjE;AACL,QAAA,WAAW,CAAC,IAAI,CAAsB,eAAe,CAAC;;AAExD,IAAA,OAAO,WAAW;AACpB;AAEA,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,WAAkC,EAClC,MAAe,EAAA;AAEf,IAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QACtB,MAAM,MAAM,GAAG,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC;AACpD,QAAA,IAAI,OAAO,MAAM,IAAI,UAAU,EAAE;AAC/B,YAAA,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB;;QAEF,QAAQ,GAAG,MAAM;;IAGnB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC;IACvE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACxC,QAAA,OAAO,WAAW;;AAGpB,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1B,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1B,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;IACxB,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAE1D,MAAM,kBAAkB,GAAG,SAAS,IAAI,SAAS,IAAI,OAAO,IAAI,SAAS;IACzE,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE;QAC9C,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;;IAE5D;AACF;AAEA,SAAS,mBAAmB,CAAC,KAAa,EAAE,MAAe,EAAA;IACzD,QAAQ,KAAK;AACX,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,WAAW;AACpB,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,WAAW;AACpB,QAAA,KAAK,YAAY;AACf,YAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAc,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC;AAC/F,QAAA,KAAK,YAAY;AACf,YAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAc,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC;AAC/F,QAAA;YACE,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC1C,YAAA,OAAO,QAAQ;;AAErB;AAEA;AACA;AACA;AACA;AACA,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC1D,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAE5D,SAAS,oBAAoB,CAAC,GAAW,EAAE,GAAW,EAAA;AACpD,IAAA,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC;AACvF,IAAA,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC;AAEvF,IAAA,OAAO,CAAC,SAAc,EAAE,OAAY,KAAa;QAC/C,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS;QACnD,IAAI,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,OAAO;QAEjD,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;YACpE,QAAQ,GAAG,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC;;QAErF,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;YAClE,QAAQ,GAAG,OAAO,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC;;QAGnF,OAAO,QAAQ,IAAI,QAAQ;AAC7B,KAAC;AACH;;AC1BA,MAAM,UAAU,GAAG,OAAO;AAC1B,MAAM,gBAAgB,mBAAmB,IAAI,MAAM,CAAC,CAAA,EAAA,EAAK,UAAU,CAAA,IAAA,CAAM,EAAE,GAAG,CAAC;AAE/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;AACG,SAAU,iBAAiB,CAC/B,MAAuB,EACvB,QAAiD,EACjD,MAAe,EACf,QAAkB,EAAA;AAElB,IAAA,OAAO,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjF;AAEA,MAAM,aAAa,GAAG,EAAE;MAEX,0BAA0B,CAAA;AACjB,IAAA,OAAA;AAApB,IAAA,WAAA,CAAoB,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO;;AAE3B,IAAA,KAAK,CACH,QAAiD,EACjD,MAAe,EACf,QAAkB,EAAA;AAElB,QAAA,MAAM,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,CAAC;AACtD,QAAA,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;AAC3C,QAAA,MAAM,GAAG,IACP,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAC/D;AAED,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,OAAO,CAAC,6BAA6B,CAAC,IAAI,EAAE;gBAC9C,iCAAiC,CAAC,QAAQ,EAAE;AAC1C,oBAAA,GAAG,OAAO,CAAC,6BAA6B,CAAC,IAAI,EAAE;AAChD,iBAAA,CAAC;;;AAIN,QAAA,OAAO,GAAG;;AAGJ,IAAA,6BAA6B,CAAC,OAAmC,EAAA;AACvE,QAAA,OAAO,CAAC,oBAAoB,GAAG,aAAa;AAC5C,QAAA,OAAO,CAAC,eAAe,GAAG,IAAI,GAAG,EAAuC;QACxE,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,GAAG,EAAE,CAAC;AACrD,QAAA,OAAO,CAAC,WAAW,GAAG,CAAC;;IAGzB,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;QAEnC,IAAI,UAAU,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;QACzC,IAAI,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrC,MAAM,MAAM,GAAe,EAAE;QAC7B,MAAM,WAAW,GAAoB,EAAE;QACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;;QAGvC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACnC,YAAA,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;YAC3C,IAAI,GAAG,CAAC,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;gBAC3C,MAAM,QAAQ,GAAG,GAA6B;AAC9C,gBAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI;gBAC1B;AACG,qBAAA,QAAQ;qBACR,KAAK,CAAC,SAAS;AACf,qBAAA,OAAO,CAAC,CAAC,CAAC,KAAI;AACb,oBAAA,QAAQ,CAAC,IAAI,GAAG,CAAC;AACjB,oBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjD,iBAAC,CAAC;AACJ,gBAAA,QAAQ,CAAC,IAAI,GAAG,IAAI;;iBACf,IAAI,GAAG,CAAC,IAAI,IAAI,qBAAqB,CAAC,UAAU,EAAE;gBACvD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAkC,EAAE,OAAO,CAAC;AACpF,gBAAA,UAAU,IAAI,UAAU,CAAC,UAAU;AACnC,gBAAA,QAAQ,IAAI,UAAU,CAAC,QAAQ;AAC/B,gBAAA,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;;iBACvB;gBACL,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;;AAE5C,SAAC,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;YACnC,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM;YACN,WAAW;YACX,UAAU;YACV,QAAQ;AACR,YAAA,OAAO,EAAE,IAAI;SACd;;IAGH,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;AAC1D,QAAA,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI;AACvE,QAAA,IAAI,QAAQ,CAAC,qBAAqB,EAAE;AAClC,YAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU;AACrC,YAAA,MAAM,MAAM,GAAG,SAAS,IAAI,EAAE;YAC9B,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AAChC,gBAAA,IAAI,KAAK,YAAY,GAAG,EAAE;AACxB,oBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;wBACtB,kBAAkB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;4BACxC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC/B,gCAAA,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;;AAExB,yBAAC,CAAC;AACJ,qBAAC,CAAC;;AAEN,aAAC,CAAC;AACF,YAAA,IAAI,WAAW,CAAC,IAAI,EAAE;gBACpB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;;QAI/E,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,IAAI,EAAE,QAAQ,CAAC,IAAI;AACnB,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,OAAO,EAAE,SAAS,GAAG,EAAC,MAAM,EAAE,SAAS,EAAC,GAAG,IAAI;SAChD;;IAGH,eAAe,CACb,QAAqC,EACrC,OAAmC,EAAA;AAEnC,QAAA,OAAO,CAAC,UAAU,GAAG,CAAC;AACtB,QAAA,OAAO,CAAC,QAAQ,GAAG,CAAC;AACpB,QAAA,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;AAC1F,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC;QAEnE,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,UAAU;YACtC,QAAQ;YACR,SAAS;YACT,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC1B,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,aAAa,CACX,QAAmC,EACnC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,QAAQ;YACpC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAChE,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW;QACvC,IAAI,YAAY,GAAG,CAAC;QACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;AACxC,YAAA,OAAO,CAAC,WAAW,GAAG,WAAW;YACjC,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;YAClD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC;AAC1D,YAAA,OAAO,QAAQ;AACjB,SAAC,CAAC;AAEF,QAAA,OAAO,CAAC,WAAW,GAAG,YAAY;QAClC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,KAAK;AACL,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;AAEnC,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC;AACtE,QAAA,OAAO,CAAC,qBAAqB,GAAG,SAAS;AACzC,QAAA,IAAI,QAAiC;AACrC,QAAA,IAAI,aAAa,GAAgE,QAAQ,CAAC;cACtF,QAAQ,CAAC;AACX,cAAE,KAAK,CAAC,EAAE,CAAC;QACb,IAAI,aAAa,CAAC,IAAI,IAAI,qBAAqB,CAAC,SAAS,EAAE;YACzD,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,aAAmD,EAAE,OAAO,CAAC;;aACvF;AACL,YAAA,IAAI,aAAa,GAAG,QAAQ,CAAC,MAAgC;YAC7D,IAAI,OAAO,GAAG,KAAK;YACnB,IAAI,CAAC,aAAa,EAAE;gBAClB,OAAO,GAAG,IAAI;gBACd,MAAM,YAAY,GAAsC,EAAE;AAC1D,gBAAA,IAAI,SAAS,CAAC,MAAM,EAAE;AACpB,oBAAA,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM;;AAE3C,gBAAA,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC;;YAErC,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK;YAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,CAAC;AACzD,YAAA,SAAS,CAAC,WAAW,GAAG,OAAO;YAC/B,QAAQ,GAAG,SAAS;;AAGtB,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI;QACpC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;AACnC,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,OAAO,EAAE,IAAI;SACd;;IAGH,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;QAC9E,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC;AACjD,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC;AACpC,QAAA,OAAO,GAAG;;IAGJ,aAAa,CACnB,QAAgC,EAChC,OAAmC,EAAA;QAEnC,MAAM,MAAM,GAAkC,EAAE;QAChD,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;AAE3F,QAAA,KAAK,IAAI,UAAU,IAAI,cAAc,EAAE;AACrC,YAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,gBAAA,IAAI,UAAU,KAAK,UAAU,EAAE;AAC7B,oBAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;qBAClB;oBACL,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;;;iBAE/C;AACL,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;;;QAIpD,IAAI,qBAAqB,GAAG,KAAK;QACjC,IAAI,eAAe,GAAkB,IAAI;AACzC,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;AAC3B,YAAA,IAAI,SAAS,YAAY,GAAG,EAAE;AAC5B,gBAAA,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC3B,oBAAA,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAW;AACnD,oBAAA,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;gBAE5B,IAAI,CAAC,qBAAqB,EAAE;oBAC1B,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE;AACpC,wBAAA,IAAI,KAAM,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;4BAC3D,qBAAqB,GAAG,IAAI;4BAC5B;;;;;AAKV,SAAC,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,MAAM;AACN,YAAA,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,qBAAqB;AACrB,YAAA,OAAO,EAAE,IAAI;SACd;;IAGK,iBAAiB,CAAC,GAAa,EAAE,OAAmC,EAAA;AAC1E,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB;AAC7C,QAAA,IAAI,OAAO,GAAG,OAAO,CAAC,WAAW;AACjC,QAAA,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW;AACnC,QAAA,IAAI,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE;YAC5B,SAAS,IAAI,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK;;QAG/C,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAC3B,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE;YAE/B,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AAC5B,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;oBACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;AAC7C,wBAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAClB,wBAAA,OAAO,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC;wBAC/C;;;;;AAMJ,gBAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,oBAAqB,CAAE;gBACnF,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC;gBAChD,IAAI,oBAAoB,GAAG,IAAI;gBAC/B,IAAI,cAAc,EAAE;oBAClB,IACE,SAAS,IAAI,OAAO;wBACpB,SAAS,IAAI,cAAc,CAAC,SAAS;AACrC,wBAAA,OAAO,IAAI,cAAc,CAAC,OAAO,EACjC;wBACA,OAAO,CAAC,MAAM,CAAC,IAAI,CACjB,wBAAwB,CACtB,IAAI,EACJ,cAAc,CAAC,SAAS,EACxB,cAAc,CAAC,OAAO,EACtB,SAAS,EACT,OAAO,CACR,CACF;wBACD,oBAAoB,GAAG,KAAK;;;;;AAM9B,oBAAA,SAAS,GAAG,cAAc,CAAC,SAAS;;gBAGtC,IAAI,oBAAoB,EAAE;oBACxB,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,SAAS,EAAE,OAAO,EAAC,CAAC;;AAGjD,gBAAA,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC;;AAE/D,aAAC,CAAC;AACJ,SAAC,CAAC;;IAGJ,cAAc,CACZ,QAA4C,EAC5C,OAAmC,EAAA;AAEnC,QAAA,MAAM,GAAG,GAAiB,EAAC,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC;AAC5F,QAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YAClC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACvC,YAAA,OAAO,GAAG;;QAGZ,MAAM,mBAAmB,GAAG,CAAC;QAE7B,IAAI,yBAAyB,GAAG,CAAC;QACjC,MAAM,OAAO,GAAa,EAAE;QAC5B,IAAI,iBAAiB,GAAG,KAAK;QAC7B,IAAI,mBAAmB,GAAG,KAAK;QAC/B,IAAI,cAAc,GAAW,CAAC;QAE9B,MAAM,SAAS,GAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;YAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC;YACjD,IAAI,SAAS,GACX,KAAK,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;YACnE,IAAI,MAAM,GAAW,CAAC;AACtB,YAAA,IAAI,SAAS,IAAI,IAAI,EAAE;AACrB,gBAAA,yBAAyB,EAAE;AAC3B,gBAAA,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS;;YAEnC,mBAAmB,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;AACrE,YAAA,iBAAiB,GAAG,iBAAiB,IAAI,MAAM,GAAG,cAAc;YAChE,cAAc,GAAG,MAAM;AACvB,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB,YAAA,OAAO,KAAK;AACd,SAAC,CAAC;QAEF,IAAI,mBAAmB,EAAE;YACvB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;;QAGtC,IAAI,iBAAiB,EAAE;YACrB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;;AAGlD,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM;QACpC,IAAI,eAAe,GAAG,CAAC;QACvB,IAAI,yBAAyB,GAAG,CAAC,IAAI,yBAAyB,GAAG,MAAM,EAAE;YACvE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;;AACzC,aAAA,IAAI,yBAAyB,IAAI,CAAC,EAAE;YACzC,eAAe,GAAG,mBAAmB,IAAI,MAAM,GAAG,CAAC,CAAC;;AAGtD,QAAA,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC;AACxB,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW;AACvC,QAAA,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAsB;AAC5D,QAAA,MAAM,eAAe,GAAG,qBAAqB,CAAC,QAAQ;QACtD,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI;AAC1B,YAAA,MAAM,MAAM,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;AACxF,YAAA,MAAM,qBAAqB,GAAG,MAAM,GAAG,eAAe;YACtD,OAAO,CAAC,WAAW,GAAG,WAAW,GAAG,qBAAqB,CAAC,KAAK,GAAG,qBAAqB;AACvF,YAAA,qBAAqB,CAAC,QAAQ,GAAG,qBAAqB;AACtD,YAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC;AACnC,YAAA,EAAE,CAAC,MAAM,GAAG,MAAM;AAElB,YAAA,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AACrB,SAAC,CAAC;AAEF,QAAA,OAAO,GAAG;;IAGZ,cAAc,CACZ,QAAoC,EACpC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,SAAS;AACrC,YAAA,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;AACnF,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,iBAAiB,CACf,QAAuC,EACvC,OAAmC,EAAA;QAEnC,OAAO,CAAC,QAAQ,EAAE;QAClB,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,YAAY;AACxC,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,eAAe,CACb,QAAqC,EACrC,OAAmC,EAAA;QAEnC,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,UAAU;YACtC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;AAC3D,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,UAAU,CAAC,QAAgC,EAAE,OAAmC,EAAA;AAC9E,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,oBAAqB;QACpD,MAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,CAA0B;QAEjE,OAAO,CAAC,UAAU,EAAE;AACpB,QAAA,OAAO,CAAC,YAAY,GAAG,QAAQ;AAC/B,QAAA,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACpE,QAAA,OAAO,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAC5C,cAAE,cAAc,GAAG,GAAG,GAAG;cACvB,QAAQ;AACZ,QAAA,oBAAoB,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,oBAAoB,EAAE,IAAI,GAAG,EAAE,CAAC;AAEtF,QAAA,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;AAC1F,QAAA,OAAO,CAAC,YAAY,GAAG,IAAI;AAC3B,QAAA,OAAO,CAAC,oBAAoB,GAAG,cAAc;QAE7C,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,KAAK;YACjC,QAAQ;AACR,YAAA,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;AACzB,YAAA,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ;YAC5B,WAAW;YACX,SAAS;YACT,gBAAgB,EAAE,QAAQ,CAAC,QAAQ;AACnC,YAAA,OAAO,EAAE,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;SACrD;;IAGH,YAAY,CACV,QAAkC,EAClC,OAAmC,EAAA;AAEnC,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACzB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;;AAEvC,QAAA,MAAM,OAAO,GACX,QAAQ,CAAC,OAAO,KAAK;AACnB,cAAE,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM;AACxC,cAAE,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QAE3D,OAAO;YACL,IAAI,EAAE,qBAAqB,CAAC,OAAO;AACnC,YAAA,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;YACnF,OAAO;AACP,YAAA,OAAO,EAAE,IAAI;SACd;;AAEJ;AAED,SAAS,iBAAiB,CAAC,QAAgB,EAAA;AACzC,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,UAAU;AAChF,UAAE;UACA,KAAK;IACT,IAAI,YAAY,EAAE;QAChB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;;;;AAKnD,IAAA,QAAQ,GAAG;AACR,SAAA,OAAO,CAAC,MAAM,EAAE,mBAAmB;AACnC,SAAA,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,mBAAmB,GAAG,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACtE,SAAA,OAAO,CAAC,aAAa,EAAE,qBAAqB,CAAC;AAEhD,IAAA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC;AACjC;AAEA,SAAS,eAAe,CAAC,GAA+B,EAAA;AACtD,IAAA,OAAO,GAAG,GAAG,EAAC,GAAG,GAAG,EAAC,GAAG,IAAI;AAC9B;MAOa,0BAA0B,CAAA;AAWlB,IAAA,MAAA;IAVZ,UAAU,GAAW,CAAC;IACtB,QAAQ,GAAW,CAAC;IACpB,iBAAiB,GAAuC,IAAI;IAC5D,YAAY,GAAkC,IAAI;IAClD,oBAAoB,GAAkB,IAAI;IAC1C,qBAAqB,GAAqB,IAAI;IAC9C,WAAW,GAAW,CAAC;AACvB,IAAA,eAAe,GAAG,IAAI,GAAG,EAAuC;IAChE,OAAO,GAA4B,IAAI;AACvC,IAAA,6BAA6B,GAAgB,IAAI,GAAG,EAAU;AACrE,IAAA,WAAA,CAAmB,MAAe,EAAA;QAAf,IAAM,CAAA,MAAA,GAAN,MAAM;;AAC1B;AAID,SAAS,aAAa,CAAC,MAA0C,EAAA;IAC/D,IAAI,OAAO,MAAM,IAAI,QAAQ;AAAE,QAAA,OAAO,IAAI;IAE1C,IAAI,MAAM,GAAkB,IAAI;AAEhC,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;YAC5B,IAAI,UAAU,YAAY,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACzD,MAAM,GAAG,GAAG,UAA2B;gBACvC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;AAChD,gBAAA,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAExB,SAAC,CAAC;;SACG,IAAI,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QACxD,MAAM,GAAG,GAAG,MAAM;QAClB,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;AAChD,QAAA,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAEtB,IAAA,OAAO,MAAM;AACf;AAEA,SAAS,kBAAkB,CAAC,KAAuC,EAAE,MAAe,EAAA;AAClF,IAAA,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AACpC,QAAA,OAAO,KAAuB;;AAGhC,IAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;QAC5B,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ;QACtD,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;;IAGvC,MAAM,QAAQ,GAAG,KAAe;AAChC,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IAC7F,IAAI,SAAS,EAAE;QACb,MAAM,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAQ;AAC1C,QAAA,GAAG,CAAC,OAAO,GAAG,IAAI;AAClB,QAAA,GAAG,CAAC,QAAQ,GAAG,QAAQ;AACvB,QAAA,OAAO,GAAuB;;IAGhC,MAAM,OAAO,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC/C,IAAA,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC;AACvE;AAEA,SAAS,yBAAyB,CAAC,OAAgC,EAAA;IACjE,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,GAAG,EAAC,GAAG,OAAO,EAAC;AACtB,QAAA,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;YACrB,OAAO,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE;;;SAEpD;QACL,OAAO,GAAG,EAAE;;AAEd,IAAA,OAAO,OAAO;AAChB;AAEA,SAAS,aAAa,CAAC,QAAgB,EAAE,KAAa,EAAE,MAAqB,EAAA;AAC3E,IAAA,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAC;AAClC;;SCrpBgB,yBAAyB,CACvC,OAAY,EACZ,SAA+B,EAC/B,aAAuB,EACvB,cAAwB,EACxB,QAAgB,EAChB,KAAa,EACb,SAAwB,IAAI,EAC5B,cAAuB,KAAK,EAAA;IAE5B,OAAO;AACL,QAAA,IAAI,EAAsD,CAAA;QAC1D,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;QACd,QAAQ;QACR,KAAK;QACL,SAAS,EAAE,QAAQ,GAAG,KAAK;QAC3B,MAAM;QACN,WAAW;KACZ;AACH;;MCxCa,qBAAqB,CAAA;AACxB,IAAA,IAAI,GAAG,IAAI,GAAG,EAAuC;AAE7D,IAAA,GAAG,CAAC,OAAY,EAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;;IAGrC,MAAM,CAAC,OAAY,EAAE,YAA4C,EAAA;QAC/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;QACjD,IAAI,CAAC,oBAAoB,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,oBAAoB,GAAG,EAAE,EAAE;;AAErD,QAAA,oBAAoB,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;;AAG5C,IAAA,GAAG,CAAC,OAAY,EAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;;IAG/B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;;AAEpB;;ACgBD,MAAM,yBAAyB,GAAG,CAAC;AACnC,MAAM,WAAW,GAAG,QAAQ;AAC5B,MAAM,iBAAiB,mBAAmB,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;AACtE,MAAM,WAAW,GAAG,QAAQ;AAC5B,MAAM,iBAAiB,mBAAmB,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;AAEtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EG;AACa,SAAA,uBAAuB,CACrC,MAAuB,EACvB,WAAgB,EAChB,GAA+B,EAC/B,cAAsB,EACtB,cAAsB,EACtB,cAAA,GAAgC,IAAI,GAAG,EAAE,EACzC,WAA6B,GAAA,IAAI,GAAG,EAAE,EACtC,OAAyB,EACzB,eAAuC,EACvC,MAAA,GAAkB,EAAE,EAAA;IAEpB,OAAO,IAAI,+BAA+B,EAAE,CAAC,cAAc,CACzD,MAAM,EACN,WAAW,EACX,GAAG,EACH,cAAc,EACd,cAAc,EACd,cAAc,EACd,WAAW,EACX,OAAO,EACP,eAAe,EACf,MAAM,CACP;AACH;MAEa,+BAA+B,CAAA;IAC1C,cAAc,CACZ,MAAuB,EACvB,WAAgB,EAChB,GAA+B,EAC/B,cAAsB,EACtB,cAAsB,EACtB,cAA6B,EAC7B,WAA0B,EAC1B,OAAyB,EACzB,eAAuC,EACvC,SAAkB,EAAE,EAAA;AAEpB,QAAA,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE;AAChE,QAAA,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAC1C,MAAM,EACN,WAAW,EACX,eAAe,EACf,cAAc,EACd,cAAc,EACd,MAAM,EACN,EAAE,CACH;AACD,QAAA,OAAO,CAAC,OAAO,GAAG,OAAO;AACzB,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACnE,QAAA,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5C,QAAA,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC;AAElF,QAAA,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC;;AAGhC,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,iBAAiB,EAAE,CAAC;;;;;QAMtF,IAAI,SAAS,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,EAAE;AACxC,YAAA,IAAI,gBAA6C;AACjD,YAAA,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,gBAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC;AAC7B,gBAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,WAAW,EAAE;oBACpC,gBAAgB,GAAG,QAAQ;oBAC3B;;;YAGJ,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,EAAE;AACnE,gBAAA,gBAAgB,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC;;;QAG5E,OAAO,SAAS,CAAC;AACf,cAAE,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,cAAc,EAAE;cACrD,CAAC,yBAAyB,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;;IAG/E,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;;;IAI/D,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;;;IAI3D,eAAe,CAAC,GAAkB,EAAE,OAAiC,EAAA;;;IAIrE,iBAAiB,CAAC,GAAoB,EAAE,OAAiC,EAAA;AACvE,QAAA,MAAM,mBAAmB,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC;QACxE,IAAI,mBAAmB,EAAE;YACvB,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;AAC1D,YAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW;AACrD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CACxC,mBAAmB,EACnB,YAAY,EACZ,YAAY,CAAC,OAA8B,CAC5C;AACD,YAAA,IAAI,SAAS,IAAI,OAAO,EAAE;;;AAGxB,gBAAA,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC;;;AAG7C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,eAAe,CAAC,GAAkB,EAAE,OAAiC,EAAA;QACnE,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;QAC1D,YAAY,CAAC,wBAAwB,EAAE;AACvC,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC;QAC1F,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC;QAChD,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;AAC1E,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;AAGpB,IAAA,wBAAwB,CAC9B,qBAAkD,EAClD,OAAiC,EACjC,YAAsC,EAAA;AAEtC,QAAA,KAAK,MAAM,mBAAmB,IAAI,qBAAqB,EAAE;AACvD,YAAA,MAAM,cAAc,GAAG,mBAAmB,EAAE,KAAK;YACjD,IAAI,cAAc,EAAE;AAClB,gBAAA,MAAM,mBAAmB,GACvB,OAAO,cAAc,KAAK;AACxB,sBAAE;AACF,sBAAE,kBAAkB,CAChB,iBAAiB,CACf,cAAc,EACd,mBAAmB,EAAE,MAAM,IAAI,EAAE,EACjC,OAAO,CAAC,MAAM,CACf,CACF;AACP,gBAAA,YAAY,CAAC,aAAa,CAAC,mBAAmB,CAAC;;;;AAK7C,IAAA,qBAAqB,CAC3B,YAA4C,EAC5C,OAAiC,EACjC,OAA4B,EAAA;AAE5B,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW;QACrD,IAAI,YAAY,GAAG,SAAS;;;QAI5B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;QACvF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI;AAC9E,QAAA,IAAI,QAAQ,KAAK,CAAC,EAAE;AAClB,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACnC,gBAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,2BAA2B,CAC5D,WAAW,EACX,QAAQ,EACR,KAAK,CACN;AACD,gBAAA,YAAY,GAAG,IAAI,CAAC,GAAG,CACrB,YAAY,EACZ,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CACvD;AACH,aAAC,CAAC;;AAGJ,QAAA,OAAO,YAAY;;IAGrB,cAAc,CAAC,GAAiB,EAAE,OAAiC,EAAA;QACjE,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;QACxC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC;AAC1C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,aAAa,CAAC,GAAgB,EAAE,OAAiC,EAAA;AAC/D,QAAA,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe;QAC/C,IAAI,GAAG,GAAG,OAAO;AACjB,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO;AAE3B,QAAA,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AAChD,YAAA,GAAG,GAAG,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACvC,GAAG,CAAC,wBAAwB,EAAE;AAE9B,YAAA,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;gBACzB,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;AACxD,oBAAA,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE;AAC3C,oBAAA,GAAG,CAAC,YAAY,GAAG,0BAA0B;;gBAG/C,MAAM,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/C,gBAAA,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC;;;AAI5B,QAAA,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE;AACpB,YAAA,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;;AAGpD,YAAA,GAAG,CAAC,eAAe,CAAC,qBAAqB,EAAE;;;;AAK3C,YAAA,IAAI,GAAG,CAAC,eAAe,GAAG,eAAe,EAAE;gBACzC,GAAG,CAAC,wBAAwB,EAAE;;;AAIlC,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;QACzD,MAAM,cAAc,GAAsB,EAAE;AAC5C,QAAA,IAAI,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW;QACtD,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;QAE1F,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;YACtB,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC1D,IAAI,KAAK,EAAE;AACT,gBAAA,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGnC,YAAA,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC;AACnC,YAAA,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC;AAC/E,YAAA,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;AACnD,SAAC,CAAC;;;;AAKF,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,KAC9B,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAC/D;AACD,QAAA,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC;AAC9C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAGpB,YAAY,CAAC,GAAc,EAAE,OAAiC,EAAA;AACpE,QAAA,IAAK,GAAwB,CAAC,OAAO,EAAE;AACrC,YAAA,MAAM,QAAQ,GAAI,GAAwB,CAAC,QAAQ;AACnD,YAAA,MAAM,WAAW,GAAG,OAAO,CAAC;AAC1B,kBAAE,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM;kBAC1D,QAAQ;YACZ,OAAO,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC;;aAC5C;AACL,YAAA,OAAO,EAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC;;;IAIzE,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;AAC7D,QAAA,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACzF,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe;AACxC,QAAA,IAAI,OAAO,CAAC,KAAK,EAAE;AACjB,YAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC;YACpC,QAAQ,CAAC,qBAAqB,EAAE;;AAGlC,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK;QACvB,IAAI,KAAK,CAAC,IAAI,IAAI,qBAAqB,CAAC,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC;;aAC9B;AACL,YAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC;AACvC,YAAA,IAAI,CAAC,UAAU,CAAC,KAAiB,EAAE,OAAO,CAAC;YAC3C,QAAQ,CAAC,qBAAqB,EAAE;;AAGlC,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI;AACpC,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;AACzD,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe;AACxC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAsB;;;QAI9C,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,yBAAyB,EAAE,EAAE;YACpD,QAAQ,CAAC,YAAY,EAAE;;AAGzB,QAAA,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM;AACxD,QAAA,IAAI,GAAG,CAAC,WAAW,EAAE;AACnB,YAAA,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC;;aAC1B;AACL,YAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC;;AAGzE,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,cAAc,CAAC,GAAiB,EAAE,OAAiC,EAAA;AACjE,QAAA,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAsB;AAC5D,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAgB,CAAC,QAAQ;AACnD,QAAA,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ;AAC/C,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,EAAE;AAC/C,QAAA,MAAM,aAAa,GAAG,YAAY,CAAC,eAAe;AAClD,QAAA,aAAa,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM;QAEnD,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1B,YAAA,MAAM,MAAM,GAAW,IAAI,CAAC,MAAM,IAAI,CAAC;AACvC,YAAA,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5C,YAAA,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC;YAClF,aAAa,CAAC,qBAAqB,EAAE;AACvC,SAAC,CAAC;;;AAIF,QAAA,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC;;;AAInE,QAAA,OAAO,CAAC,wBAAwB,CAAC,SAAS,GAAG,QAAQ,CAAC;AACtD,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,UAAU,CAAC,GAAa,EAAE,OAAiC,EAAA;;;AAGzD,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW;QACrD,MAAM,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI,EAAE,CAA0B;AAC5D,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AAEnE,QAAA,IACE,KAAK;aACJ,OAAO,CAAC,YAAY,CAAC,IAAI,KAAK,qBAAqB,CAAC,KAAK;AACxD,iBAAC,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,yBAAyB,EAAE,CAAC,CAAC,EAC1E;AACA,YAAA,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE;AAC/C,YAAA,OAAO,CAAC,YAAY,GAAG,0BAA0B;;QAGnD,IAAI,YAAY,GAAG,SAAS;AAC5B,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAC9B,GAAG,CAAC,QAAQ,EACZ,GAAG,CAAC,gBAAgB,EACpB,GAAG,CAAC,KAAK,EACT,GAAG,CAAC,WAAW,EACf,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAC/B,OAAO,CAAC,MAAM,CACf;AAED,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM;QACvC,IAAI,mBAAmB,GAA2B,IAAI;QACtD,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,KAAI;AAC1B,YAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC;AAC7B,YAAA,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;YACnE,IAAI,KAAK,EAAE;AACT,gBAAA,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGnC,YAAA,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;AAC/B,gBAAA,mBAAmB,GAAG,YAAY,CAAC,eAAe;;YAGpD,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC;;;;AAK/C,YAAA,YAAY,CAAC,eAAe,CAAC,qBAAqB,EAAE;AAEpD,YAAA,MAAM,OAAO,GAAG,YAAY,CAAC,eAAe,CAAC,WAAW;YACxD,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC;AAChD,SAAC,CAAC;AAEF,QAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC;AAC7B,QAAA,OAAO,CAAC,iBAAiB,GAAG,CAAC;AAC7B,QAAA,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC;QAE9C,IAAI,mBAAmB,EAAE;AACvB,YAAA,OAAO,CAAC,eAAe,CAAC,4BAA4B,CAAC,mBAAmB,CAAC;AACzE,YAAA,OAAO,CAAC,eAAe,CAAC,qBAAqB,EAAE;;AAGjD,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;IAG5B,YAAY,CAAC,GAAe,EAAE,OAAiC,EAAA;AAC7D,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAc;AAC5C,QAAA,MAAM,EAAE,GAAG,OAAO,CAAC,eAAe;AAClC,QAAA,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC3C,MAAM,OAAO,GAAG,QAAQ,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC1D,QAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,iBAAiB;AAEhD,QAAA,IAAI,kBAAkB,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,MAAM;QAC1E,QAAQ,kBAAkB;AACxB,YAAA,KAAK,SAAS;AACZ,gBAAA,KAAK,GAAG,OAAO,GAAG,KAAK;gBACvB;AACF,YAAA,KAAK,MAAM;AACT,gBAAA,KAAK,GAAG,aAAa,CAAC,kBAAkB;gBACxC;;AAGJ,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,eAAe;QACxC,IAAI,KAAK,EAAE;AACT,YAAA,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;;AAG/B,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW;QACzC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC;AAC1C,QAAA,OAAO,CAAC,YAAY,GAAG,GAAG;;;;;AAM1B,QAAA,aAAa,CAAC,kBAAkB;AAC9B,YAAA,EAAE,CAAC,WAAW,GAAG,YAAY,IAAI,EAAE,CAAC,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC;;AAE7F;AAOD,MAAM,0BAA0B,GAA+B,EAAE;MACpD,wBAAwB,CAAA;AAYzB,IAAA,OAAA;AACD,IAAA,OAAA;AACA,IAAA,eAAA;AACC,IAAA,eAAA;AACA,IAAA,eAAA;AACD,IAAA,MAAA;AACA,IAAA,SAAA;IAjBF,aAAa,GAAoC,IAAI;AACrD,IAAA,eAAe;IACf,qBAAqB,GAA0B,IAAI;IACnD,YAAY,GAA+B,0BAA0B;IACrE,eAAe,GAAG,CAAC;IACnB,OAAO,GAAqB,EAAE;IAC9B,iBAAiB,GAAW,CAAC;IAC7B,iBAAiB,GAAW,CAAC;IAC7B,kBAAkB,GAAW,CAAC;AAErC,IAAA,WAAA,CACU,OAAwB,EACzB,OAAY,EACZ,eAAsC,EACrC,eAAuB,EACvB,eAAuB,EACxB,MAAe,EACf,SAA4B,EACnC,eAAiC,EAAA;QAPzB,IAAO,CAAA,OAAA,GAAP,OAAO;QACR,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAe,CAAA,eAAA,GAAf,eAAe;QACd,IAAe,CAAA,eAAA,GAAf,eAAe;QACf,IAAe,CAAA,eAAA,GAAf,eAAe;QAChB,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAS,CAAA,SAAA,GAAT,SAAS;AAGhB,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AACvF,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;;AAGtC,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;;IAG5B,aAAa,CAAC,OAAgC,EAAE,YAAsB,EAAA;AACpE,QAAA,IAAI,CAAC,OAAO;YAAE;QAEd,MAAM,UAAU,GAAG,OAAc;AACjC,QAAA,IAAI,eAAe,GAAG,IAAI,CAAC,OAAO;;AAGlC,QAAA,IAAI,UAAU,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC9B,eAAuB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC;;AAG7E,QAAA,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;YAC5B,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC;;AAG9D,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM;QACnC,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,cAAc,GAA0B,eAAe,CAAC,MAAO;YACnE,IAAI,CAAC,cAAc,EAAE;gBACnB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;;YAG3C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;gBACtC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AACzD,oBAAA,cAAc,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC;;AAE1F,aAAC,CAAC;;;IAIE,YAAY,GAAA;QAClB,MAAM,OAAO,GAAqB,EAAE;AACpC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;YACrC,IAAI,SAAS,EAAE;gBACb,MAAM,MAAM,IAA2B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;oBACtC,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;AAChC,iBAAC,CAAC;;;AAGN,QAAA,OAAO,OAAO;;AAGhB,IAAA,gBAAgB,CACd,OAAmC,GAAA,IAAI,EACvC,OAAa,EACb,OAAgB,EAAA;AAEhB,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO;QACtC,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAC1C,IAAI,CAAC,OAAO,EACZ,MAAM,EACN,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,CAChD;AACD,QAAA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;AACxC,QAAA,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB;AAE1D,QAAA,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE;AACrC,QAAA,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC;AAE9B,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;AAClD,QAAA,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;AAClD,QAAA,OAAO,CAAC,aAAa,GAAG,IAAI;QAC5B,IAAI,CAAC,eAAe,EAAE;AACtB,QAAA,OAAO,OAAO;;AAGhB,IAAA,wBAAwB,CAAC,OAAgB,EAAA;AACvC,QAAA,IAAI,CAAC,YAAY,GAAG,0BAA0B;AAC9C,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;QACvE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;QACzC,OAAO,IAAI,CAAC,eAAe;;AAG7B,IAAA,2BAA2B,CACzB,WAAyC,EACzC,QAAuB,EACvB,KAAoB,EAAA;AAEpB,QAAA,MAAM,cAAc,GAAmB;AACrC,YAAA,QAAQ,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,WAAW,CAAC,QAAQ;YAC5D,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK;AACzF,YAAA,MAAM,EAAE,EAAE;SACX;AACD,QAAA,MAAM,OAAO,GAAG,IAAI,kBAAkB,CACpC,IAAI,CAAC,OAAO,EACZ,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,SAAS,EACrB,WAAW,CAAC,aAAa,EACzB,WAAW,CAAC,cAAc,EAC1B,cAAc,EACd,WAAW,CAAC,uBAAuB,CACpC;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5B,QAAA,OAAO,cAAc;;AAGvB,IAAA,aAAa,CAAC,IAAY,EAAA;AACxB,QAAA,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC;;AAGxE,IAAA,aAAa,CAAC,KAAa,EAAA;;AAEzB,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC;;;IAI7C,WAAW,CACT,QAAgB,EAChB,gBAAwB,EACxB,KAAa,EACb,WAAoB,EACpB,QAAiB,EACjB,MAAe,EAAA;QAEf,IAAI,OAAO,GAAU,EAAE;QACvB,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;AAE5B,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEvB,YAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1E,YAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1E,YAAA,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC;AACxB,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;AAChE,YAAA,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,QAAQ;AACN,oBAAA,KAAK,GAAG;AACN,0BAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC,MAAM;0BACvD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;;AAEhC,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;;QAG3B,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;;AAE7C,QAAA,OAAO,OAAO;;AAEjB;MAEY,eAAe,CAAA;AAchB,IAAA,OAAA;AACD,IAAA,OAAA;AACA,IAAA,SAAA;AACC,IAAA,4BAAA;IAhBH,QAAQ,GAAW,CAAC;IACpB,MAAM,GAAkB,IAAI;AAC3B,IAAA,iBAAiB,GAAkB,IAAI,GAAG,EAAE;AAC5C,IAAA,gBAAgB,GAAkB,IAAI,GAAG,EAAE;AAC3C,IAAA,UAAU,GAAG,IAAI,GAAG,EAAyB;AAC7C,IAAA,aAAa,GAAG,IAAI,GAAG,EAAuB;AAC9C,IAAA,oBAAoB,GAAkB,IAAI,GAAG,EAAE;AAC/C,IAAA,qBAAqB;AACrB,IAAA,cAAc,GAAkB,IAAI,GAAG,EAAE;AACzC,IAAA,SAAS,GAAkB,IAAI,GAAG,EAAE;IACpC,yBAAyB,GAAyB,IAAI;AAE9D,IAAA,WAAA,CACU,OAAwB,EACzB,OAAY,EACZ,SAAiB,EAChB,4BAAsD,EAAA;QAHtD,IAAO,CAAA,OAAA,GAAP,OAAO;QACR,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;QACR,IAA4B,CAAA,4BAAA,GAA5B,4BAA4B;AAEpC,QAAA,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;AACtC,YAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,GAAG,EAAsB;;QAGnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,CAAE;AAC5E,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC/B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,oBAAoB;YACtD,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC;;QAE3E,IAAI,CAAC,aAAa,EAAE;;IAGtB,iBAAiB,GAAA;AACf,QAAA,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;AAC1B,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAO,KAAK;AACd,YAAA,KAAK,CAAC;AACJ,gBAAA,OAAO,IAAI,CAAC,yBAAyB,EAAE;AACzC,YAAA;AACE,gBAAA,OAAO,IAAI;;;IAIjB,yBAAyB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC;;AAGvC,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;;AAGvC,IAAA,aAAa,CAAC,KAAa,EAAA;;;;;AAKzB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI;AAE9E,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,eAAe,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAC1C,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,qBAAqB,EAAE;;;aAEzB;AACL,YAAA,IAAI,CAAC,SAAS,IAAI,KAAK;;;IAI3B,IAAI,CAAC,OAAY,EAAE,WAAoB,EAAA;QACrC,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,OAAO,IAAI,eAAe,CACxB,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,WAAW,IAAI,IAAI,CAAC,WAAW,EAC/B,IAAI,CAAC,4BAA4B,CAClC;;IAGK,aAAa,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB;;AAEhD,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE;AAC3D,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC;;;IAI7D,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,QAAQ,IAAI,yBAAyB;QAC1C,IAAI,CAAC,aAAa,EAAE;;AAGtB,IAAA,WAAW,CAAC,IAAY,EAAA;QACtB,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC,aAAa,EAAE;;IAGd,YAAY,CAAC,IAAY,EAAE,KAAsB,EAAA;QACvD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;QAC1C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,EAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAC,CAAC;;IAG/D,uBAAuB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC,gBAAgB;;AAGjE,IAAA,cAAc,CAAC,MAAqB,EAAA;QAClC,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;;;;;;;;QAS9C,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,UAAU,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;;AAE7C,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,gBAAgB;;AAGxD,IAAA,SAAS,CACP,KAAoC,EACpC,MAAqB,EACrB,MAAe,EACf,OAA0B,EAAA;QAE1B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;;QAE9C,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE;QAChD,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC;QAC/D,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,EAAE;YAChC,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC;;AAE9E,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC;;;IAIhC,qBAAqB,GAAA;AACnB,QAAA,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;YAAE;QAEnC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AACtC,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;QAE3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YAC9C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;AAExC,SAAC,CAAC;;IAGJ,qBAAqB,GAAA;QACnB,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACjD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAClC,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC;;;IAIhC,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAG3C,IAAA,IAAI,UAAU,GAAA;QACZ,MAAM,UAAU,GAAa,EAAE;AAC/B,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACtC,YAAA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEvB,QAAA,OAAO,UAAU;;AAGnB,IAAA,4BAA4B,CAAC,QAAyB,EAAA;QACpD,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;YAC7C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;gBAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;;AAE3C,SAAC,CAAC;;IAGJ,cAAc,GAAA;QACZ,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU;AACvC,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU;AACxC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;QAEjE,IAAI,cAAc,GAAyB,EAAE;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,IAAI,KAAI;AACzC,YAAA,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,CAAC;YAC/D,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACpC,gBAAA,IAAI,KAAK,KAAKA,UAAS,EAAE;AACvB,oBAAA,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;;AAClB,qBAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AAC/B,oBAAA,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;;AAE5B,aAAC,CAAC;YACF,IAAI,CAAC,OAAO,EAAE;gBACZ,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAEnD,YAAA,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC;AACpC,SAAC,CAAC;QAEF,MAAM,QAAQ,GAAa,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;QACtD,MAAM,SAAS,GAAa,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;;QAGxD,IAAI,OAAO,EAAE;AACX,YAAA,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC;AAC7B,YAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;AACxB,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpB,YAAA,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpB,YAAA,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;;QAG7B,OAAO,yBAAyB,CAC9B,IAAI,CAAC,OAAO,EACZ,cAAc,EACd,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,KAAK,CACN;;AAEJ;AAED,MAAM,kBAAmB,SAAQ,eAAe,CAAA;AAMrC,IAAA,SAAA;AACA,IAAA,aAAA;AACA,IAAA,cAAA;AAEC,IAAA,wBAAA;AATH,IAAA,OAAO;AAEd,IAAA,WAAA,CACE,MAAuB,EACvB,OAAY,EACL,SAA+B,EAC/B,aAAuB,EACvB,cAAwB,EAC/B,OAAuB,EACf,2BAAoC,KAAK,EAAA;QAEjD,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;QAN9B,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAc,CAAA,cAAA,GAAd,cAAc;QAEb,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB;QAGhC,IAAI,CAAC,OAAO,GAAG,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAC;;IAGlF,iBAAiB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;;IAGzB,cAAc,GAAA;AACrB,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;QAC9B,IAAI,EAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,OAAO;AAC5C,QAAA,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,EAAE;YAC1C,MAAM,YAAY,GAAyB,EAAE;AAC7C,YAAA,MAAM,SAAS,GAAG,QAAQ,GAAG,KAAK;AAClC,YAAA,MAAM,WAAW,GAAG,KAAK,GAAG,SAAS;;YAGrC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9C,YAAA,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjC,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAEnC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9C,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;AACxD,YAAA,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAEnC;;;;;;;;;;;;;AAaG;;AAGH,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;AAClC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC/B,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC9B,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAW;AAC5C,gBAAA,MAAM,cAAc,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ;AACnD,gBAAA,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC;AACzD,gBAAA,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;;;YAIvB,QAAQ,GAAG,SAAS;YACpB,KAAK,GAAG,CAAC;YACT,MAAM,GAAG,EAAE;YAEX,SAAS,GAAG,YAAY;;QAG1B,OAAO,yBAAyB,CAC9B,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,IAAI,CACL;;AAEJ;AAED,SAAS,WAAW,CAAC,MAAc,EAAE,aAAa,GAAG,CAAC,EAAA;AACpD,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,GAAG,CAAC,CAAC;IAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI;AACzC;AAEA,SAAS,aAAa,CAAC,KAAoC,EAAE,SAAwB,EAAA;AACnF,IAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE;AACvC,IAAA,IAAI,aAAkD;AACtD,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACtB,QAAA,IAAI,KAAK,KAAK,GAAG,EAAE;AACjB,YAAA,aAAa,KAAK,SAAS,CAAC,IAAI,EAAE;AAClC,YAAA,KAAK,IAAI,IAAI,IAAI,aAAa,EAAE;AAC9B,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;;;aAEzB;YACL,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAsB,EAAE;AAC9C,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;;AAG3B,KAAC,CAAC;AACF,IAAA,OAAO,MAAM;AACf;;ACjhCM,SAAU,2BAA2B,CACzC,OAAY,EACZ,WAAmB,EACnB,SAAiB,EACjB,OAAe,EACf,mBAA4B,EAC5B,UAAyB,EACzB,QAAuB,EACvB,SAAyC,EACzC,eAAsB,EACtB,aAAoC,EACpC,cAAqC,EACrC,SAAiB,EACjB,MAAgB,EAAA;IAEhB,OAAO;AACL,QAAA,IAAI,EAAwD,CAAA;QAC5D,OAAO;QACP,WAAW;QACX,mBAAmB;QACnB,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,eAAe;QACf,aAAa;QACb,cAAc;QACd,SAAS;QACT,MAAM;KACP;AACH;;ACxCA,MAAM,YAAY,GAAG,EAAE;MAEV,0BAA0B,CAAA;AAE3B,IAAA,YAAA;AACD,IAAA,GAAA;AACC,IAAA,YAAA;AAHV,IAAA,WAAA,CACU,YAAoB,EACrB,GAAkB,EACjB,YAA+C,EAAA;QAF/C,IAAY,CAAA,YAAA,GAAZ,YAAY;QACb,IAAG,CAAA,GAAA,GAAH,GAAG;QACF,IAAY,CAAA,YAAA,GAAZ,YAAY;;AAGtB,IAAA,KAAK,CAAC,YAAiB,EAAE,SAAc,EAAE,OAAY,EAAE,MAA4B,EAAA;AACjF,QAAA,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;;AAG/F,IAAA,WAAW,CACT,SAAuC,EACvC,MAA4B,EAC5B,MAAe,EAAA;QAEf,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;AACvC,QAAA,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,YAAA,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,MAAM;;AAEjE,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE;;AAGhE,IAAA,KAAK,CACH,MAAuB,EACvB,OAAY,EACZ,YAAiB,EACjB,SAAc,EACd,cAAsB,EACtB,cAAsB,EACtB,cAAiC,EACjC,WAA8B,EAC9B,eAAuC,EACvC,YAAsB,EAAA;QAEtB,MAAM,MAAM,GAAY,EAAE;AAE1B,QAAA,MAAM,yBAAyB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY;QAC/F,MAAM,sBAAsB,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,YAAY;AACxF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,sBAAsB,EAAE,MAAM,CAAC;QACzF,MAAM,mBAAmB,GAAG,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY;AAC/E,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC;AAEhF,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAO;AACtC,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAoB;AAC/C,QAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAoB;AAChD,QAAA,MAAM,SAAS,GAAG,SAAS,KAAK,MAAM;AAEtC,QAAA,MAAM,gBAAgB,GAAqB;AACzC,YAAA,MAAM,EAAE,kBAAkB,CAAC,mBAAmB,EAAE,yBAAyB,CAAC;AAC1E,YAAA,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK;SAC/B;QAED,MAAM,SAAS,GAAG;AAChB,cAAE;cACA,uBAAuB,CACrB,MAAM,EACN,OAAO,EACP,IAAI,CAAC,GAAG,CAAC,SAAS,EAClB,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,MAAM,CACP;QAEL,IAAI,SAAS,GAAG,CAAC;AACjB,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACvB,YAAA,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;AACzD,SAAC,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,OAAO,2BAA2B,CAChC,OAAO,EACP,IAAI,CAAC,YAAY,EACjB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,EAAE,EACF,EAAE,EACF,WAAW,EACX,YAAY,EACZ,SAAS,EACT,MAAM,CACP;;AAGH,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACvB,YAAA,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO;AACtB,YAAA,MAAM,QAAQ,GAAG,oBAAoB,CAAC,WAAW,EAAE,GAAG,EAAE,IAAI,GAAG,EAAU,CAAC;AAC1E,YAAA,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAEtD,YAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,GAAG,EAAU,CAAC;AAC5E,YAAA,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAExD,YAAA,IAAI,GAAG,KAAK,OAAO,EAAE;AACnB,gBAAA,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;;AAE5B,SAAC,CAAC;AAEF,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,6BAA6B,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC;;AAGrE,QAAA,OAAO,2BAA2B,CAChC,OAAO,EACP,IAAI,CAAC,YAAY,EACjB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,SAAS,EACT,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAC7B,WAAW,EACX,YAAY,EACZ,SAAS,CACV;;AAEJ;AAED;;;;;;;;;;;;;AAaG;AACH,SAAS,6BAA6B,CACpC,SAAyC,EACzC,WAAmB,EACnB,MAAuB,EAAA;AAEvB,IAAA,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;QAC3C;;AAGF,IAAA,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAS;;;;;QAKhD,QAAQ;AACT,KAAA,CAAC;AAEF,IAAA,MAAM,yBAAyB,GAAG,IAAI,GAAG,EAAU;IAEnD,SAAS,CAAC,OAAO,CAAC,CAAC,EAAC,SAAS,EAAC,KAAI;AAChC,QAAA,MAAM,+BAA+B,GAAG,IAAI,GAAG,EAA2B;AAC1E,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC7B,YAAA,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAC1D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,CACjD;YACD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,+BAAgC,CAAC,IAAI,CAAC,EAAE;AAClD,oBAAA,IAAI,+BAA+B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACrF,MAAM,gBAAgB,GAAG,+BAA+B,CAAC,GAAG,CAAC,IAAI,CAAC;AAClE,wBAAA,IAAI,gBAAgB,KAAK,KAAK,EAAE;AAC9B,4BAAA,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC;;;yBAEhC;AACL,wBAAA,+BAA+B,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;;;;AAIxD,SAAC,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IAAI,yBAAyB,CAAC,IAAI,GAAG,CAAC,EAAE;AACtC,QAAA,OAAO,CAAC,IAAI,CACV,CAAA,gCAAA,EAAmC,WAAW,CAA0C,wCAAA,CAAA;YACtF,8BAA8B;YAC9B,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAChD,IAAI;AACJ,YAAA,iIAAiI,CACpI;;AAEL;AAEA,SAAS,yBAAyB,CAChC,QAA+B,EAC/B,YAAiB,EACjB,SAAc,EACd,OAAY,EACZ,MAA4B,EAAA;IAE5B,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC5E;AAEA,SAAS,kBAAkB,CAAC,UAA+B,EAAE,QAA6B,EAAA;AACxF,IAAA,MAAM,MAAM,GAAwB,EAAC,GAAG,QAAQ,EAAC;AACjD,IAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AAClD,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK;;AAEvB,KAAC,CAAC;AACF,IAAA,OAAO,MAAM;AACf;MAEa,oBAAoB,CAAA;AAErB,IAAA,MAAA;AACA,IAAA,aAAA;AACA,IAAA,UAAA;AAHV,IAAA,WAAA,CACU,MAAgB,EAChB,aAAmC,EACnC,UAAoC,EAAA;QAFpC,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAU,CAAA,UAAA,GAAV,UAAU;;IAGpB,WAAW,CAAC,MAA4B,EAAE,MAAe,EAAA;AACvD,QAAA,MAAM,WAAW,GAAkB,IAAI,GAAG,EAAE;QAC5C,MAAM,cAAc,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACnC,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;oBAC1B,IAAI,GAAG,EAAE;wBACP,GAAG,GAAG,iBAAiB,CAAC,GAAG,EAAE,cAAc,EAAE,MAAM,CAAC;;AAEtD,oBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC;AAC1E,oBAAA,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,MAAM,CAAC;AAC5E,oBAAA,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAC5B,iBAAC,CAAC;;AAEN,SAAC,CAAC;AACF,QAAA,OAAO,WAAW;;AAErB;;SCvPe,YAAY,CAC1B,IAAY,EACZ,GAAe,EACf,UAAoC,EAAA;IAEpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC;AACpD;MAEa,gBAAgB,CAAA;AAMlB,IAAA,IAAA;AACA,IAAA,GAAA;AACC,IAAA,WAAA;IAPH,mBAAmB,GAAiC,EAAE;AACtD,IAAA,kBAAkB;AAClB,IAAA,MAAM,GAAG,IAAI,GAAG,EAAgC;AAEvD,IAAA,WAAA,CACS,IAAY,EACZ,GAAe,EACd,WAAqC,EAAA;QAFtC,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAG,CAAA,GAAA,GAAH,GAAG;QACF,IAAW,CAAA,WAAA,GAAX,WAAW;QAEnB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACzB,YAAA,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE;YAC/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;AAC5F,SAAC,CAAC;QAEF,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC;QAC3C,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC;QAE5C,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AAC9B,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACvF,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAwB,CAAC;;AAGzF,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC;;AAGhC,IAAA,eAAe,CACb,YAAiB,EACjB,SAAc,EACd,OAAY,EACZ,MAA4B,EAAA;QAE5B,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,KAC5C,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAClD;QACD,OAAO,KAAK,IAAI,IAAI;;AAGtB,IAAA,WAAW,CAAC,YAAiB,EAAE,MAA4B,EAAE,MAAe,EAAA;AAC1E,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC;;AAE3E;AAED,SAAS,wBAAwB,CAC/B,WAAmB,EACnB,MAAyC,EACzC,UAAoC,EAAA;AAEpC,IAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,SAAc,EAAE,OAAY,KAAK,IAAI,CAAC;AACzD,IAAA,MAAM,SAAS,GAAgB,EAAC,IAAI,EAAE,qBAAqB,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAC;AAC/F,IAAA,MAAM,UAAU,GAAkB;QAChC,IAAI,EAAE,qBAAqB,CAAC,UAAU;QACtC,SAAS;QACT,QAAQ;AACR,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,QAAQ,EAAE,CAAC;KACZ;IACD,OAAO,IAAI,0BAA0B,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC;AACxE;AAEA,SAAS,iBAAiB,CACxB,QAA2C,EAC3C,IAAY,EACZ,IAAY,EAAA;AAEZ,IAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvB,YAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;;;AAEpC,SAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,QAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;;AAE3C;;ACxDA,MAAM,qBAAqB,mBAAmB,IAAI,qBAAqB,EAAE;MAE5D,uBAAuB,CAAA;AAMzB,IAAA,QAAA;AACC,IAAA,OAAA;AACA,IAAA,WAAA;AAPF,IAAA,WAAW,GAAG,IAAI,GAAG,EAAsC;AAC3D,IAAA,YAAY,GAAG,IAAI,GAAG,EAA2B;IAClD,OAAO,GAAsB,EAAE;AAEtC,IAAA,WAAA,CACS,QAAa,EACZ,OAAwB,EACxB,WAAqC,EAAA;QAFtC,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAW,CAAA,WAAA,GAAX,WAAW;;IAGrB,QAAQ,CAAC,EAAU,EAAE,QAAiD,EAAA;QACpE,MAAM,MAAM,GAAY,EAAE;QAC1B,MAAM,QAAQ,GAAa,EAAE;AAC7B,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;AACvE,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,cAAc,CAAC,MAAM,CAAC;;aACvB;AACL,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,gBAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;oBACnB,YAAY,CAAC,QAAQ,CAAC;;;YAG1B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC;;;AAIzB,IAAA,YAAY,CAClB,CAA+B,EAC/B,SAAwB,EACxB,UAA0B,EAAA;AAE1B,QAAA,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO;AACzB,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;QAC1F,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;;AAG1F,IAAA,MAAM,CAAC,EAAU,EAAE,OAAY,EAAE,UAA4B,EAAE,EAAA;QAC7D,MAAM,MAAM,GAAY,EAAE;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;AACpC,QAAA,IAAI,YAA4C;AAEhD,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAsB;QAEnD,IAAI,GAAG,EAAE;AACP,YAAA,YAAY,GAAG,uBAAuB,CACpC,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,GAAG,EACH,eAAe,EACf,eAAe,EACf,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,EAAE,EACT,OAAO,EACP,qBAAqB,EACrB,MAAM,CACP;AACD,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,gBAAA,MAAM,MAAM,GAAG,oBAAoB,CACjC,aAAa,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,GAAG,EAAkC,CAC1C;AACD,gBAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/D,aAAC,CAAC;;aACG;AACL,YAAA,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC1C,YAAY,GAAG,EAAE;;AAGnB,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,qBAAqB,CAAC,MAAM,CAAC;;QAGrC,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,KAAI;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAI;AACzB,gBAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AACxE,aAAC,CAAC;AACJ,SAAC,CAAC;QAEF,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAI;YACrC,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;AAC3C,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,MAAM,CAAC;AAChD,SAAC,CAAC;AACF,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC;AACjC,QAAA,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAExC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,OAAO,MAAM;;AAGf,IAAA,OAAO,CAAC,EAAU,EAAA;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,EAAE;AAChB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1C,QAAA,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;AAIzB,IAAA,UAAU,CAAC,EAAU,EAAA;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,aAAa,CAAC,EAAE,CAAC;;AAEzB,QAAA,OAAO,MAAM;;AAGf,IAAA,MAAM,CACJ,EAAU,EACV,OAAe,EACf,SAAiB,EACjB,QAA6B,EAAA;;AAG7B,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzD,QAAA,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;AACnE,QAAA,OAAO,MAAO,GAAC;;AAGjB,IAAA,OAAO,CAAC,EAAU,EAAE,OAAY,EAAE,OAAe,EAAE,IAAW,EAAA;AAC5D,QAAA,IAAI,OAAO,IAAI,UAAU,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAA4C,CAAC;YACrE;;AAGF,QAAA,IAAI,OAAO,IAAI,QAAQ,EAAE;YACvB,MAAM,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAqB;YACnD,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC;YACjC;;QAGF,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAClC,QAAQ,OAAO;AACb,YAAA,KAAK,MAAM;gBACT,MAAM,CAAC,IAAI,EAAE;gBACb;AACF,YAAA,KAAK,OAAO;gBACV,MAAM,CAAC,KAAK,EAAE;gBACd;AACF,YAAA,KAAK,OAAO;gBACV,MAAM,CAAC,KAAK,EAAE;gBACd;AACF,YAAA,KAAK,SAAS;gBACZ,MAAM,CAAC,OAAO,EAAE;gBAChB;AACF,YAAA,KAAK,QAAQ;gBACX,MAAM,CAAC,MAAM,EAAE;gBACf;AACF,YAAA,KAAK,MAAM;gBACT,MAAM,CAAC,IAAI,EAAE;gBACb;AACF,YAAA,KAAK,aAAa;gBAChB,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAW,CAAC,CAAC;gBACjD;AACF,YAAA,KAAK,SAAS;AACZ,gBAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChB;;;AAGP;;ACxJD,MAAM,gBAAgB,GAAG,mBAAmB;AAC5C,MAAM,eAAe,GAAG,oBAAoB;AAC5C,MAAM,kBAAkB,GAAG,qBAAqB;AAChD,MAAM,iBAAiB,GAAG,sBAAsB;AAChD,MAAM,cAAc,GAAG,kBAAkB;AACzC,MAAM,aAAa,GAAG,mBAAmB;AAEzC,MAAM,kBAAkB,GAAgC,EAAE;AAC1D,MAAM,kBAAkB,GAA0B;AAChD,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,YAAY,EAAE,KAAK;AACnB,IAAA,oBAAoB,EAAE,KAAK;CAC5B;AACD,MAAM,0BAA0B,GAA0B;AACxD,IAAA,WAAW,EAAE,EAAE;AACf,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,YAAY,EAAE,KAAK;AACnB,IAAA,oBAAoB,EAAE,IAAI;CAC3B;AAkBD,MAAM,YAAY,GAAG,cAAc;AAWnC,MAAM,UAAU,CAAA;AAUL,IAAA,WAAA;AATF,IAAA,KAAK;AACL,IAAA,OAAO;AAEd,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAA8B;;IAGpD,WACE,CAAA,KAAU,EACH,WAAA,GAAsB,EAAE,EAAA;QAAxB,IAAW,CAAA,WAAA,GAAX,WAAW;QAElB,MAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;AACpD,QAAA,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK;AAC5C,QAAA,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC;QACzC,IAAI,KAAK,EAAE;;YAET,MAAM,EAAC,KAAK,EAAE,GAAG,OAAO,EAAC,GAAG,KAAK;AACjC,YAAA,IAAI,CAAC,OAAO,GAAG,OAA2B;;aACrC;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,EAAE;;AAEnB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;;;AAI5B,IAAA,aAAa,CAAC,OAAyB,EAAA;AACrC,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM;QAChC,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO;YACtC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACtC,gBAAA,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;oBAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;;AAErC,aAAC,CAAC;;;AAGP;AAED,MAAM,UAAU,GAAG,MAAM;AACzB,MAAM,mBAAmB,mBAAmB,IAAI,UAAU,CAAC,UAAU,CAAC;AAEtE,MAAM,4BAA4B,CAAA;AAWvB,IAAA,EAAA;AACA,IAAA,WAAA;AACC,IAAA,OAAA;IAZH,OAAO,GAAgC,EAAE;AAExC,IAAA,SAAS,GAAG,IAAI,GAAG,EAA4B;IAC/C,MAAM,GAAuB,EAAE;AAE/B,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAA0B;AAErD,IAAA,cAAc;AAEtB,IAAA,WAAA,CACS,EAAU,EACV,WAAgB,EACf,OAAkC,EAAA;QAFnC,IAAE,CAAA,EAAA,GAAF,EAAE;QACF,IAAW,CAAA,WAAA,GAAX,WAAW;QACV,IAAO,CAAA,OAAA,GAAP,OAAO;AAEf,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,GAAG,EAAE;AACpC,QAAA,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC;;AAG5C,IAAA,MAAM,CAAC,OAAY,EAAE,IAAY,EAAE,KAAa,EAAE,QAAiC,EAAA;QACjF,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,YAAA,MAAM,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC;;QAGnC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,YAAY,CAAC,IAAI,CAAC;;AAG1B,QAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAC/B,YAAA,MAAM,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC;;AAG5C,QAAA,MAAM,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC;QAC3E,MAAM,IAAI,GAAG,EAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC;AACpC,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAEpB,QAAA,MAAM,kBAAkB,GAAG,oBAAoB,CAC7C,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,OAAO,EACP,IAAI,GAAG,EAAsB,CAC9B;QACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACjC,YAAA,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC;YACvC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC;AACpD,YAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC;;AAGnD,QAAA,OAAO,MAAK;;;;AAIV,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;gBAC3B,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;AACrC,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;gBAG5B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,oBAAA,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC;;AAEnC,aAAC,CAAC;AACJ,SAAC;;IAGH,QAAQ,CAAC,IAAY,EAAE,GAAqB,EAAA;QAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;AAE5B,YAAA,OAAO,KAAK;;aACP;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAC7B,YAAA,OAAO,IAAI;;;AAIP,IAAA,WAAW,CAAC,IAAY,EAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,MAAM,mBAAmB,CAAC,IAAI,CAAC;;AAEjC,QAAA,OAAO,OAAO;;IAGhB,OAAO,CACL,OAAY,EACZ,WAAmB,EACnB,KAAU,EACV,oBAA6B,IAAI,EAAA;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;AAC7C,QAAA,MAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC;AAE3E,QAAA,IAAI,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;QAClE,IAAI,CAAC,kBAAkB,EAAE;AACvB,YAAA,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC;YACvC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,GAAG,GAAG,GAAG,WAAW,CAAC;AAC3D,YAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAC9B,OAAO,GACN,kBAAkB,GAAG,IAAI,GAAG,EAAsB,EACpD;;QAGH,IAAI,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC;AACpD,QAAA,IAAI,CAAC,KAAK,IAAI,SAAS,EAAE;AACvB,YAAA,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;;AAG1C,QAAA,kBAAkB,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;QAE5C,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,mBAAmB;;AAGjC,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,KAAK,UAAU;;;;;;;QAQ9C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;;;AAGnD,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChD,MAAM,MAAM,GAAY,EAAE;AAC1B,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;AACjF,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;AAC3E,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC;;qBAC3B;AACL,oBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;AAC3B,wBAAA,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC;AAChC,wBAAA,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC9B,qBAAC,CAAC;;;YAGN;;AAGF,QAAA,MAAM,gBAAgB,GAAgC,oBAAoB,CACxE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC7B,OAAO,EACP,EAAE,CACH;AACD,QAAA,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;;;AAKlC,YAAA,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;gBACvF,MAAM,CAAC,OAAO,EAAE;;AAEpB,SAAC,CAAC;QAEF,IAAI,UAAU,GAAG,OAAO,CAAC,eAAe,CACtC,SAAS,CAAC,KAAK,EACf,OAAO,CAAC,KAAK,EACb,OAAO,EACP,OAAO,CAAC,MAAM,CACf;QACD,IAAI,oBAAoB,GAAG,KAAK;QAChC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,iBAAiB;gBAAE;AACxB,YAAA,UAAU,GAAG,OAAO,CAAC,kBAAkB;YACvC,oBAAoB,GAAG,IAAI;;AAG7B,QAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACjC,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,OAAO;YACP,MAAM;YACN,oBAAoB;AACrB,SAAA,CAAC;QAEF,IAAI,CAAC,oBAAoB,EAAE;AACzB,YAAA,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC;AACnC,YAAA,MAAM,CAAC,OAAO,CAAC,MAAK;AAClB,gBAAA,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC;AACxC,aAAC,CAAC;;AAGJ,QAAA,MAAM,CAAC,MAAM,CAAC,MAAK;YACjB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;AACxC,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAG/B,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC1D,IAAI,OAAO,EAAE;gBACX,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;AACnC,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;AAG9B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;AAE7B,QAAA,OAAO,MAAM;;AAGf,IAAA,UAAU,CAAC,IAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AAE3B,QAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEzE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;AACpD,YAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CACxB,OAAO,EACP,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,KAAI;AACzB,gBAAA,OAAO,KAAK,CAAC,IAAI,IAAI,IAAI;aAC1B,CAAC,CACH;AACH,SAAC,CAAC;;AAGJ,IAAA,iBAAiB,CAAC,OAAY,EAAA;QAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;AAC5C,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC;AACtC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;QACjE,IAAI,cAAc,EAAE;AAClB,YAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;YACpD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC;;;IAIzC,8BAA8B,CAAC,WAAgB,EAAE,OAAY,EAAA;AACnE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,mBAAmB,EAAE,IAAI,CAAC;;;;AAKlF,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;;;YAGvB,IAAI,GAAG,CAAC,YAAY,CAAC;gBAAE;YAEvB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC;AAC7D,YAAA,IAAI,UAAU,CAAC,IAAI,EAAE;gBACnB,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;iBAC1E;AACL,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;;AAE/B,SAAC,CAAC;;;QAIF,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,MACpC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CACvD;;AAGH,IAAA,qBAAqB,CACnB,OAAY,EACZ,OAAY,EACZ,oBAA8B,EAC9B,iBAA2B,EAAA;AAE3B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/D,QAAA,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAkB;QACxD,IAAI,aAAa,EAAE;YACjB,MAAM,OAAO,GAAgC,EAAE;YAC/C,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,WAAW,KAAI;gBAC3C,sBAAsB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC;;;gBAGpD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AACnC,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,CAAC;oBAChF,IAAI,MAAM,EAAE;AACV,wBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAG1B,aAAC,CAAC;AAEF,YAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,gBAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,sBAAsB,CAAC;gBAC1F,IAAI,oBAAoB,EAAE;AACxB,oBAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;;AAEnF,gBAAA,OAAO,IAAI;;;AAGf,QAAA,OAAO,KAAK;;AAGd,IAAA,8BAA8B,CAAC,OAAY,EAAA;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;AACrD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;;;AAI/D,QAAA,IAAI,SAAS,IAAI,aAAa,EAAE;AAC9B,YAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU;AACzC,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC7B,gBAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI;AACjC,gBAAA,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC;oBAAE;AACtC,gBAAA,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC;gBAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAE;AAChD,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,kBAAkB;gBAC7C,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,mBAAmB;AACvE,gBAAA,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC;AAC1C,gBAAA,MAAM,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC;AAE3E,gBAAA,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACjC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACf,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,MAAM;AACN,oBAAA,oBAAoB,EAAE,IAAI;AAC3B,iBAAA,CAAC;AACJ,aAAC,CAAC;;;IAIN,UAAU,CAAC,OAAY,EAAE,OAAY,EAAA;AACnC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO;AAC3B,QAAA,IAAI,OAAO,CAAC,iBAAiB,EAAE;AAC7B,YAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,OAAO,CAAC;;;QAIvD,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;YAAE;;;QAIxD,IAAI,iCAAiC,GAAG,KAAK;AAC7C,QAAA,IAAI,MAAM,CAAC,eAAe,EAAE;AAC1B,YAAA,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC;kBAClC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO;kBAC1C,EAAE;;;;;AAMN,YAAA,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;gBAC3C,iCAAiC,GAAG,IAAI;;iBACnC;gBACL,IAAI,MAAM,GAAG,OAAO;gBACpB,QAAQ,MAAM,GAAG,MAAM,CAAC,UAAU,GAAG;oBACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnD,IAAI,QAAQ,EAAE;wBACZ,iCAAiC,GAAG,IAAI;wBACxC;;;;;;;;;AAUR,QAAA,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC;;;QAI5C,IAAI,iCAAiC,EAAE;AACrC,YAAA,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;;aACxD;AACL,YAAA,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;AACzC,YAAA,IAAI,CAAC,WAAW,IAAI,WAAW,KAAK,kBAAkB,EAAE;;;AAGtD,gBAAA,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACxD,gBAAA,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC;AACtC,gBAAA,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC;;;;IAKjD,UAAU,CAAC,OAAY,EAAE,MAAW,EAAA;AAClC,QAAA,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC;;AAGxC,IAAA,sBAAsB,CAAC,WAAmB,EAAA;QACxC,MAAM,YAAY,GAAuB,EAAE;QAC3C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AAC5B,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM;YAC3B,IAAI,MAAM,CAAC,SAAS;gBAAE;AAEtB,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACrD,IAAI,SAAS,EAAE;AACb,gBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAyB,KAAI;oBAC9C,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;wBACtC,MAAM,SAAS,GAAG,kBAAkB,CAClC,OAAO,EACP,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,SAAS,CAAC,KAAK,EACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CACpB;AACA,wBAAA,SAAiB,CAAC,OAAO,CAAC,GAAG,WAAW;AACzC,wBAAA,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC;;AAE9E,iBAAC,CAAC;;AAGJ,YAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;AAC3B,gBAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAK;;;oBAG3B,MAAM,CAAC,OAAO,EAAE;AAClB,iBAAC,CAAC;;iBACG;AACL,gBAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE5B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE;QAEhB,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAI;;;YAGhC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;YACpC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;YACpC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACtB,OAAO,EAAE,GAAG,EAAE;;YAEhB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE;AAC3E,SAAC,CAAC;;AAGJ,IAAA,OAAO,CAAC,OAAY,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC;;AAEjE;MAQY,yBAAyB,CAAA;AA6B3B,IAAA,QAAA;AACA,IAAA,MAAA;AACC,IAAA,WAAA;IA9BH,OAAO,GAAgC,EAAE;AACzC,IAAA,eAAe,GAAG,IAAI,GAAG,EAAqC;AAC9D,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAoC;AAC9D,IAAA,uBAAuB,GAAG,IAAI,GAAG,EAAoC;AACrE,IAAA,eAAe,GAAG,IAAI,GAAG,EAAgC;AACzD,IAAA,aAAa,GAAG,IAAI,GAAG,EAAO;IAE9B,eAAe,GAAG,CAAC;IACnB,kBAAkB,GAAG,CAAC;IAErB,gBAAgB,GAAiD,EAAE;IACnE,cAAc,GAAmC,EAAE;IACnD,SAAS,GAAkB,EAAE;IAC7B,aAAa,GAAkB,EAAE;AAElC,IAAA,uBAAuB,GAAG,IAAI,GAAG,EAAqC;IACtE,sBAAsB,GAAU,EAAE;IAClC,sBAAsB,GAAU,EAAE;;IAGlC,iBAAiB,GAAG,CAAC,OAAY,EAAE,OAAY,KAAM,GAAC;;IAG7D,kBAAkB,CAAC,OAAY,EAAE,OAAY,EAAA;AAC3C,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC;;AAG1C,IAAA,WAAA,CACS,QAAa,EACb,MAAuB,EACtB,WAAqC,EAAA;QAFtC,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAM,CAAA,MAAA,GAAN,MAAM;QACL,IAAW,CAAA,WAAA,GAAX,WAAW;;AAGrB,IAAA,IAAI,aAAa,GAAA;QACf,MAAM,OAAO,GAAgC,EAAE;QAC/C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;YACjC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC5B,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;AAExB,aAAC,CAAC;AACJ,SAAC,CAAC;AACF,QAAA,OAAO,OAAO;;IAGhB,eAAe,CAAC,WAAmB,EAAE,WAAgB,EAAA;QACnD,MAAM,EAAE,GAAG,IAAI,4BAA4B,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC;AAC3E,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE;AAC5E,YAAA,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,WAAW,CAAC;;aACtC;;;;YAIL,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;;;;;;AAOzC,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC;;QAEvC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE;;IAGzC,qBAAqB,CAAC,EAAgC,EAAE,WAAgB,EAAA;AAC9E,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;AACzC,QAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,uBAAuB;AAC5D,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,KAAK,GAAG,KAAK;;;YAGjB,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC;YACxD,OAAO,QAAQ,EAAE;gBACf,MAAM,UAAU,GAAG,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACxD,IAAI,UAAU,EAAE;;;oBAGd,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC/C,aAAa,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtC,KAAK,GAAG,IAAI;oBACZ;;gBAEF,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;YAEnD,IAAI,CAAC,KAAK,EAAE;;;;AAIV,gBAAA,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;;;aAEtB;AACL,YAAA,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;;AAGxB,QAAA,uBAAuB,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;AAC5C,QAAA,OAAO,EAAE;;IAGX,QAAQ,CAAC,WAAmB,EAAE,WAAgB,EAAA;QAC5C,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAC,EAAE,EAAE;YACP,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC;;AAErD,QAAA,OAAO,EAAE;;AAGX,IAAA,eAAe,CAAC,WAAmB,EAAE,IAAY,EAAE,OAAyB,EAAA;QAC1E,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAC3C,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,eAAe,EAAE;;;IAI1B,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAA;AACvC,QAAA,IAAI,CAAC,WAAW;YAAE;QAClB,IAAI,CAAC,UAAU,CAAC,MAAO,GAAC,CAAC;AAEzB,QAAA,IAAI,CAAC,wBAAwB,CAAC,MAAK;YACjC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;YAC5C,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7C,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAEtC,YAAA,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;AACnB,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;AAC3C,SAAC,CAAC;;AAGI,IAAA,eAAe,CAAC,EAAU,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;;AAGlC,IAAA,wBAAwB,CAAC,OAAY,EAAA;;;;;;AAMnC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAgC;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;QACvD,IAAI,aAAa,EAAE;YACjB,KAAK,IAAI,UAAU,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE;AAC7C,gBAAA,IAAI,UAAU,CAAC,WAAW,EAAE;oBAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC;oBACvD,IAAI,EAAE,EAAE;AACN,wBAAA,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;;;;;AAK1B,QAAA,OAAO,UAAU;;AAGnB,IAAA,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAE,IAAY,EAAE,KAAU,EAAA;AACjE,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;YAC5C,IAAI,EAAE,EAAE;gBACN,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC;AAChC,gBAAA,OAAO,IAAI;;;AAGf,QAAA,OAAO,KAAK;;AAGd,IAAA,UAAU,CAAC,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB,EAAA;AAC9E,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAAE;;;AAI7B,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B;AAC9D,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;AACpC,YAAA,OAAO,CAAC,aAAa,GAAG,KAAK;AAC7B,YAAA,OAAO,CAAC,UAAU,GAAG,IAAI;YACzB,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC;AAC1D,YAAA,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;;;;;QAOhD,IAAI,WAAW,EAAE;YACf,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;;;;;;;YAO5C,IAAI,EAAE,EAAE;AACN,gBAAA,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC;;;;QAKlC,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;;;AAIrC,IAAA,mBAAmB,CAAC,OAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC;;IAG3C,qBAAqB,CAAC,OAAY,EAAE,KAAc,EAAA;QAChD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/B,gBAAA,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC;;;aAElC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC1C,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC;AAClC,YAAA,WAAW,CAAC,OAAO,EAAE,kBAAkB,CAAC;;;AAI5C,IAAA,UAAU,CAAC,WAAmB,EAAE,OAAY,EAAE,OAAY,EAAA;AACxD,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,MAAM,EAAE,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,IAAI;YACjE,IAAI,EAAE,EAAE;AACN,gBAAA,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC;;iBAC1B;gBACL,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;;YAGjE,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;YACxD,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,EAAE;AACvC,gBAAA,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC;;;aAEhC;AACL,YAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC;;;IAI7C,oBAAoB,CAClB,WAAmB,EACnB,OAAY,EACZ,YAAsB,EACtB,OAAa,EACb,sBAA4C,EAAA;AAE5C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC;QACzC,OAAO,CAAC,YAAY,CAAC,GAAG;YACtB,WAAW;AACX,YAAA,aAAa,EAAE,OAAO;YACtB,YAAY;AACZ,YAAA,oBAAoB,EAAE,KAAK;YAC3B,sBAAsB;SACvB;;IAGH,MAAM,CACJ,WAAmB,EACnB,OAAY,EACZ,IAAY,EACZ,KAAa,EACb,QAAiC,EAAA;AAEjC,QAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;;AAEjF,QAAA,OAAO,MAAO,GAAC;;IAGT,iBAAiB,CACvB,KAAuB,EACvB,YAAmC,EACnC,cAAsB,EACtB,cAAsB,EACtB,YAAsB,EAAA;QAEtB,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAC3B,IAAI,CAAC,MAAM,EACX,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,SAAS,CAAC,KAAK,EACrB,KAAK,CAAC,OAAO,CAAC,KAAK,EACnB,cAAc,EACd,cAAc,EACd,KAAK,CAAC,SAAS,CAAC,OAAO,EACvB,KAAK,CAAC,OAAO,CAAC,OAAO,EACrB,YAAY,EACZ,YAAY,CACb;;AAGH,IAAA,sBAAsB,CAAC,gBAAqB,EAAA;AAC1C,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,IAAI,CAAC;AAC7E,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,CAAC;AAE9E,QAAA,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC;YAAE;AAE5C,QAAA,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,IAAI,CAAC;AAC3E,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;;AAGpF,IAAA,iCAAiC,CAAC,OAAY,EAAA;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;QAClD,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;;AAIzB,gBAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,oBAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI;;qBACzB;oBACL,MAAM,CAAC,OAAO,EAAE;;AAEpB,aAAC,CAAC;;;AAIN,IAAA,qCAAqC,CAAC,OAAY,EAAA;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;QACzD,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;;;IAIhD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;AACnC,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACvB,gBAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,OAAO,EAAE,CAAC;;iBAC3D;AACL,gBAAA,OAAO,EAAE;;AAEb,SAAC,CAAC;;AAGJ,IAAA,gBAAgB,CAAC,OAAY,EAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B;AAC9D,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;;AAEpC,YAAA,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB;AAC1C,YAAA,IAAI,OAAO,CAAC,WAAW,EAAE;AACvB,gBAAA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBACpC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;gBACpD,IAAI,EAAE,EAAE;AACN,oBAAA,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC;;;YAGjC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC;;QAGzD,IAAI,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACnD,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC;;AAG5C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACnE,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC;AACzC,SAAC,CAAC;;IAGJ,KAAK,CAAC,WAAsB,GAAA,EAAE,EAAA;QAC5B,IAAI,OAAO,GAAsB,EAAE;AACnC,QAAA,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,KAAK,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AACtF,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;;QAG9B,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;AAC9D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC1C,gBAAA,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC;;;AAIjC,QAAA,IACE,IAAI,CAAC,cAAc,CAAC,MAAM;aACzB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAC/D;YACA,MAAM,UAAU,GAAe,EAAE;AACjC,YAAA,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC;;oBAChD;AACR,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,oBAAA,UAAU,CAAC,CAAC,CAAC,EAAE;;;;aAGd;AACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC9C,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;AAIlC,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC;AAC3B,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE;AAEnB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;;AAI7B,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa;AACnC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;AAEvB,YAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,gBAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAK;oBACvC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAChC,iBAAC,CAAC;;iBACG;gBACL,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;;;;AAKpC,IAAA,WAAW,CAAC,MAAe,EAAA;AACzB,QAAA,MAAM,wBAAwB,CAAC,MAAM,CAAC;;IAGhC,gBAAgB,CACtB,UAAsB,EACtB,WAAmB,EAAA;AAEnB,QAAA,MAAM,YAAY,GAAG,IAAI,qBAAqB,EAAE;QAChD,MAAM,cAAc,GAAgC,EAAE;AACtD,QAAA,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAA0B;QAC3D,MAAM,kBAAkB,GAAuB,EAAE;AACjD,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAoC;AACnE,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAoB;AACvD,QAAA,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAoB;AAExD,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO;QAC1C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAClC,YAAA,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AAC7B,YAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC;AAC3E,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpD,mBAAmB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;AAEpD,SAAC,CAAC;AAEF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAClE,MAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC;;;;AAKlF,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe;QAC9C,IAAI,CAAC,GAAG,CAAC;QACT,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,MAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE;AACvC,YAAA,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;AACpC,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACpD,SAAC,CAAC;QAEF,MAAM,aAAa,GAAU,EAAE;AAC/B,QAAA,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAO;AACvC,QAAA,MAAM,2BAA2B,GAAG,IAAI,GAAG,EAAO;AAClD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC9C,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B;AAC9D,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE;AACpC,gBAAA,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;AAC3B,gBAAA,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7B,gBAAA,IAAI,OAAO,CAAC,YAAY,EAAE;AACxB,oBAAA,IAAI,CAAC;AACF,yBAAA,KAAK,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI;AAClC,yBAAA,OAAO,CAAC,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;qBACzC;AACL,oBAAA,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC;;;;AAK9C,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAe;AAC9C,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnF,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,MAAM,SAAS,GAAG,eAAe,GAAG,CAAC,EAAE;AACvC,YAAA,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;AACpC,YAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACpD,SAAC,CAAC;AAEF,QAAA,UAAU,CAAC,IAAI,CAAC,MAAK;YACnB,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;gBACnC,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAE;AAC5C,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACvD,aAAC,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;gBACnC,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAE;AAC5C,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACvD,aAAC,CAAC;AAEF,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AAChC,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AAChC,aAAC,CAAC;AACJ,SAAC,CAAC;QAEF,MAAM,UAAU,GAAgC,EAAE;QAClD,MAAM,oBAAoB,GAAqC,EAAE;AACjE,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACxD,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YACjC,EAAE,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvD,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM;AAC3B,gBAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAC7B,gBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;AAEvB,gBAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE;AACtC,oBAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B;;;AAG9D,oBAAA,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;wBACjC,IACE,OAAO,CAAC,sBAAsB;4BAC9B,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EACrD;AACA,4BAAA,MAAM,aAAa,GAAG,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAW;;;AAIrF,4BAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;4BAClE,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;gCACnE,MAAM,KAAK,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAE;AACxD,gCAAA,KAAK,CAAC,KAAK,GAAG,aAAa;gCAC3B,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC;;;wBAIpD,MAAM,CAAC,OAAO,EAAE;wBAChB;;;AAIJ,gBAAA,MAAM,cAAc,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACnF,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAE;gBACpD,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAE;AACpD,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CACxC,KAAK,EACL,YAAY,EACZ,cAAc,EACd,cAAc,EACd,cAAc,CACd;gBACF,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;AACnD,oBAAA,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC;oBACtC;;;;;;gBAOF,IAAI,cAAc,EAAE;AAClB,oBAAA,MAAM,CAAC,OAAO,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;AAClE,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChE,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC3B;;;;;AAMF,gBAAA,IAAI,KAAK,CAAC,oBAAoB,EAAE;AAC9B,oBAAA,MAAM,CAAC,OAAO,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;AAClE,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChE,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC3B;;;;;;;gBAQF,MAAM,SAAS,GAAmC,EAAE;gBACpD,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;AACnC,oBAAA,EAAE,CAAC,uBAAuB,GAAG,IAAI;AACjC,oBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;AACvC,wBAAA,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;;AAEtB,iBAAC,CAAC;AACF,gBAAA,WAAW,CAAC,SAAS,GAAG,SAAS;gBAEjC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC;gBAEnD,MAAM,KAAK,GAAG,EAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAC;AAE5C,gBAAA,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;gBAE9B,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,KAC1C,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAChE;gBAED,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;AACvD,oBAAA,IAAI,SAAS,CAAC,IAAI,EAAE;wBAClB,IAAI,MAAM,GAAgB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAE;wBAC3D,IAAI,CAAC,MAAM,EAAE;AACX,4BAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,GAAG,IAAI,GAAG,EAAU,EAAE;;AAEhE,wBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;AAEpD,iBAAC,CAAC;gBAEF,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,OAAO,KAAI;oBACxD,IAAI,MAAM,GAAgB,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAE;oBAC5D,IAAI,CAAC,MAAM,EAAE;AACX,wBAAA,oBAAoB,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,GAAG,IAAI,GAAG,EAAU,EAAE;;AAEjE,oBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,iBAAC,CAAC;AACJ,aAAC,CAAC;;AAGJ,QAAA,IAAI,oBAAoB,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM,GAAY,EAAE;AAC1B,YAAA,oBAAoB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AAC3C,gBAAA,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,MAAO,CAAC,CAAC;AAC7E,aAAC,CAAC;AAEF,YAAA,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;AAChD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;;AAG1B,QAAA,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAoC;;;;;AAKzE,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAY;AAC/C,QAAA,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACnC,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AAC7B,YAAA,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC7B,gBAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AACzC,gBAAA,IAAI,CAAC,qBAAqB,CACxB,KAAK,CAAC,MAAM,CAAC,WAAW,EACxB,KAAK,CAAC,WAAW,EACjB,qBAAqB,CACtB;;AAEL,SAAC,CAAC;AAEF,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAChC,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;YAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,EACP,KAAK,EACL,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,WAAW,EAClB,IAAI,CACL;AACD,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;AACrC,gBAAA,oBAAoB,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;gBACzE,UAAU,CAAC,OAAO,EAAE;AACtB,aAAC,CAAC;AACJ,SAAC,CAAC;;;;;;;;QASF,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,KAAI;YACjD,OAAO,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC;AAChF,SAAC,CAAC;;AAGF,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAsB;AACnD,QAAA,MAAM,oBAAoB,GAAG,qBAAqB,CAChD,aAAa,EACb,IAAI,CAAC,MAAM,EACX,2BAA2B,EAC3B,oBAAoB,EACpB,UAAU,CACX;AAED,QAAA,oBAAoB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACpC,IAAI,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,CAAC,EAAE;AAC3E,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;AAE3B,SAAC,CAAC;;AAGF,QAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAsB;QAClD,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnC,YAAA,qBAAqB,CACnB,YAAY,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,GAAG,CAAC,KAAK,CAAC,EACd,mBAAmB,EACnBA,UAAS,CACV;AACH,SAAC,CAAC;AAEF,QAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YAC5B,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;YACpC,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AAClC,YAAA,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3F,SAAC,CAAC;QAEF,MAAM,WAAW,GAAgC,EAAE;QACnD,MAAM,UAAU,GAAgC,EAAE;QAClD,MAAM,oCAAoC,GAAG,EAAE;AAC/C,QAAA,kBAAkB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YACnC,MAAM,EAAC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAC,GAAG,KAAK;;;AAG5C,YAAA,IAAI,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC7B,gBAAA,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,oBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChE,oBAAA,MAAM,CAAC,QAAQ,GAAG,IAAI;AACtB,oBAAA,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC;AAC/C,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC3B;;;;;;;;gBASF,IAAI,mBAAmB,GAAQ,oCAAoC;AACnE,gBAAA,IAAI,mBAAmB,CAAC,IAAI,GAAG,CAAC,EAAE;oBAChC,IAAI,GAAG,GAAG,OAAO;oBACjB,MAAM,YAAY,GAAU,EAAE;oBAC9B,QAAQ,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG;wBAC7B,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC;wBACnD,IAAI,cAAc,EAAE;4BAClB,mBAAmB,GAAG,cAAc;4BACpC;;AAEF,wBAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;;AAExB,oBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;;gBAGxF,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CACtC,MAAM,CAAC,WAAW,EAClB,WAAW,EACX,qBAAqB,EACrB,iBAAiB,EACjB,YAAY,EACZ,aAAa,CACd;AAED,gBAAA,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC;AAEjC,gBAAA,IAAI,mBAAmB,KAAK,oCAAoC,EAAE;AAChE,oBAAA,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;;qBACnB;oBACL,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,CAAC;AACpE,oBAAA,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;AACzC,wBAAA,MAAM,CAAC,YAAY,GAAG,mBAAmB,CAAC,aAAa,CAAC;;AAE1D,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;;iBAExB;AACL,gBAAA,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC;AAC5C,gBAAA,MAAM,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;;;;AAIhE,gBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;AACvB,gBAAA,IAAI,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,oBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAGjC,SAAC,CAAC;;AAGF,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;;;YAG5B,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/D,YAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;AACjD,gBAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;AAC1D,gBAAA,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC;;AAErC,SAAC,CAAC;;;;AAKF,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAChC,YAAA,IAAI,MAAM,CAAC,YAAY,EAAE;AACvB,gBAAA,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC;;iBACvC;gBACL,MAAM,CAAC,OAAO,EAAE;;AAEpB,SAAC,CAAC;;;;AAKF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC;AAChC,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAA0B;AAC9D,YAAA,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC;;;;AAKrC,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY;gBAAE;YAErC,IAAI,OAAO,GAAgC,EAAE;;;;AAK7C,YAAA,IAAI,eAAe,CAAC,IAAI,EAAE;gBACxB,IAAI,oBAAoB,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC;AACvD,gBAAA,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EAAE;AACvD,oBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC;;AAGvC,gBAAA,IAAI,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,CAAC;AAClF,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpD,IAAI,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;AACjE,oBAAA,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;AAC3C,wBAAA,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;;;;AAKrC,YAAA,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;AACzD,YAAA,IAAI,aAAa,CAAC,MAAM,EAAE;AACxB,gBAAA,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC;;iBACtD;AACL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;;;;AAKlC,QAAA,aAAa,CAAC,MAAM,GAAG,CAAC;AAExB,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACzB,YAAA,MAAM,CAAC,MAAM,CAAC,MAAK;gBACjB,MAAM,CAAC,OAAO,EAAE;gBAEhB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC1C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAC/B,aAAC,CAAC;YACF,MAAM,CAAC,IAAI,EAAE;AACf,SAAC,CAAC;AAEF,QAAA,OAAO,WAAW;;AAGpB,IAAA,UAAU,CAAC,QAAmB,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAG/B,IAAA,wBAAwB,CAAC,QAAmB,EAAA;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;;IAG3B,mBAAmB,CACzB,OAAe,EACf,gBAAyB,EACzB,WAAoB,EACpB,WAAoB,EACpB,YAAkB,EAAA;QAElB,IAAI,OAAO,GAAgC,EAAE;QAC7C,IAAI,gBAAgB,EAAE;YACpB,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,IAAI,qBAAqB,EAAE;gBACzB,OAAO,GAAG,qBAAqB;;;aAE5B;YACL,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC;YACzD,IAAI,cAAc,EAAE;gBAClB,MAAM,kBAAkB,GAAG,CAAC,YAAY,IAAI,YAAY,IAAI,UAAU;AACtE,gBAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;oBAChC,IAAI,MAAM,CAAC,MAAM;wBAAE;AACnB,oBAAA,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,WAAW,IAAI,WAAW;wBAAE;AAC9D,oBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACtB,iBAAC,CAAC;;;AAGN,QAAA,IAAI,WAAW,IAAI,WAAW,EAAE;YAC9B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAI;AAClC,gBAAA,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;AAAE,oBAAA,OAAO,KAAK;AAClE,gBAAA,IAAI,WAAW,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW;AAAE,oBAAA,OAAO,KAAK;AAClE,gBAAA,OAAO,IAAI;AACb,aAAC,CAAC;;AAEJ,QAAA,OAAO,OAAO;;AAGR,IAAA,qBAAqB,CAC3B,WAAmB,EACnB,WAA2C,EAC3C,qBAA4D,EAAA;AAE5D,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW;AAC3C,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO;;;AAIvC,QAAA,MAAM,iBAAiB,GAAuB,WAAW,CAAC;AACxD,cAAE;cACA,WAAW;AACf,QAAA,MAAM,iBAAiB,GAAuB,WAAW,CAAC;AACxD,cAAE;cACA,WAAW;AAEf,QAAA,KAAK,MAAM,mBAAmB,IAAI,WAAW,CAAC,SAAS,EAAE;AACvD,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO;AAC3C,YAAA,MAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW;YAChD,MAAM,OAAO,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,OAAO,EAAE,EAAE,CAAC;AACxE,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,EACP,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,WAAW,CAAC,OAAO,CACpB;AACD,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACjC,gBAAA,MAAM,UAAU,GAAI,MAAoC,CAAC,aAAa,EAAS;AAC/E,gBAAA,IAAI,UAAU,CAAC,aAAa,EAAE;oBAC5B,UAAU,CAAC,aAAa,EAAE;;gBAE5B,MAAM,CAAC,OAAO,EAAE;AAChB,gBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACtB,aAAC,CAAC;;;;AAKJ,QAAA,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,UAAU,CAAC;;IAG1C,eAAe,CACrB,WAAmB,EACnB,WAA2C,EAC3C,qBAA4D,EAC5D,iBAA8C,EAC9C,YAAqC,EACrC,aAAsC,EAAA;AAEtC,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW;AAC3C,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO;;;QAIvC,MAAM,iBAAiB,GAAgC,EAAE;AACzD,QAAA,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAO;AAC1C,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAO;QACrC,MAAM,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,mBAAmB,KAAI;AACtE,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO;AAC3C,YAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC;;AAGhC,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC;AACrC,YAAA,IAAI,OAAO,IAAI,OAAO,CAAC,oBAAoB;gBACzC,OAAO,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC;AACzF,YAAA,MAAM,gBAAgB,GAAG,OAAO,KAAK,WAAW;AAChD,YAAA,MAAM,eAAe,GAAG,mBAAmB,CACzC,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC,CACzF,CAAC,MAAM,CAAC,CAAC,CAAC,KAAI;;;;;gBAKb,MAAM,EAAE,GAAG,CAAQ;AACnB,gBAAA,OAAO,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK;AACpD,aAAC,CAAC;YAEF,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;AAE7C,YAAA,MAAM,SAAS,GAAG,kBAAkB,CAClC,IAAI,CAAC,WAAW,EAChB,mBAAmB,CAAC,SAAS,EAC7B,SAAS,EACT,UAAU,CACX;AACD,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,SAAS,EAAE,eAAe,CAAC;;;AAIjF,YAAA,IAAI,mBAAmB,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACxD,gBAAA,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;;YAG7B,IAAI,gBAAgB,EAAE;gBACpB,MAAM,aAAa,GAAG,IAAI,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC;AACtF,gBAAA,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC;AACnC,gBAAA,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;;AAGvC,YAAA,OAAO,MAAM;AACf,SAAC,CAAC;AAEF,QAAA,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACnC,YAAA,oBAAoB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACnF,YAAA,MAAM,CAAC,MAAM,CAAC,MAAM,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC/F,SAAC,CAAC;AAEF,QAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;AACnF,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC;AACjD,QAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,YAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;AACtF,YAAA,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC;AAC9C,SAAC,CAAC;;;AAIF,QAAA,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACjC,YAAA,oBAAoB,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AACnE,SAAC,CAAC;AAEF,QAAA,OAAO,MAAM;;AAGP,IAAA,YAAY,CAClB,WAAyC,EACzC,SAA+B,EAC/B,eAAkC,EAAA;AAElC,QAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CACxB,WAAW,CAAC,OAAO,EACnB,SAAS,EACT,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,MAAM,EAClB,eAAe,CAChB;;;;QAKH,OAAO,IAAI,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;;AAE1E;MAEY,yBAAyB,CAAA;AAe3B,IAAA,WAAA;AACA,IAAA,WAAA;AACA,IAAA,OAAA;AAhBD,IAAA,OAAO,GAAoB,IAAI,mBAAmB,EAAE;IACpD,mBAAmB,GAAG,KAAK;AAE3B,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAmC;IACrD,SAAS,GAAG,KAAK;IAC1B,YAAY,GAA2B,IAAI;IAE3C,gBAAgB,GAAY,KAAK;IACjC,QAAQ,GAAG,KAAK;IAEd,MAAM,GAAY,IAAI;IACf,SAAS,GAAW,CAAC;AAErC,IAAA,WAAA,CACS,WAAmB,EACnB,WAAmB,EACnB,OAAY,EAAA;QAFZ,IAAW,CAAA,WAAA,GAAX,WAAW;QACX,IAAW,CAAA,WAAA,GAAX,WAAW;QACX,IAAO,CAAA,OAAA,GAAP,OAAO;;AAGhB,IAAA,aAAa,CAAC,MAAuB,EAAA;QACnC,IAAI,IAAI,CAAC,mBAAmB;YAAE;AAE9B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM;QACrB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;AACjD,YAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrF,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AAC7B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;AAC/B,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC;AACvC,QAAA,IAAuB,CAAC,MAAM,GAAG,KAAK;;IAGzC,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,OAAO;;AAGrB,IAAA,iBAAiB,CAAC,SAAiB,EAAA;AAChC,QAAA,IAAY,CAAC,SAAS,GAAG,SAAS;;AAGrC,IAAA,gBAAgB,CAAC,MAAuB,EAAA;AACtC,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,OAAc;AAC7B,QAAA,IAAI,CAAC,CAAC,eAAe,EAAE;AACrB,YAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC;;QAEnD,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QAClC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;;IAGhC,WAAW,CAAC,IAAY,EAAE,QAA6B,EAAA;AAC7D,QAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAGtE,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;;AAE9B,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;;AAGzB,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;;AAE/B,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;;AAG1B,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;;AAEjC,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;;IAG5B,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;IAGrB,UAAU,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;;IAGxD,IAAI,GAAA;QACF,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;IAGrC,KAAK,GAAA;QACH,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;;IAGtC,OAAO,GAAA;QACL,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;IAGxC,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;IAGvB,OAAO,GAAA;AACJ,QAAA,IAA6B,CAAC,SAAS,GAAG,IAAI;AAC/C,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;IAGxB,KAAK,GAAA;QACH,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;;AAGtC,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;;;IAI/B,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;;;AAIrD,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,CAAC,GAAG,IAAI,CAAC,OAAc;AAC7B,QAAA,IAAI,CAAC,CAAC,eAAe,EAAE;AACrB,YAAA,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC;;;AAGjC;AAED,SAAS,kBAAkB,CAAO,GAAgB,EAAE,GAAM,EAAE,KAAQ,EAAA;IAClE,IAAI,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,IAAI,aAAa,EAAE;AACjB,QAAA,IAAI,aAAa,CAAC,MAAM,EAAE;YACxB,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C,YAAA,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;AAEhC,QAAA,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,YAAA,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;;;AAGnB,IAAA,OAAO,aAAa;AACtB;AAEA,SAAS,qBAAqB,CAAC,KAAU,EAAA;;;;IAIvC,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI;AACrC;AAEA,SAAS,aAAa,CAAC,IAAS,EAAA;IAC9B,OAAO,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACvC;AAEA,SAAS,mBAAmB,CAAC,SAAiB,EAAA;AAC5C,IAAA,OAAO,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,MAAM;AACpD;AAEA,SAAS,YAAY,CAAC,OAAY,EAAE,KAAc,EAAA;AAChD,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO;AACtC,IAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM;AACtD,IAAA,OAAO,QAAQ;AACjB;AAEA,SAAS,qBAAqB,CAC5B,SAAkC,EAClC,MAAuB,EACvB,QAAkB,EAClB,eAAsC,EACtC,YAAoB,EAAA;IAEpB,MAAM,SAAS,GAAa,EAAE;AAC9B,IAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IAEpE,MAAM,cAAc,GAAU,EAAE;IAEhC,eAAe,CAAC,OAAO,CAAC,CAAC,KAAkB,EAAE,OAAY,KAAI;AAC3D,QAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE;AACvC,QAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC;AAC9D,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;;;YAIvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC/B,gBAAA,OAAO,CAAC,YAAY,CAAC,GAAG,0BAA0B;AAClD,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;;AAEhC,SAAC,CAAC;AACF,QAAA,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;AAChC,KAAC,CAAC;;;IAIF,IAAI,CAAC,GAAG,CAAC;AACT,IAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAEpE,IAAA,OAAO,cAAc;AACvB;AAEA;;;;;;;;;AASG;AACH,SAAS,YAAY,CAAC,KAAY,EAAE,KAAY,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,IAAI,GAAG,EAAc;AACrC,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAE9C,IAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAAE,QAAA,OAAO,OAAO;IAErC,MAAM,SAAS,GAAG,CAAC;AACnB,IAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC;AAC9B,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAY;IAExC,SAAS,OAAO,CAAC,IAAS,EAAA;AACxB,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,OAAO,SAAS;QAE3B,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI;AAErB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU;AAC9B,QAAA,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAEvB,IAAI,GAAG,MAAM;;AACR,aAAA,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;YAE9B,IAAI,GAAG,SAAS;;aACX;;AAEL,YAAA,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;;AAGxB,QAAA,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAC5B,QAAA,OAAO,IAAI;;AAGb,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEjC,KAAC,CAAC;AAEF,IAAA,OAAO,OAAO;AAChB;AAEA,SAAS,QAAQ,CAAC,OAAY,EAAE,SAAiB,EAAA;AAC/C,IAAA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC;AACnC;AAEA,SAAS,WAAW,CAAC,OAAY,EAAE,SAAiB,EAAA;AAClD,IAAA,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;AACtC;AAEA,SAAS,6BAA6B,CACpC,MAAiC,EACjC,OAAY,EACZ,OAA0B,EAAA;AAE1B,IAAA,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7E;AAEA,SAAS,mBAAmB,CAAC,OAA0B,EAAA;IACrD,MAAM,YAAY,GAAsB,EAAE;AAC1C,IAAA,yBAAyB,CAAC,OAAO,EAAE,YAAY,CAAC;AAChD,IAAA,OAAO,YAAY;AACrB;AAEA,SAAS,yBAAyB,CAAC,OAA0B,EAAE,YAA+B,EAAA;AAC5F,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;AACzB,QAAA,IAAI,MAAM,YAAY,oBAAoB,EAAE;AAC1C,YAAA,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC;;aAClD;AACL,YAAA,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAG/B;AAEA,SAAS,SAAS,CAAC,CAAuB,EAAE,CAAuB,EAAA;IACjE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACzB,IAAA,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM;AAAE,QAAA,OAAO,KAAK;AACxC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,QAAA,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AAClB,QAAA,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAAE,YAAA,OAAO,KAAK;;AAElE,IAAA,OAAO,IAAI;AACb;AAEA,SAAS,sBAAsB,CAC7B,OAAY,EACZ,mBAA0C,EAC1C,oBAA2C,EAAA;IAE3C,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC;AACnD,IAAA,IAAI,CAAC,SAAS;AAAE,QAAA,OAAO,KAAK;IAE5B,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC;IAC/C,IAAI,QAAQ,EAAE;AACZ,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,QAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;SAC3C;AACL,QAAA,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;;AAG7C,IAAA,oBAAoB,CAAC,MAAM,CAAC,OAAO,CAAC;AACpC,IAAA,OAAO,IAAI;AACb;;MC73Da,eAAe,CAAA;AAWhB,IAAA,OAAA;AACA,IAAA,WAAA;AAXF,IAAA,iBAAiB;AACjB,IAAA,eAAe;IAEf,aAAa,GAAsC,EAAE;;IAGtD,iBAAiB,GAAG,CAAC,OAAY,EAAE,OAAY,KAAM,GAAC;AAE7D,IAAA,WAAA,CACE,GAAa,EACL,OAAwB,EACxB,WAAqC,EAAA;QADrC,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAW,CAAA,WAAA,GAAX,WAAW;AAEnB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,yBAAyB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AACtF,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;QAElF,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,CAAC,OAAY,EAAE,OAAY,KACpE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC;;IAG5C,eAAe,CACb,WAAmB,EACnB,WAAmB,EACnB,WAAgB,EAChB,IAAY,EACZ,QAAkC,EAAA;AAElC,QAAA,MAAM,QAAQ,GAAG,WAAW,GAAG,GAAG,GAAG,IAAI;QACzC,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,MAAM,GAAY,EAAE;YAC1B,MAAM,QAAQ,GAAa,EAAE;AAC7B,YAAA,MAAM,GAAG,GAAG,iBAAiB,CAC3B,IAAI,CAAC,OAAO,EACZ,QAA6B,EAC7B,MAAM,EACN,QAAQ,CACK;AACf,YAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,gBAAA,MAAM,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC;;AAExC,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,gBAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,oBAAA,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC;;;YAGpC,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC;AACnD,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,OAAO;;QAExC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;;IAGpE,QAAQ,CAAC,WAAmB,EAAE,WAAgB,EAAA;QAC5C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC;;IAG3D,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAA;QACvC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;;AAGtD,IAAA,QAAQ,CAAC,WAAmB,EAAE,OAAY,EAAE,MAAW,EAAE,YAAqB,EAAA;AAC5E,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC;;AAG/E,IAAA,QAAQ,CAAC,WAAmB,EAAE,OAAY,EAAE,OAAY,EAAA;QACtD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;;IAGlE,iBAAiB,CAAC,OAAY,EAAE,OAAgB,EAAA;QAC9C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC;;AAGhE,IAAA,OAAO,CAAC,WAAmB,EAAE,OAAY,EAAE,QAAgB,EAAE,KAAU,EAAA;QACrE,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC7B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YACnD,MAAM,IAAI,GAAG,KAAc;AAC3B,YAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;;aAClD;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;;;IAIzE,MAAM,CACJ,WAAmB,EACnB,OAAY,EACZ,SAAiB,EACjB,UAAkB,EAClB,QAA6B,EAAA;;QAG7B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;YAC9B,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC;AACpD,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;;AAEnE,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;;IAG7F,KAAK,CAAC,WAAsB,GAAA,EAAE,EAAA;AAC5B,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC;;AAG3C,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;;IAG7E,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE;;AAGnD,IAAA,wBAAwB,CAAC,EAAgB,EAAA;AACvC,QAAA,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,EAAE,CAAC;;AAEtD;;AC/HD;;;;;;;;;;AAUG;AACa,SAAA,0BAA0B,CACxC,OAAY,EACZ,MAA4C,EAAA;IAE5C,IAAI,WAAW,GAAyB,IAAI;IAC5C,IAAI,SAAS,GAAyB,IAAI;IAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;QAC1C,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClD,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,YAAA,SAAS,GAAG,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;;AAE7D,SAAA,IAAI,MAAM,YAAY,GAAG,EAAE;AAChC,QAAA,WAAW,GAAG,yBAAyB,CAAC,MAAM,CAAC;;AAGjD,IAAA,OAAO,WAAW,IAAI,SAAS,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,IAAI;AAClG;AAEA;;;;;;;AAOG;MACU,kBAAkB,CAAA;AAOnB,IAAA,QAAA;AACA,IAAA,YAAA;AACA,IAAA,UAAA;IARV,OAAO,sBAAsB,mBAAmB,IAAI,OAAO,EAAsB;AAEzE,IAAA,MAAM,GAAmC,CAAA;AACzC,IAAA,cAAc;AAEtB,IAAA,WAAA,CACU,QAAa,EACb,YAAkC,EAClC,UAAgC,EAAA;QAFhC,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAU,CAAA,UAAA,GAAV,UAAU;QAElB,IAAI,aAAa,GAAG,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,GAAG,aAAa,GAAG,IAAI,GAAG,EAAE,EAAE;;AAEtF,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa;;IAGrC,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,wCAAoC;AACjD,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC;;YAElE,IAAI,CAAC,MAAM,GAAA,CAAA;;;IAIf,MAAM,GAAA;QACJ,IAAI,CAAC,KAAK,EAAE;AACZ,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,yCAAqC;YAClD,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC;AAC7C,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;AACzC,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;YAExB,IAAI,CAAC,MAAM,GAAA,CAAA;;;IAIf,OAAO,GAAA;QACL,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,IAAI,CAAC,MAAM,GAAA,CAAA,0CAAsC;YACnD,kBAAkB,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/D,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC;AAC7C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAExB,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC;AAC3C,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;YAExB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC;YAC7C,IAAI,CAAC,MAAM,GAAA,CAAA;;;;AAsBjB,SAAS,yBAAyB,CAAC,MAAqB,EAAA;IACtD,IAAI,MAAM,GAAyB,IAAI;IACvC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAC3B,QAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,MAAM,GAAG,MAAM,IAAI,IAAI,GAAG,EAAE;AAC5B,YAAA,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;AAEzB,KAAC,CAAC;AACF,IAAA,OAAO,MAAM;AACf;AAEA,SAAS,oBAAoB,CAAC,IAAY,EAAA;AACxC,IAAA,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU;AAClD;;MC3Ha,mBAAmB,CAAA;AA0BrB,IAAA,OAAA;AACA,IAAA,SAAA;AACA,IAAA,OAAA;AACC,IAAA,cAAA;IA5BF,UAAU,GAAe,EAAE;IAC3B,WAAW,GAAe,EAAE;IAC5B,aAAa,GAAe,EAAE;AAC9B,IAAA,SAAS;AACT,IAAA,MAAM;IACN,YAAY,GAAG,KAAK;IACpB,SAAS,GAAG,KAAK;IACjB,QAAQ,GAAG,KAAK;IAChB,UAAU,GAAG,KAAK;AAClB,IAAA,cAAc;;;;IAKd,kBAAkB,GAAe,EAAE;IACnC,mBAAmB,GAAe,EAAE;;AAG5B,IAAA,SAAS;IAClB,IAAI,GAAG,CAAC;IAER,YAAY,GAA2B,IAAI;AAC3C,IAAA,eAAe,GAAkB,IAAI,GAAG,EAAE;AAEjD,IAAA,WAAA,CACS,OAAY,EACZ,SAA+B,EAC/B,OAAyC,EACxC,cAA0C,EAAA;QAH3C,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAS,CAAA,SAAA,GAAT,SAAS;QACT,IAAO,CAAA,OAAA,GAAP,OAAO;QACN,IAAc,CAAA,cAAA,GAAd,cAAc;AAEtB,QAAA,IAAI,CAAC,SAAS,GAAW,OAAO,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAW,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;;IAGlC,SAAS,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACrC,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;;IAIxB,IAAI,GAAA;QACF,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,yBAAyB,EAAE;;IAG1B,YAAY,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY;YAAE;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AAExB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;;AAEhC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC;QACjF,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE;QACpF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;QACvC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACnD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAK;;;;YAIlB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACxD,SAAC,CAAC;;IAGI,yBAAyB,GAAA;;AAE/B,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE;;aACtB;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;;AAIlB,IAAA,yBAAyB,CAAC,SAA+B,EAAA;QAC/D,MAAM,GAAG,GAAU,EAAE;AACrB,QAAA,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;YAC1B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACrC,SAAC,CAAC;AACF,QAAA,OAAO,GAAG;;;AAIZ,IAAA,oBAAoB,CAClB,OAAoB,EACpB,SAA+B,EAC/B,OAAY,EAAA;AAEZ,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC;;AAG5E,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;AAG3B,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;;AAG1B,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;;IAG7B,IAAI,GAAA;QACF,IAAI,CAAC,YAAY,EAAE;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACtB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACtC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AACpB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;;AAG/B,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;IAGvB,KAAK,GAAA;QACH,IAAI,CAAC,IAAI,EAAE;AACX,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;IAGxB,MAAM,GAAA;QACJ,IAAI,CAAC,IAAI,EAAE;AACX,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;;QAE9B,IAAI,CAAC,SAAS,EAAE;AAChB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;;IAGzB,KAAK,GAAA;QACH,IAAI,CAAC,oBAAoB,EAAE;AAC3B,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB;;IAGnC,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;;;IAI3B,OAAO,GAAA;QACL,IAAI,CAAC,KAAK,EAAE;QACZ,IAAI,CAAC,IAAI,EAAE;;IAGb,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ;;IAGtB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;YACtB,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,gBAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;;AAE/B,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AACxC,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE;;;AAI3B,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,IAAI,EAAE;;QAEb,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI;;IAG5C,WAAW,GAAA;;AAET,QAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;;AAGvD,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;;IAGrC,aAAa,GAAA;AACX,QAAA,MAAM,MAAM,GAAkB,IAAI,GAAG,EAAE;AACvC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;AAIrB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAe;YAC1C,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAClC,gBAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACrB,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;;AAE7E,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,eAAe,GAAG,MAAM;;;AAI/B,IAAA,eAAe,CAAC,SAAiB,EAAA;AAC/B,QAAA,MAAM,OAAO,GAAG,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;QAC1E,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAC7B,QAAA,OAAO,CAAC,MAAM,GAAG,CAAC;;AAErB;;MClMY,mBAAmB,CAAA;AAC9B,IAAA,qBAAqB,CAAC,IAAY,EAAA;;AAEhC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC;;AAEpC,QAAA,OAAO,IAAI;;AAGb,IAAA,+BAA+B,CAAC,IAAY,EAAA;;AAE1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,OAAO,GAAG,mBAAmB,CAAC,IAAI,CAAC;AACzC,YAAA,OAAO,kCAAkC,CAAC,OAAO,CAAC;;AAEpD,QAAA,OAAO,IAAI;;IAGb,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;;AAGpC,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAO,gBAAgB,CAAC,OAAO,CAAC;;AAGlC,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAG9C,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;AAC5D,QAAA,OAAO,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;;AAGpC,IAAA,OAAO,CACL,OAAY,EACZ,SAA8C,EAC9C,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU;QAC7C,MAAM,aAAa,GAAqC,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC;;;QAG/E,IAAI,MAAM,EAAE;AACV,YAAA,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM;;AAGlC,QAAA,MAAM,cAAc,GAAkB,IAAI,GAAG,EAAE;AAC/C,QAAA,MAAM,2BAA2B,IAC/B,eAAe,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,YAAY,mBAAmB,CAAC,CAC1E;AACD,QAAA,IAAI,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AACnD,YAAA,2BAA2B,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;gBAC7C,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC9E,aAAC,CAAC;;QAGJ,IAAI,UAAU,GAAGC,oBAAkB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/E,UAAU,GAAG,kCAAkC,CAAC,OAAO,EAAE,UAAU,EAAE,cAAc,CAAC;QACpF,MAAM,aAAa,GAAG,0BAA0B,CAAC,OAAO,EAAE,UAAU,CAAC;QACrE,OAAO,IAAI,mBAAmB,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC;;AAEpF;;AC/Ee,SAAA,YAAY,CAAC,IAA2B,EAAE,GAAa,EAAA;;AAErE,IAAA,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,QAAA,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,mBAAmB,EAAE,EAAE,IAAI,4BAA4B,EAAE,CAAC;;AAGhG,IAAA,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,mBAAmB,EAAE,EAAE,IAAI,4BAA4B,EAAE,CAAC;AAChG;;MCIa,SAAS,CAAA;AAGV,IAAA,OAAA;AAFF,IAAA,aAAa;IACrB,WACU,CAAA,OAAwB,EAChC,KAA8C,EAAA;QADtC,IAAO,CAAA,OAAA,GAAP,OAAO;QAGf,MAAM,MAAM,GAAY,EAAE;QAC1B,MAAM,QAAQ,GAAa,EAAE;AAC7B,QAAA,MAAM,GAAG,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC/D,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,gBAAgB,CAAC,MAAM,CAAC;;AAEhC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,cAAc,CAAC,QAAQ,CAAC;;;AAG5B,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG;;IAG1B,cAAc,CACZ,OAAY,EACZ,cAAoD,EACpD,iBAAuD,EACvD,OAAyB,EACzB,eAAuC,EAAA;AAEvC,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc;AACxC,cAAE,eAAe,CAAC,cAAc;cACf,cAAc;AACjC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB;AAC1C,cAAE,eAAe,CAAC,iBAAiB;cAClB,iBAAiB;QACpC,MAAM,MAAM,GAAQ,EAAE;AACtB,QAAA,eAAe,GAAG,eAAe,IAAI,IAAI,qBAAqB,EAAE;AAChE,QAAA,MAAM,MAAM,GAAG,uBAAuB,CACpC,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,IAAI,CAAC,aAAa,EAClB,eAAe,EACf,eAAe,EACf,KAAK,EACL,IAAI,EACJ,OAAO,EACP,eAAe,EACf,MAAM,CACP;AACD,QAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,YAAA,MAAM,cAAc,CAAC,MAAM,CAAC;;AAE9B,QAAA,OAAO,MAAM;;AAEhB;;ACrED,MAAM,gBAAgB,GAAG,GAAG;AAC5B,MAAM,uBAAuB,GAAG,YAAY;MAe/B,qBAAqB,CAAA;AAMpB,IAAA,WAAA;AACH,IAAA,QAAA;AACA,IAAA,MAAA;AACC,IAAA,UAAA;;;AAND,IAAA,KAAK,GAAgE,CAAA;AAE9E,IAAA,WAAA,CACY,WAAmB,EACtB,QAAmB,EACnB,MAAuB,EACtB,UAAuB,EAAA;QAHrB,IAAW,CAAA,WAAA,GAAX,WAAW;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAM,CAAA,MAAA,GAAN,MAAM;QACL,IAAU,CAAA,UAAA,GAAV,UAAU;;AAGpB,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;;AAG3B,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;;IAGnC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC;AACpD,QAAA,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAK;;;YAGxC,cAAc,CAAC,MAAK;AAClB,gBAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzB,aAAC,CAAC;AACJ,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,UAAU,IAAI;;IAGrB,aAAa,CAAC,IAAY,EAAE,SAAqC,EAAA;QAC/D,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC;;AAGrD,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;;AAG3C,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;;IAGxC,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;QACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC3C,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC;;IAGjE,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAE,SAAkB,IAAI,EAAA;QAC5E,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;;AAEtD,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;;AAGlE,IAAA,WAAW,CAAC,MAAW,EAAE,QAAa,EAAE,aAAuB,EAAA;;;;;AAK7D,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;;;IAInE,iBAAiB,CAAC,cAAmB,EAAE,eAAyB,EAAA;QAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC;;AAGzE,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGvC,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;;AAGxC,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAqC,EAAA;AACtF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC;;AAGxD,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAqC,EAAA;QAC1E,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC;;IAGpD,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC;;IAGlC,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC;;AAGrC,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAAuC,EAAA;AAClF,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;AAGjD,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAAuC,EAAA;QACzE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;;AAG7C,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,IAAI,IAAI,IAAI,uBAAuB,EAAE;YACzE,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC;;aAC9B;YACL,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;;;IAI9C,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;;AAGrC,IAAA,MAAM,CACJ,MAAW,EACX,SAAiB,EACjB,QAAwC,EACxC,OAAyB,EAAA;AAEzB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;;IAGzD,iBAAiB,CAAC,OAAY,EAAE,KAAc,EAAA;QACtD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC;;AAEhD;AAEK,MAAO,iBAAkB,SAAQ,qBAAqB,CAAA;AAEjD,IAAA,OAAA;IADT,WACS,CAAA,OAA6C,EACpD,WAAmB,EACnB,QAAmB,EACnB,MAAuB,EACvB,SAAsB,EAAA;QAEtB,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;QANxC,IAAO,CAAA,OAAA,GAAP,OAAO;AAOd,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW;;AAGvB,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;QACpD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,uBAAuB,EAAE;AAC5D,gBAAA,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK;AAC5C,gBAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,KAAgB,CAAC;;iBACvC;gBACL,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;;;aAE5D;YACL,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;;;AAIrC,IAAA,MAAM,CACb,MAA4C,EAC5C,SAAiB,EACjB,QAA6B,EAC7B,OAAyB,EAAA;QAEzB,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;AAC3C,YAAA,MAAM,OAAO,GAAG,wBAAwB,CAAC,MAAM,CAAC;YAChD,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7B,IAAI,KAAK,GAAG,EAAE;;;YAGd,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE;gBACtC,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,wBAAwB,CAAC,IAAI,CAAC;;AAEhD,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,KAAI;gBAC1E,MAAM,OAAO,GAAI,KAAa,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC7C,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;AACjE,aAAC,CAAC;;AAEJ,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;;AAEpE;AAED,SAAS,wBAAwB,CAAC,MAA4C,EAAA;IAC5E,QAAQ,MAAM;AACZ,QAAA,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC,IAAI;AACtB,QAAA,KAAK,UAAU;AACb,YAAA,OAAO,QAAQ;AACjB,QAAA,KAAK,QAAQ;AACX,YAAA,OAAO,MAAM;AACf,QAAA;AACE,YAAA,OAAO,MAAM;;AAEnB;AAEA,SAAS,wBAAwB,CAAC,WAAmB,EAAA;IACnD,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC;IACzC,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC;IAClD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC7C,IAAA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;AACzB;;MCxMa,wBAAwB,CAAA;AAQzB,IAAA,QAAA;AACA,IAAA,MAAA;AACA,IAAA,KAAA;IATF,UAAU,GAAW,CAAC;IACtB,YAAY,GAAW,CAAC;IACxB,yBAAyB,GAA6B,EAAE;AACxD,IAAA,cAAc,GAAG,IAAI,GAAG,EAAoC;IAC5D,aAAa,GAAG,CAAC;AAEzB,IAAA,WAAA,CACU,QAA0B,EAC1B,MAAuB,EACvB,KAAa,EAAA;QAFb,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAK,CAAA,KAAA,GAAL,KAAK;QAEb,MAAM,CAAC,iBAAiB,GAAG,CAAC,OAAY,EAAE,QAA0B,KAAI;AACtE,YAAA,QAAQ,EAAE,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC;AACtC,SAAC;;IAGH,cAAc,CAAC,WAAgB,EAAE,IAAmB,EAAA;QAClD,MAAM,kBAAkB,GAAG,EAAE;;;AAI7B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC;AAChE,QAAA,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC,EAAE;AAC9C,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc;YACjC,IAAI,QAAQ,GAAsC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;YACrE,IAAI,CAAC,QAAQ,EAAE;;;gBAGb,MAAM,iBAAiB,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;AACtD,gBAAA,QAAQ,GAAG,IAAI,qBAAqB,CAClC,kBAAkB,EAClB,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,iBAAiB,CAClB;;AAED,gBAAA,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;;AAE/B,YAAA,OAAO,QAAQ;;AAGjB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU;QACnD,IAAI,CAAC,UAAU,EAAE;QAEjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC;AAE9C,QAAA,MAAM,eAAe,GAAG,CAAC,OAAuC,KAAI;AAClE,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,gBAAA,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;;iBAC3B;AACL,gBAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;;AAE7F,SAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAqC;AACpF,QAAA,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC;AAE1C,QAAA,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC;;IAGxE,KAAK,GAAA;QACH,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACvB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;;;IAIjB,kBAAkB,GAAA;QACxB,cAAc,CAAC,MAAK;YAClB,IAAI,CAAC,YAAY,EAAE;AACrB,SAAC,CAAC;;;AAIJ,IAAA,wBAAwB,CAAC,KAAa,EAAE,EAAmB,EAAE,IAAS,EAAA;QACpE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;AAC3C,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;YAC9B;;AAGF,QAAA,MAAM,wBAAwB,GAAG,IAAI,CAAC,yBAAyB;AAC/D,QAAA,IAAI,wBAAwB,CAAC,MAAM,IAAI,CAAC,EAAE;YACxC,cAAc,CAAC,MAAK;AAClB,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAK;AAClB,oBAAA,wBAAwB,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACzC,wBAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,KAAK;wBACxB,EAAE,CAAC,IAAI,CAAC;AACV,qBAAC,CAAC;AACF,oBAAA,IAAI,CAAC,yBAAyB,GAAG,EAAE;AACrC,iBAAC,CAAC;AACJ,aAAC,CAAC;;QAEJ,wBAAwB,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;;IAG3C,GAAG,GAAA;QACD,IAAI,CAAC,aAAa,EAAE;;;AAIpB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAK;gBAChC,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;AACtC,aAAC,CAAC;;AAEJ,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;;;IAIvB,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;;AAGxC;;;AAGG;AACO,IAAA,iBAAiB,CAAC,WAAmB,EAAA;;AAE7C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;QAClB,IAAI,CAAC,QAAuD,CAAC,iBAAiB,GAAG,WAAW,CAAC;;AAEjG;;;;"}