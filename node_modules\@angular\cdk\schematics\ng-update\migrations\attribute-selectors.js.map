{"version": 3, "file": "attribute-selectors.js", "sourceRoot": "", "sources": ["attribute-selectors.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,iCAAiC;AAGjC,2DAAsD;AAEtD,mDAA8D;AAC9D,kDAAmE;AAEnE;;;GAGG;AACH,MAAa,2BAA4B,SAAQ,qBAAsB;IAAvE;;QACE,6DAA6D;QAC7D,SAAI,GAAG,IAAA,oCAAqB,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;QAEzD,2DAA2D;QAC3D,YAAO,GAAY,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAyD5C,CAAC;IAvDU,SAAS,CAAC,IAAa;QAC9B,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEQ,aAAa,CAAC,QAA0B;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAA,iCAAuB,EAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;iBACxD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;iBACtC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,eAAe,CAAC,UAA4B;QACnD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC;YAChD,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,WAAW,GAAG,CAAC;YAEpD,IAAA,iCAAuB,EAAC,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC;iBACzD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;iBACxC,OAAO,CAAC,KAAK,CAAC,EAAE,CACf,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAE;gBAChD,OAAO,EAAE,eAAe;gBACxB,WAAW,EAAE,eAAe;aAC7B,CAAC,CACH,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,OAA6B;QAC3D,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAC3E,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;QAE3E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAA,iCAAuB,EAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC;iBACnD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC;iBAC1C,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CACtB,QAAuB,EACvB,KAAa,EACb,IAAkC;QAElC,IAAI,CAAC,UAAU;aACZ,IAAI,CAAC,QAAQ,CAAC;aACd,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;aAClC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CACF;AA9DD,kEA8DC"}