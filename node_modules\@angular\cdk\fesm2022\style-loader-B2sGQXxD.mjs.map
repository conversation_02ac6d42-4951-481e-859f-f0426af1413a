{"version": 3, "file": "style-loader-B2sGQXxD.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/private/style-loader.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ApplicationRef,\n  ComponentRef,\n  createComponent,\n  EnvironmentInjector,\n  inject,\n  Injectable,\n  Injector,\n  Type,\n} from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap<\n  ApplicationRef,\n  {\n    /** Style loaders that have been added. */\n    loaders: Set<Type<unknown>>;\n\n    /** References to the instantiated loaders. */\n    refs: ComponentRef<unknown>[];\n  }\n>();\n\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\n@Injectable({providedIn: 'root'})\nexport class _CdkPrivateStyleLoader {\n  private _appRef: ApplicationRef | undefined;\n  private _injector = inject(Injector);\n  private _environmentInjector = inject(EnvironmentInjector);\n\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader: Type<unknown>): void {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = (this._appRef = this._appRef || this._injector.get(ApplicationRef));\n    let data = appsWithLoaders.get(appRef);\n\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {loaders: new Set(), refs: []};\n      appsWithLoaders.set(appRef, data);\n\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {environmentInjector: this._environmentInjector}));\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAmBA;AACA,MAAM,eAAe,GAAG,IAAI,OAAO,EAShC;AAEH;;;AAGG;MAEU,sBAAsB,CAAA;AACzB,IAAA,OAAO;AACP,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CAAC;AAE1D;;;AAGG;AACH,IAAA,IAAI,CAAC,MAAqB,EAAA;;QAExB,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAClF,IAAI,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC;;QAGtC,IAAI,CAAC,IAAI,EAAE;AACT,YAAA,IAAI,GAAG,EAAC,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAC;AACrC,YAAA,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;;AAGjC,YAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,gBAAA,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;AAC/D,gBAAA,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;AAChC,aAAC,CAAC;;;QAIJ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;AACxB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAC,CAAC,CAAC;;;uGA7BlF,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAtB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,cADV,MAAM,EAAA,CAAA;;2FAClB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBADlC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}