const EMPTY_ANIMATION_OPTIONS = {};
export {};
//# sourceMappingURL=data:application/json;base64,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