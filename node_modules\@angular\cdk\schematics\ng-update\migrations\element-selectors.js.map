{"version": 3, "file": "element-selectors.js", "sourceRoot": "", "sources": ["element-selectors.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,iCAAiC;AAGjC,2DAAsD;AAEtD,mDAA8D;AAC9D,kDAAmE;AAEnE;;;GAGG;AACH,MAAa,yBAA0B,SAAQ,qBAAsB;IAArE;;QACE,iEAAiE;QACjE,SAAI,GAAG,IAAA,oCAAqB,EAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAEvD,2DAA2D;QAC3D,YAAO,GAAY,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAiD5C,CAAC;IA/CU,SAAS,CAAC,IAAa;QAC9B,IAAI,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEQ,aAAa,CAAC,QAA0B;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAA,iCAAuB,EAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;iBACxD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;iBACtC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,eAAe,CAAC,UAA4B;QACnD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAA,iCAAuB,EAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;iBAC1D,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;iBACxC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,IAA0B;QACxD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YACrE,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;QAExE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAA,iCAAuB,EAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC;iBACnD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC;iBACvC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CACtB,QAAuB,EACvB,KAAa,EACb,IAAgC;QAEhC,IAAI,CAAC,UAAU;aACZ,IAAI,CAAC,QAAQ,CAAC;aACd,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;aAClC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CACF;AAtDD,8DAsDC"}