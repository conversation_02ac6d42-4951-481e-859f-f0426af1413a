{"version": 3, "file": "shadow-dom-B0oHn41l.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/shadow-dom.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nlet shadowDomIsSupported: boolean;\n\n/** Checks whether the user's browser support Shadow DOM. */\nexport function _supportsShadowDom(): boolean {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && ((head as any).createShadowRoot || head.attachShadow));\n  }\n\n  return shadowDomIsSupported;\n}\n\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nexport function _getShadowRoot(element: HTMLElement): ShadowRoot | null {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nexport function _getFocusedElementPierceShadowDom(): HTMLElement | null {\n  let activeElement =\n    typeof document !== 'undefined' && document\n      ? (document.activeElement as HTMLElement | null)\n      : null;\n\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement as HTMLElement | null;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n\n  return activeElement;\n}\n\n/** Gets the target of an event while accounting for Shadow DOM. */\nexport function _getEventTarget<T extends EventTarget>(event: Event): T | null {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return (event.composedPath ? event.composedPath()[0] : event.target) as T | null;\n}\n"], "names": [], "mappings": "AAQA,IAAI,oBAA6B;AAEjC;SACgB,kBAAkB,GAAA;AAChC,IAAA,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,QAAA,MAAM,IAAI,GAAG,OAAO,QAAQ,KAAK,WAAW,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI;AACnE,QAAA,oBAAoB,GAAG,CAAC,EAAE,IAAI,KAAM,IAAY,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;;AAG1F,IAAA,OAAO,oBAAoB;AAC7B;AAEA;AACM,SAAU,cAAc,CAAC,OAAoB,EAAA;IACjD,IAAI,kBAAkB,EAAE,EAAE;AACxB,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,IAAI;;;QAInE,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU,IAAI,QAAQ,YAAY,UAAU,EAAE;AACrF,YAAA,OAAO,QAAQ;;;AAInB,IAAA,OAAO,IAAI;AACb;AAEA;;;AAGG;SACa,iCAAiC,GAAA;AAC/C,IAAA,IAAI,aAAa,GACf,OAAO,QAAQ,KAAK,WAAW,IAAI;UAC9B,QAAQ,CAAC;UACV,IAAI;AAEV,IAAA,OAAO,aAAa,IAAI,aAAa,CAAC,UAAU,EAAE;AAChD,QAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,UAAU,CAAC,aAAmC;AACrF,QAAA,IAAI,gBAAgB,KAAK,aAAa,EAAE;YACtC;;aACK;YACL,aAAa,GAAG,gBAAgB;;;AAIpC,IAAA,OAAO,aAAa;AACtB;AAEA;AACM,SAAU,eAAe,CAAwB,KAAY,EAAA;;;IAGjE,QAAQ,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM;AACrE;;;;"}