
import { HttpClient } from '@angular/common/http';
import { Component, ViewChild } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { CellClickedEvent, ColDef } from 'ag-grid-community';
import { Observable } from 'rxjs/internal/Observable';
import { CardRendererComponent } from './card-renderer/card-renderer.component';
import { BreakpointObserver } from "@angular/cdk/layout";


@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.scss"],
})
export class AppComponent {
  public rowData$!: Observable<any[]>;

  isMobile = false;

  standardColumnDefs = [
    { headerName: "ID", field: "id", minWidth: 80 },
    { headerName: "Name", field: "name", minWidth: 150 },
    { headerName: "Email", field: "email", minWidth: 200 },
    { headerName: "Phone", field: "phone", minWidth: 150 },
    { headerName: "City", field: "city", minWidth: 120 },
  ];

  cardColumnDefs = [
    {
      headerName: "Card View",
      field: "card",
      cellRenderer: "cardRenderer",
      autoHeight: true,
      flex: 1,
    },
  ];

  columnDefs: any[] = this.standardColumnDefs;

  rowData = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    name: `User ${i + 1}`,
    email: `user${i + 1}@example.com`,
    phone: `+12345678${i + 1}`,
    city: ["Berlin", "Paris", "Madrid", "Rome", "Vienna"][i % 5],
  }));

  frameworkComponents = {
    cardRenderer: CardRendererComponent,
  };

  public defaultColDef: ColDef = {
    sortable: true,
    filter: true,
  };

  constructor(private http: HttpClient, private breakpointObserver: BreakpointObserver) {
    this.breakpointObserver
      .observe(["(max-width: 600px)"])
      .subscribe((result) => {
        this.isMobile = result.matches;
        this.columnDefs = this.isMobile
          ? this.cardColumnDefs
          : this.standardColumnDefs;
      });
  }

  ngOnInit() {
    this.rowData$ = this.http.get<any[]>(
      "https://www.ag-grid.com/example-assets/row-data.json"
    );
  }

  onCellClicked(event: CellClickedEvent) {
    console.log(event);
  }

  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  clearSelection() {
    this.agGrid.api.deselectAll();
  }
}
