/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { AUTO_STYLE, style } from '@angular/animations';
import { getOrSetAsInMap } from '../render/shared';
import { copyObj, extractStyleParams, iteratorToArray, NG_ANIMATING_SELECTOR, NG_TRIGGER_SELECTOR, normalizeAnimationEntry, resolveTiming, SUBSTITUTION_EXPR_START, validateStyleParams, visitDslNode } from '../util';
import { parseTransitionExpr } from './animation_transition_expr';
const SELF_TOKEN = ':self';
const SELF_TOKEN_REGEX = new RegExp(`\s*${SELF_TOKEN}\s*,?`, 'g');
/*
 * [Validation]
 * The visitor code below will traverse the animation AST generated by the animation verb functions
 * (the output is a tree of objects) and attempt to perform a series of validations on the data. The
 * following corner-cases will be validated:
 *
 * 1. Overlap of animations
 * Given that a CSS property cannot be animated in more than one place at the same time, it's
 * important that this behavior is detected and validated. The way in which this occurs is that
 * each time a style property is examined, a string-map containing the property will be updated with
 * the start and end times for when the property is used within an animation step.
 *
 * If there are two or more parallel animations that are currently running (these are invoked by the
 * group()) on the same element then the validator will throw an error. Since the start/end timing
 * values are collected for each property then if the current animation step is animating the same
 * property and its timing values fall anywhere into the window of time that the property is
 * currently being animated within then this is what causes an error.
 *
 * 2. Timing values
 * The validator will validate to see if a timing value of `duration delay easing` or
 * `durationNumber` is valid or not.
 *
 * (note that upon validation the code below will replace the timing data with an object containing
 * {duration,delay,easing}.
 *
 * 3. Offset Validation
 * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().
 * Offsets within keyframes() are considered valid when:
 *
 *   - No offsets are used at all
 *   - Each style() entry contains an offset value
 *   - Each offset is between 0 and 1
 *   - Each offset is greater to or equal than the previous one
 *
 * Otherwise an error will be thrown.
 */
export function buildAnimationAst(driver, metadata, errors) {
    return new AnimationAstBuilderVisitor(driver).build(metadata, errors);
}
const ROOT_SELECTOR = '';
export class AnimationAstBuilderVisitor {
    constructor(_driver) {
        this._driver = _driver;
    }
    build(metadata, errors) {
        const context = new AnimationAstBuilderContext(errors);
        this._resetContextStyleTimingState(context);
        return visitDslNode(this, normalizeAnimationEntry(metadata), context);
    }
    _resetContextStyleTimingState(context) {
        context.currentQuerySelector = ROOT_SELECTOR;
        context.collectedStyles = {};
        context.collectedStyles[ROOT_SELECTOR] = {};
        context.currentTime = 0;
    }
    visitTrigger(metadata, context) {
        let queryCount = context.queryCount = 0;
        let depCount = context.depCount = 0;
        const states = [];
        const transitions = [];
        if (metadata.name.charAt(0) == '@') {
            context.errors.push('animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\'@foo\', [...]))');
        }
        metadata.definitions.forEach(def => {
            this._resetContextStyleTimingState(context);
            if (def.type == 0 /* State */) {
                const stateDef = def;
                const name = stateDef.name;
                name.toString().split(/\s*,\s*/).forEach(n => {
                    stateDef.name = n;
                    states.push(this.visitState(stateDef, context));
                });
                stateDef.name = name;
            }
            else if (def.type == 1 /* Transition */) {
                const transition = this.visitTransition(def, context);
                queryCount += transition.queryCount;
                depCount += transition.depCount;
                transitions.push(transition);
            }
            else {
                context.errors.push('only state() and transition() definitions can sit inside of a trigger()');
            }
        });
        return {
            type: 7 /* Trigger */,
            name: metadata.name,
            states,
            transitions,
            queryCount,
            depCount,
            options: null
        };
    }
    visitState(metadata, context) {
        const styleAst = this.visitStyle(metadata.styles, context);
        const astParams = (metadata.options && metadata.options.params) || null;
        if (styleAst.containsDynamicStyles) {
            const missingSubs = new Set();
            const params = astParams || {};
            styleAst.styles.forEach(value => {
                if (isObject(value)) {
                    const stylesObj = value;
                    Object.keys(stylesObj).forEach(prop => {
                        extractStyleParams(stylesObj[prop]).forEach(sub => {
                            if (!params.hasOwnProperty(sub)) {
                                missingSubs.add(sub);
                            }
                        });
                    });
                }
            });
            if (missingSubs.size) {
                const missingSubsArr = iteratorToArray(missingSubs.values());
                context.errors.push(`state("${metadata
                    .name}", ...) must define default values for all the following style substitutions: ${missingSubsArr.join(', ')}`);
            }
        }
        return {
            type: 0 /* State */,
            name: metadata.name,
            style: styleAst,
            options: astParams ? { params: astParams } : null
        };
    }
    visitTransition(metadata, context) {
        context.queryCount = 0;
        context.depCount = 0;
        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);
        const matchers = parseTransitionExpr(metadata.expr, context.errors);
        return {
            type: 1 /* Transition */,
            matchers,
            animation,
            queryCount: context.queryCount,
            depCount: context.depCount,
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitSequence(metadata, context) {
        return {
            type: 2 /* Sequence */,
            steps: metadata.steps.map(s => visitDslNode(this, s, context)),
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitGroup(metadata, context) {
        const currentTime = context.currentTime;
        let furthestTime = 0;
        const steps = metadata.steps.map(step => {
            context.currentTime = currentTime;
            const innerAst = visitDslNode(this, step, context);
            furthestTime = Math.max(furthestTime, context.currentTime);
            return innerAst;
        });
        context.currentTime = furthestTime;
        return {
            type: 3 /* Group */,
            steps,
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitAnimate(metadata, context) {
        const timingAst = constructTimingAst(metadata.timings, context.errors);
        context.currentAnimateTimings = timingAst;
        let styleAst;
        let styleMetadata = metadata.styles ? metadata.styles : style({});
        if (styleMetadata.type == 5 /* Keyframes */) {
            styleAst = this.visitKeyframes(styleMetadata, context);
        }
        else {
            let styleMetadata = metadata.styles;
            let isEmpty = false;
            if (!styleMetadata) {
                isEmpty = true;
                const newStyleData = {};
                if (timingAst.easing) {
                    newStyleData['easing'] = timingAst.easing;
                }
                styleMetadata = style(newStyleData);
            }
            context.currentTime += timingAst.duration + timingAst.delay;
            const _styleAst = this.visitStyle(styleMetadata, context);
            _styleAst.isEmptyStep = isEmpty;
            styleAst = _styleAst;
        }
        context.currentAnimateTimings = null;
        return {
            type: 4 /* Animate */,
            timings: timingAst,
            style: styleAst,
            options: null
        };
    }
    visitStyle(metadata, context) {
        const ast = this._makeStyleAst(metadata, context);
        this._validateStyleAst(ast, context);
        return ast;
    }
    _makeStyleAst(metadata, context) {
        const styles = [];
        if (Array.isArray(metadata.styles)) {
            metadata.styles.forEach(styleTuple => {
                if (typeof styleTuple == 'string') {
                    if (styleTuple == AUTO_STYLE) {
                        styles.push(styleTuple);
                    }
                    else {
                        context.errors.push(`The provided style string value ${styleTuple} is not allowed.`);
                    }
                }
                else {
                    styles.push(styleTuple);
                }
            });
        }
        else {
            styles.push(metadata.styles);
        }
        let containsDynamicStyles = false;
        let collectedEasing = null;
        styles.forEach(styleData => {
            if (isObject(styleData)) {
                const styleMap = styleData;
                const easing = styleMap['easing'];
                if (easing) {
                    collectedEasing = easing;
                    delete styleMap['easing'];
                }
                if (!containsDynamicStyles) {
                    for (let prop in styleMap) {
                        const value = styleMap[prop];
                        if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {
                            containsDynamicStyles = true;
                            break;
                        }
                    }
                }
            }
        });
        return {
            type: 6 /* Style */,
            styles,
            easing: collectedEasing,
            offset: metadata.offset,
            containsDynamicStyles,
            options: null
        };
    }
    _validateStyleAst(ast, context) {
        const timings = context.currentAnimateTimings;
        let endTime = context.currentTime;
        let startTime = context.currentTime;
        if (timings && startTime > 0) {
            startTime -= timings.duration + timings.delay;
        }
        ast.styles.forEach(tuple => {
            if (typeof tuple == 'string')
                return;
            Object.keys(tuple).forEach(prop => {
                if (!this._driver.validateStyleProperty(prop)) {
                    context.errors.push(`The provided animation property "${prop}" is not a supported CSS property for animations`);
                    return;
                }
                const collectedStyles = context.collectedStyles[context.currentQuerySelector];
                const collectedEntry = collectedStyles[prop];
                let updateCollectedStyle = true;
                if (collectedEntry) {
                    if (startTime != endTime && startTime >= collectedEntry.startTime &&
                        endTime <= collectedEntry.endTime) {
                        context.errors.push(`The CSS property "${prop}" that exists between the times of "${collectedEntry.startTime}ms" and "${collectedEntry
                            .endTime}ms" is also being animated in a parallel animation between the times of "${startTime}ms" and "${endTime}ms"`);
                        updateCollectedStyle = false;
                    }
                    // we always choose the smaller start time value since we
                    // want to have a record of the entire animation window where
                    // the style property is being animated in between
                    startTime = collectedEntry.startTime;
                }
                if (updateCollectedStyle) {
                    collectedStyles[prop] = { startTime, endTime };
                }
                if (context.options) {
                    validateStyleParams(tuple[prop], context.options, context.errors);
                }
            });
        });
    }
    visitKeyframes(metadata, context) {
        const ast = { type: 5 /* Keyframes */, styles: [], options: null };
        if (!context.currentAnimateTimings) {
            context.errors.push(`keyframes() must be placed inside of a call to animate()`);
            return ast;
        }
        const MAX_KEYFRAME_OFFSET = 1;
        let totalKeyframesWithOffsets = 0;
        const offsets = [];
        let offsetsOutOfOrder = false;
        let keyframesOutOfRange = false;
        let previousOffset = 0;
        const keyframes = metadata.steps.map(styles => {
            const style = this._makeStyleAst(styles, context);
            let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);
            let offset = 0;
            if (offsetVal != null) {
                totalKeyframesWithOffsets++;
                offset = style.offset = offsetVal;
            }
            keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;
            offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;
            previousOffset = offset;
            offsets.push(offset);
            return style;
        });
        if (keyframesOutOfRange) {
            context.errors.push(`Please ensure that all keyframe offsets are between 0 and 1`);
        }
        if (offsetsOutOfOrder) {
            context.errors.push(`Please ensure that all keyframe offsets are in order`);
        }
        const length = metadata.steps.length;
        let generatedOffset = 0;
        if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {
            context.errors.push(`Not all style() steps within the declared keyframes() contain offsets`);
        }
        else if (totalKeyframesWithOffsets == 0) {
            generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);
        }
        const limit = length - 1;
        const currentTime = context.currentTime;
        const currentAnimateTimings = context.currentAnimateTimings;
        const animateDuration = currentAnimateTimings.duration;
        keyframes.forEach((kf, i) => {
            const offset = generatedOffset > 0 ? (i == limit ? 1 : (generatedOffset * i)) : offsets[i];
            const durationUpToThisFrame = offset * animateDuration;
            context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;
            currentAnimateTimings.duration = durationUpToThisFrame;
            this._validateStyleAst(kf, context);
            kf.offset = offset;
            ast.styles.push(kf);
        });
        return ast;
    }
    visitReference(metadata, context) {
        return {
            type: 8 /* Reference */,
            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitAnimateChild(metadata, context) {
        context.depCount++;
        return {
            type: 9 /* AnimateChild */,
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitAnimateRef(metadata, context) {
        return {
            type: 10 /* AnimateRef */,
            animation: this.visitReference(metadata.animation, context),
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitQuery(metadata, context) {
        const parentSelector = context.currentQuerySelector;
        const options = (metadata.options || {});
        context.queryCount++;
        context.currentQuery = metadata;
        const [selector, includeSelf] = normalizeSelector(metadata.selector);
        context.currentQuerySelector =
            parentSelector.length ? (parentSelector + ' ' + selector) : selector;
        getOrSetAsInMap(context.collectedStyles, context.currentQuerySelector, {});
        const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);
        context.currentQuery = null;
        context.currentQuerySelector = parentSelector;
        return {
            type: 11 /* Query */,
            selector,
            limit: options.limit || 0,
            optional: !!options.optional,
            includeSelf,
            animation,
            originalSelector: metadata.selector,
            options: normalizeAnimationOptions(metadata.options)
        };
    }
    visitStagger(metadata, context) {
        if (!context.currentQuery) {
            context.errors.push(`stagger() can only be used inside of query()`);
        }
        const timings = metadata.timings === 'full' ?
            { duration: 0, delay: 0, easing: 'full' } :
            resolveTiming(metadata.timings, context.errors, true);
        return {
            type: 12 /* Stagger */,
            animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),
            timings,
            options: null
        };
    }
}
function normalizeSelector(selector) {
    const hasAmpersand = selector.split(/\s*,\s*/).find(token => token == SELF_TOKEN) ? true : false;
    if (hasAmpersand) {
        selector = selector.replace(SELF_TOKEN_REGEX, '');
    }
    // Note: the :enter and :leave aren't normalized here since those
    // selectors are filled in at runtime during timeline building
    selector = selector.replace(/@\*/g, NG_TRIGGER_SELECTOR)
        .replace(/@\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.substr(1))
        .replace(/:animating/g, NG_ANIMATING_SELECTOR);
    return [selector, hasAmpersand];
}
function normalizeParams(obj) {
    return obj ? copyObj(obj) : null;
}
export class AnimationAstBuilderContext {
    constructor(errors) {
        this.errors = errors;
        this.queryCount = 0;
        this.depCount = 0;
        this.currentTransition = null;
        this.currentQuery = null;
        this.currentQuerySelector = null;
        this.currentAnimateTimings = null;
        this.currentTime = 0;
        this.collectedStyles = {};
        this.options = null;
    }
}
function consumeOffset(styles) {
    if (typeof styles == 'string')
        return null;
    let offset = null;
    if (Array.isArray(styles)) {
        styles.forEach(styleTuple => {
            if (isObject(styleTuple) && styleTuple.hasOwnProperty('offset')) {
                const obj = styleTuple;
                offset = parseFloat(obj['offset']);
                delete obj['offset'];
            }
        });
    }
    else if (isObject(styles) && styles.hasOwnProperty('offset')) {
        const obj = styles;
        offset = parseFloat(obj['offset']);
        delete obj['offset'];
    }
    return offset;
}
function isObject(value) {
    return !Array.isArray(value) && typeof value == 'object';
}
function constructTimingAst(value, errors) {
    let timings = null;
    if (value.hasOwnProperty('duration')) {
        timings = value;
    }
    else if (typeof value == 'number') {
        const duration = resolveTiming(value, errors).duration;
        return makeTimingAst(duration, 0, '');
    }
    const strValue = value;
    const isDynamic = strValue.split(/\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');
    if (isDynamic) {
        const ast = makeTimingAst(0, 0, '');
        ast.dynamic = true;
        ast.strValue = strValue;
        return ast;
    }
    timings = timings || resolveTiming(strValue, errors);
    return makeTimingAst(timings.duration, timings.delay, timings.easing);
}
function normalizeAnimationOptions(options) {
    if (options) {
        options = copyObj(options);
        if (options['params']) {
            options['params'] = normalizeParams(options['params']);
        }
    }
    else {
        options = {};
    }
    return options;
}
function makeTimingAst(duration, delay, easing) {
    return { duration, delay, easing };
}
//# sourceMappingURL=data:application/json;base64,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