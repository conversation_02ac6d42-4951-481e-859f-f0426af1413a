{"ngccVersion": "13.1.3", "configFileHash": "6883213aec2e6448f60915f864b10646a78045af10f17ba40f81f18dd7c1b8e7", "lockFileHash": "291535ad970f606105172e1943b944dd1174db78460c9c9b97dad84a20d45823", "entryPointPaths": [["@ampproject/remapping", "@ampproject/remapping"], ["@angular/animations", "@angular/animations"], ["@angular/animations", "@angular/animations/browser"], ["@angular/animations", "@angular/animations/browser/testing"], ["@angular/cdk", "@angular/cdk"], ["@angular/cli", "@angular/cli"], ["@angular/common", "@angular/common"], ["@angular/common", "@angular/common/http"], ["@angular/common", "@angular/common/http/testing"], ["@angular/common", "@angular/common/testing"], ["@angular/common", "@angular/common/upgrade"], ["@angular/compiler", "@angular/compiler"], ["@angular/compiler", "@angular/compiler/testing"], ["@angular/compiler-cli", "@angular/compiler-cli"], ["@angular/core", "@angular/core"], ["@angular/core", "@angular/core/testing"], ["@angular/forms", "@angular/forms"], ["@angular/platform-browser", "@angular/platform-browser"], ["@angular/platform-browser", "@angular/platform-browser/animations"], ["@angular/platform-browser", "@angular/platform-browser/testing"], ["@angular/platform-browser-dynamic", "@angular/platform-browser-dynamic"], ["@angular/platform-browser-dynamic", "@angular/platform-browser-dynamic/testing"], ["@angular/router", "@angular/router"], ["@angular/router", "@angular/router/testing"], ["@angular/router", "@angular/router/upgrade"], ["@angular-devkit/architect", "@angular-devkit/architect"], ["@angular-devkit/build-angular", "@angular-devkit/build-angular"], ["@angular-devkit/build-webpack", "@angular-devkit/build-webpack"], ["@angular-devkit/core", "@angular-devkit/core"], ["@angular-devkit/core", "@angular-devkit/core/node"], ["@angular-devkit/schematics", "@angular-devkit/schematics"], ["@angular-devkit/schematics", "@angular-devkit/schematics/tasks"], ["@angular-devkit/schematics", "@angular-devkit/schematics/testing"], ["@angular-devkit/schematics", "@angular-devkit/schematics/tools"], ["@assemblyscript/loader", "@assemblyscript/loader"], ["@babel/parser", "@babel/parser"], ["@babel/types", "@babel/types"], ["@csstools/postcss-progressive-custom-properties", "@csstools/postcss-progressive-custom-properties"], ["@discoveryjs/json-ext", "@discoveryjs/json-ext"], ["@jridgewell/gen-mapping", "@jridgewell/gen-mapping"], ["@jridgewell/resolve-uri", "@jridgewell/resolve-uri"], ["@jridgewell/set-array", "@jridgewell/set-array"], ["@jridgewell/sourcemap-codec", "@jridgewell/sourcemap-codec"], ["@ngtools/webpack", "@ngtools/webpack"], ["@nodelib/fs.scandir", "@nodelib/fs.scandir"], ["@nodelib/fs.stat", "@nodelib/fs.stat"], ["@nodelib/fs.walk", "@nodelib/fs.walk"], ["@tootallnate/once", "@tootallnate/once"], ["@types/component-emitter", "@types/component-emitter"], ["@types/cookie", "@types/cookie"], ["@types/cors", "@types/cors"], ["@types/eslint", "@types/eslint"], ["@types/eslint-scope", "@types/eslint-scope"], ["@types/estree", "@types/estree"], ["@types/http-proxy", "@types/http-proxy"], ["@types/jasmine", "@types/jasmine"], ["@types/json-schema", "@types/json-schema"], ["@types/node", "@types/node"], ["@types/parse-json", "@types/parse-json"], ["@types/retry", "@types/retry"], ["@xtuc/long", "@xtuc/long"], ["abab", "abab"], ["acorn", "acorn"], ["ag-grid-angular", "ag-grid-angular", ["C:/playground/Angular/angular-data-grid-main/getting-started-video-tutorial/src/001-quickstart-guide/final/node_modules/ag-grid-angular/node_modules/tslib", "C:/playground/Angular/angular-data-grid-main/getting-started-video-tutorial/src/001-quickstart-guide/final/node_modules/@angular/core", "C:/playground/Angular/angular-data-grid-main/getting-started-video-tutorial/src/001-quickstart-guide/final/node_modules/ag-grid-community"]], ["ag-grid-angular/node_modules/tslib", "ag-grid-angular/node_modules/tslib"], ["ag-grid-community", "ag-grid-community"], ["ag-grid-enterprise", "ag-grid-enterprise"], ["agent-base", "agent-base"], ["agentkeepalive", "agentkeepalive"], ["ajv", "ajv"], ["ajv-formats", "ajv-formats"], ["ajv-keywords", "ajv-keywords"], ["ansi-colors", "ansi-colors"], ["array-flatten", "array-flatten"], ["autoprefixer", "autoprefixer"], ["base64-js", "base64-js"], ["big.js", "big.js"], ["browserslist", "browserslist"], ["buffer", "buffer"], ["builtins", "builtins"], ["canonical-path", "canonical-path"], ["chalk", "chalk"], ["chokidar", "chokidar"], ["chrome-trace-event", "chrome-trace-event"], ["cliui", "cliui"], ["colorette", "colorette"], ["commander", "commander"], ["copy-anything", "copy-anything"], ["cosmiconfig", "cosmiconfig"], ["critters", "critters"], ["css-blank-pseudo", "css-blank-pseudo"], ["css-has-pseudo", "css-has-pseudo"], ["css-prefers-color-scheme", "css-prefers-color-scheme"], ["css-select", "css-select"], ["css-what", "css-what"], ["cssdb", "cssdb"], ["dependency-graph", "dependency-graph"], ["dom-serializer", "dom-serializer"], ["domelementtype", "domelementtype"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["domutils", "domutils"], ["emoji-regex", "emoji-regex"], ["engine.io", "engine.io"], ["engine.io-parser", "engine.io-parser"], ["enhanced-resolve", "enhanced-resolve"], ["entities", "entities"], ["es-module-lexer", "es-module-lexer"], ["esbuild", "esbuild"], ["esbuild-wasm", "esbuild-wasm"], ["escalade", "escalade"], ["eventemitter-asyncresource", "eventemitter-asyncresource"], ["eventemitter3", "eventemitter3"], ["external-editor", "external-editor"], ["fast-deep-equal", "fast-deep-equal"], ["fast-glob", "fast-glob"], ["fast-json-stable-stringify", "fast-json-stable-stringify"], ["faye-websocket", "faye-websocket"], ["flatted", "flatted"], ["fraction.js", "fraction.js"], ["gauge", "gauge"], ["get-caller-file", "get-caller-file"], ["get-package-type", "get-package-type"], ["has", "has"], ["hdr-histogram-js", "hdr-histogram-js"], ["html-entities", "html-entities"], ["http-parser-js", "http-parser-js"], ["http-proxy-agent", "http-proxy-agent"], ["http-proxy-middleware", "http-proxy-middleware"], ["https-proxy-agent", "https-proxy-agent"], ["human-signals", "human-signals"], ["iconv-lite", "iconv-lite"], ["ieee754", "ieee754"], ["immutable", "immutable"], ["ipaddr.js", "ipaddr.js"], ["is-plain-object", "is-plain-object"], ["is-what", "is-what"], ["isbinaryfile", "isbinaryfile"], ["isobject", "isobject"], ["jest-worker", "jest-worker"], ["json-schema-traverse", "json-schema-traverse"], ["json5", "json5"], ["jsonc-parser", "jsonc-parser"], ["klona", "klona"], ["license-webpack-plugin", "license-webpack-plugin"], ["lines-and-columns", "lines-and-columns"], ["log4js", "log4js"], ["magic-string", "magic-string"], ["memfs", "memfs"], ["nanoid", "nanoid"], ["nanoid", "nanoid/async"], ["nanoid", "nanoid/non-secure"], ["nanoid", "nanoid/url-alphabet"], ["nth-check", "nth-check"], ["picocolors", "picocolors"], ["piscina", "piscina"], ["piscina", "piscina/dist"], ["portfinder", "portfinder"], ["postcss", "postcss"], ["postcss-color-functional-notation", "postcss-color-functional-notation"], ["postcss-color-hex-alpha", "postcss-color-hex-alpha"], ["postcss-color-rebeccapurple", "postcss-color-rebeccapurple"], ["postcss-custom-media", "postcss-custom-media"], ["postcss-custom-properties", "postcss-custom-properties"], ["postcss-custom-selectors", "postcss-custom-selectors"], ["postcss-dir-pseudo-class", "postcss-dir-pseudo-class"], ["postcss-double-position-gradients", "postcss-double-position-gradients"], ["postcss-env-function", "postcss-env-function"], ["postcss-focus-visible", "postcss-focus-visible"], ["postcss-focus-within", "postcss-focus-within"], ["postcss-gap-properties", "postcss-gap-properties"], ["postcss-image-set-function", "postcss-image-set-function"], ["postcss-lab-function", "postcss-lab-function"], ["postcss-logical", "postcss-logical"], ["postcss-nesting", "postcss-nesting"], ["postcss-overflow-shorthand", "postcss-overflow-shorthand"], ["postcss-place", "postcss-place"], ["postcss-preset-env", "postcss-preset-env"], ["postcss-pseudo-class-any-link", "postcss-pseudo-class-any-link"], ["postcss-selector-parser", "postcss-selector-parser"], ["postcss-value-parser", "postcss-value-parser"], ["queue-microtask", "queue-microtask"], ["readdirp", "readdirp"], ["reflect-metadata", "reflect-metadata"], ["regex-parser", "regex-parser"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], ["rfdc", "rfdc"], ["rxjs", "rxjs"], ["rxjs", "rxjs/ajax"], ["rxjs", "rxjs/fetch"], ["rxjs", "rxjs/operators"], ["rxjs", "rxjs/testing"], ["rxjs", "rxjs/webSocket"], ["safe-buffer", "safe-buffer"], ["schema-utils", "schema-utils"], ["setprot<PERSON>of", "setprot<PERSON>of"], ["smart-buffer", "smart-buffer"], ["socket.io", "socket.io"], ["socket.io-adapter", "socket.io-adapter"], ["socket.io-parser", "socket.io-parser"], ["socks", "socks"], ["socks-proxy-agent", "socks-proxy-agent"], ["source-map", "source-map"], ["source-map-js", "source-map-js"], ["sourcemap-codec", "sourcemap-codec"], ["spdy", "spdy"], ["spdy-transport", "spdy-transport"], ["symbol-observable", "symbol-observable"], ["tapable", "tapable"], ["terser", "terser"], ["terser", "terser/bin"], ["terser-webpack-plugin", "terser-webpack-plugin"], ["tree-kill", "tree-kill"], ["tslib", "tslib"], ["type-fest", "type-fest"], ["typed-assert", "typed-assert"], ["typescript", "typescript"], ["uri-js", "uri-js"], ["webpack", "webpack"], ["webpack-merge", "webpack-merge"], ["webpack-subresource-integrity", "webpack-subresource-integrity"], ["websocket-driver", "websocket-driver"], ["y18n", "y18n"], ["yaml", "yaml"], ["yargs", "yargs"], ["yargs-parser", "yargs-parser"], ["zone.js", "zone.js"]]}