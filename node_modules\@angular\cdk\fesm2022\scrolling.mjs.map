{"version": 3, "file": "scrolling.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scroll-strategy.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/fixed-size-virtual-scroll.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/scroll-dispatcher.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/scrollable.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/viewport-ruler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scrollable.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scroll-viewport.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scroll-viewport.html", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-for-of.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scrollable-element.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/virtual-scrollable-window.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/scrolling/scrolling-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\nimport {Observable} from 'rxjs';\nimport type {CdkVirtualScrollViewport} from './virtual-scroll-viewport';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nexport const VIRTUAL_SCROLL_STRATEGY = new InjectionToken<VirtualScrollStrategy>(\n  'VIRTUAL_SCROLL_STRATEGY',\n);\n\n/** A strategy that dictates which items should be rendered in the viewport. */\nexport interface VirtualScrollStrategy {\n  /** Emits when the index of the first element visible in the viewport changes. */\n  scrolledIndexChange: Observable<number>;\n\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport: CdkVirtualScrollViewport): void;\n\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach(): void;\n\n  /** Called when the viewport is scrolled (debounced using requestAnimationFrame). */\n  onContentScrolled(): void;\n\n  /** Called when the length of the data changes. */\n  onDataLengthChanged(): void;\n\n  /** Called when the range of items rendered in the DOM has changed. */\n  onContentRendered(): void;\n\n  /** Called when the offset of the rendered items changed. */\n  onRenderedOffsetChanged(): void;\n\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index: number, behavior: ScrollBehavior): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceNumberProperty, NumberInput} from '../coercion';\nimport {Directive, forwardRef, Input, OnChanges} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {distinctUntilChanged} from 'rxjs/operators';\nimport {VIRTUAL_SCROLL_STRATEGY, VirtualScrollStrategy} from './virtual-scroll-strategy';\nimport {CdkVirtualScrollViewport} from './virtual-scroll-viewport';\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nexport class FixedSizeVirtualScrollStrategy implements VirtualScrollStrategy {\n  private readonly _scrolledIndexChange = new Subject<number>();\n\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  scrolledIndexChange: Observable<number> = this._scrolledIndexChange.pipe(distinctUntilChanged());\n\n  /** The attached viewport. */\n  private _viewport: CdkVirtualScrollViewport | null = null;\n\n  /** The size of the items in the virtually scrolling list. */\n  private _itemSize: number;\n\n  /** The minimum amount of buffer rendered beyond the viewport (in pixels). */\n  private _minBufferPx: number;\n\n  /** The number of buffer items to render beyond the edge of the viewport (in pixels). */\n  private _maxBufferPx: number;\n\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize: number, minBufferPx: number, maxBufferPx: number) {\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport: CdkVirtualScrollViewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize: number, minBufferPx: number, maxBufferPx: number) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index: number, behavior: ScrollBehavior): void {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n\n  /** Update the viewport's total content size. */\n  private _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n\n  /** Update the viewport's rendered range. */\n  private _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {start: renderedRange.start, end: renderedRange.end};\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(\n        0,\n        Math.min(firstVisibleIndex, dataLength - maxVisibleItems),\n      );\n\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(\n        dataLength,\n        Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize),\n      );\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(\n            0,\n            Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize),\n          );\n        }\n      }\n    }\n\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nexport function _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir: CdkFixedSizeVirtualScroll) {\n  return fixedSizeDir._scrollStrategy;\n}\n\n/** A virtual scroll strategy that supports fixed-size items. */\n@Directive({\n  selector: 'cdk-virtual-scroll-viewport[itemSize]',\n  providers: [\n    {\n      provide: VIRTUAL_SCROLL_STRATEGY,\n      useFactory: _fixedSizeVirtualScrollStrategyFactory,\n      deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n    },\n  ],\n})\nexport class CdkFixedSizeVirtualScroll implements OnChanges {\n  /** The size of the items in the list (in pixels). */\n  @Input()\n  get itemSize(): number {\n    return this._itemSize;\n  }\n  set itemSize(value: NumberInput) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  _itemSize = 20;\n\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n  @Input()\n  get minBufferPx(): number {\n    return this._minBufferPx;\n  }\n  set minBufferPx(value: NumberInput) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  _minBufferPx = 100;\n\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n  @Input()\n  get maxBufferPx(): number {\n    return this._maxBufferPx;\n  }\n  set maxBufferPx(value: NumberInput) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n  _maxBufferPx = 200;\n\n  /** The scroll strategy used by this directive. */\n  _scrollStrategy = new FixedSizeVirtualScrollStrategy(\n    this.itemSize,\n    this.minBufferPx,\n    this.maxBufferPx,\n  );\n\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceElement} from '../coercion';\nimport {Platform} from '../platform';\nimport {ElementRef, Injectable, NgZone, OnDestroy, RendererFactory2, inject} from '@angular/core';\nimport {of as observableOf, Subject, Subscription, Observable, Observer} from 'rxjs';\nimport {auditTime, filter} from 'rxjs/operators';\nimport type {CdkScrollable} from './scrollable';\n\n/** Time in ms to throttle the scrolling events by default. */\nexport const DEFAULT_SCROLL_TIME = 20;\n\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n@Injectable({providedIn: 'root'})\nexport class ScrollDispatcher implements OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private _renderer = inject(RendererFactory2).createRenderer(null, null);\n  private _cleanupGlobalListener: (() => void) | undefined;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n  private readonly _scrolled = new Subject<CdkScrollable | void>();\n\n  /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n  private _scrolledCount = 0;\n\n  /**\n   * Map of all the scrollable references that are registered with the service and their\n   * scroll event subscriptions.\n   */\n  scrollContainers: Map<CdkScrollable, Subscription> = new Map();\n\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable: CdkScrollable): void {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(\n        scrollable,\n        scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)),\n      );\n    }\n  }\n\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable: CdkScrollable): void {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs: number = DEFAULT_SCROLL_TIME): Observable<CdkScrollable | void> {\n    if (!this._platform.isBrowser) {\n      return observableOf<void>();\n    }\n\n    return new Observable((observer: Observer<CdkScrollable | void>) => {\n      if (!this._cleanupGlobalListener) {\n        this._cleanupGlobalListener = this._ngZone.runOutsideAngular(() =>\n          this._renderer.listen('document', 'scroll', () => this._scrolled.next()),\n        );\n      }\n\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription =\n        auditTimeInMs > 0\n          ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n          : this._scrolled.subscribe(observer);\n\n      this._scrolledCount++;\n\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n\n        if (!this._scrolledCount) {\n          this._cleanupGlobalListener?.();\n          this._cleanupGlobalListener = undefined;\n        }\n      };\n    });\n  }\n\n  ngOnDestroy() {\n    this._cleanupGlobalListener?.();\n    this._cleanupGlobalListener = undefined;\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n    this._scrolled.complete();\n  }\n\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(\n    elementOrElementRef: ElementRef | HTMLElement,\n    auditTimeInMs?: number,\n  ): Observable<CdkScrollable | void> {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n\n    return this.scrolled(auditTimeInMs).pipe(\n      filter(target => !target || ancestors.indexOf(target) > -1),\n    );\n  }\n\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementOrElementRef: ElementRef | HTMLElement): CdkScrollable[] {\n    const scrollingContainers: CdkScrollable[] = [];\n\n    this.scrollContainers.forEach((_subscription: Subscription, scrollable: CdkScrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n\n    return scrollingContainers;\n  }\n\n  /** Returns true if the element is contained within the provided Scrollable. */\n  private _scrollableContainsElement(\n    scrollable: CdkScrollable,\n    elementOrElementRef: ElementRef | HTMLElement,\n  ): boolean {\n    let element: HTMLElement | null = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while ((element = element!.parentElement));\n\n    return false;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '../bidi';\nimport {getRtlScrollAxisType, RtlScrollAxisType, supportsScrollBehavior} from '../platform';\nimport {Directive, ElementRef, NgZone, OnDestroy, OnInit, Renderer2, inject} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {ScrollDispatcher} from './scroll-dispatcher';\n\nexport type _Without<T> = {[P in keyof T]?: never};\nexport type _XOR<T, U> = (_Without<T> & U) | (_Without<U> & T);\nexport type _Top = {top?: number};\nexport type _Bottom = {bottom?: number};\nexport type _Left = {left?: number};\nexport type _Right = {right?: number};\nexport type _Start = {start?: number};\nexport type _End = {end?: number};\nexport type _XAxis = _XOR<_XOR<_Left, _Right>, _XOR<_Start, _End>>;\nexport type _YAxis = _XOR<_Top, _Bottom>;\n\n/**\n * An extended version of ScrollToOptions that allows expressing scroll offsets relative to the\n * top, bottom, left, right, start, or end of the viewport rather than just the top and left.\n * Please note: the top and bottom properties are mutually exclusive, as are the left, right,\n * start, and end properties.\n */\nexport type ExtendedScrollToOptions = _XAxis & _YAxis & ScrollOptions;\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n@Directive({\n  selector: '[cdk-scrollable], [cdkScrollable]',\n})\nexport class CdkScrollable implements OnInit, OnDestroy {\n  protected elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected scrollDispatcher = inject(ScrollDispatcher);\n  protected ngZone = inject(NgZone);\n  protected dir? = inject(Directionality, {optional: true});\n  protected _scrollElement: EventTarget = this.elementRef.nativeElement;\n  protected readonly _destroyed = new Subject<void>();\n  private _renderer = inject(Renderer2);\n  private _cleanupScroll: (() => void) | undefined;\n  private _elementScrolled = new Subject<Event>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnInit() {\n    this._cleanupScroll = this.ngZone.runOutsideAngular(() =>\n      this._renderer.listen(this._scrollElement, 'scroll', event =>\n        this._elementScrolled.next(event),\n      ),\n    );\n    this.scrollDispatcher.register(this);\n  }\n\n  ngOnDestroy() {\n    this._cleanupScroll?.();\n    this._elementScrolled.complete();\n    this.scrollDispatcher.deregister(this);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n  elementScrolled(): Observable<Event> {\n    return this._elementScrolled;\n  }\n\n  /** Gets the ElementRef for the viewport. */\n  getElementRef(): ElementRef<HTMLElement> {\n    return this.elementRef;\n  }\n\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n  scrollTo(options: ExtendedScrollToOptions): void {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl';\n\n    // Rewrite start & end offsets as right or left offsets.\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    }\n\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      (options as _Without<_Bottom> & _Top).top =\n        el.scrollHeight - el.clientHeight - options.bottom;\n    }\n\n    // Rewrite the right offset as a left offset.\n    if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n      if (options.left != null) {\n        (options as _Without<_Left> & _Right).right =\n          el.scrollWidth - el.clientWidth - options.left;\n      }\n\n      if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        (options as _Without<_Right> & _Left).left =\n          el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n\n    this._applyScrollToOptions(options);\n  }\n\n  private _applyScrollToOptions(options: ScrollToOptions): void {\n    const el = this.elementRef.nativeElement;\n\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n  measureScrollOffset(from: 'top' | 'left' | 'right' | 'bottom' | 'start' | 'end'): number {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    }\n\n    // Rewrite start & end as left or right offsets.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n\n    if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Platform} from '../platform';\nimport {Injectable, NgZone, OnDestroy, RendererFactory2, inject, DOCUMENT} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {auditTime} from 'rxjs/operators';\n\n/** Time in ms to throttle the resize events by default. */\nexport const DEFAULT_RESIZE_TIME = 20;\n\n/** Object that holds the scroll position of the viewport in each direction. */\nexport interface ViewportScrollPosition {\n  top: number;\n  left: number;\n}\n\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n@Injectable({providedIn: 'root'})\nexport class ViewportRuler implements OnDestroy {\n  private _platform = inject(Platform);\n  private _listeners: (() => void)[] | undefined;\n\n  /** Cached viewport dimensions. */\n  private _viewportSize: {width: number; height: number} | null;\n\n  /** Stream of viewport change events. */\n  private readonly _change = new Subject<Event>();\n\n  /** Used to reference correct document/window */\n  protected _document = inject(DOCUMENT, {optional: true})!;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n\n    ngZone.runOutsideAngular(() => {\n      if (this._platform.isBrowser) {\n        const changeListener = (event: Event) => this._change.next(event);\n        this._listeners = [\n          renderer.listen('window', 'resize', changeListener),\n          renderer.listen('window', 'orientationchange', changeListener),\n        ];\n      }\n\n      // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n      this.change().subscribe(() => (this._viewportSize = null));\n    });\n  }\n\n  ngOnDestroy() {\n    this._listeners?.forEach(cleanup => cleanup());\n    this._change.complete();\n  }\n\n  /** Returns the viewport's width and height. */\n  getViewportSize(): Readonly<{width: number; height: number}> {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n\n    const output = {width: this._viewportSize!.width, height: this._viewportSize!.height};\n\n    // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null!;\n    }\n\n    return output;\n  }\n\n  /** Gets a DOMRect for the viewport's bounds. */\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {width, height} = this.getViewportSize();\n\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width,\n    };\n  }\n\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition(): ViewportScrollPosition {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {top: 0, left: 0};\n    }\n\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const document = this._document;\n    const window = this._getWindow();\n    const documentElement = document.documentElement!;\n    const documentRect = documentElement.getBoundingClientRect();\n\n    const top =\n      -documentRect.top ||\n      document.body.scrollTop ||\n      window.scrollY ||\n      documentElement.scrollTop ||\n      0;\n\n    const left =\n      -documentRect.left ||\n      document.body.scrollLeft ||\n      window.scrollX ||\n      documentElement.scrollLeft ||\n      0;\n\n    return {top, left};\n  }\n\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime: number = DEFAULT_RESIZE_TIME): Observable<Event> {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  private _getWindow(): Window {\n    return this._document.defaultView || window;\n  }\n\n  /** Updates the cached viewport size. */\n  private _updateViewportSize() {\n    const window = this._getWindow();\n    this._viewportSize = this._platform.isBrowser\n      ? {width: window.innerWidth, height: window.innerHeight}\n      : {width: 0, height: 0};\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, InjectionToken} from '@angular/core';\nimport {CdkScrollable} from './scrollable';\n\nexport const VIRTUAL_SCROLLABLE = new InjectionToken<CdkVirtualScrollable>('VIRTUAL_SCROLLABLE');\n\n/**\n * Extending the `CdkScrollable` to be used as scrolling container for virtual scrolling.\n */\n@Directive()\nexport abstract class CdkVirtualScrollable extends CdkScrollable {\n  constructor(...args: unknown[]);\n  constructor() {\n    super();\n  }\n\n  /**\n   * Measure the viewport size for the provided orientation.\n   *\n   * @param orientation The orientation to measure the size from.\n   */\n  measureViewportSize(orientation: 'horizontal' | 'vertical') {\n    const viewportEl = this.elementRef.nativeElement;\n    return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n\n  /**\n   * Measure the bounding DOMRect size including the scroll offset.\n   *\n   * @param from The edge to measure from.\n   */\n  abstract measureBoundingClientRectWithScrollOffset(\n    from: 'left' | 'top' | 'right' | 'bottom',\n  ): number;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ListRange} from '../collections';\nimport {Platform} from '../platform';\nimport {\n  afterNextRender,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  inject,\n  Inject,\n  Injector,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  signal,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  animationFrameScheduler,\n  asapScheduler,\n  Observable,\n  Observer,\n  Subject,\n  Subscription,\n} from 'rxjs';\nimport {auditTime, startWith, takeUntil} from 'rxjs/operators';\nimport {CdkScrollable, ExtendedScrollToOptions} from './scrollable';\nimport {ViewportRuler} from './viewport-ruler';\nimport {CdkVirtualScrollRepeater} from './virtual-scroll-repeater';\nimport {VIRTUAL_SCROLL_STRATEGY, VirtualScrollStrategy} from './virtual-scroll-strategy';\nimport {CdkVirtualScrollable, VIRTUAL_SCROLLABLE} from './virtual-scrollable';\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1: ListRange, r2: ListRange): boolean {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER =\n  typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\n@Component({\n  selector: 'cdk-virtual-scroll-viewport',\n  templateUrl: 'virtual-scroll-viewport.html',\n  styleUrl: 'virtual-scroll-viewport.css',\n  host: {\n    'class': 'cdk-virtual-scroll-viewport',\n    '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n    '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  providers: [\n    {\n      provide: CdkScrollable,\n      useFactory: (\n        virtualScrollable: CdkVirtualScrollable | null,\n        viewport: CdkVirtualScrollViewport,\n      ) => virtualScrollable || viewport,\n      deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n    },\n  ],\n})\nexport class CdkVirtualScrollViewport extends CdkVirtualScrollable implements OnInit, OnDestroy {\n  override elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _scrollStrategy = inject<VirtualScrollStrategy>(VIRTUAL_SCROLL_STRATEGY, {\n    optional: true,\n  })!;\n  scrollable = inject<CdkVirtualScrollable>(VIRTUAL_SCROLLABLE, {optional: true})!;\n\n  private _platform = inject(Platform);\n\n  /** Emits when the viewport is detached from a CdkVirtualForOf. */\n  private readonly _detachedSubject = new Subject<void>();\n\n  /** Emits when the rendered range changes. */\n  private readonly _renderedRangeSubject = new Subject<ListRange>();\n\n  /** The direction the viewport scrolls. */\n  @Input()\n  get orientation() {\n    return this._orientation;\n  }\n\n  set orientation(orientation: 'horizontal' | 'vertical') {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n      this._calculateSpacerSize();\n    }\n  }\n  private _orientation: 'horizontal' | 'vertical' = 'vertical';\n\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n  @Input({transform: booleanAttribute}) appendOnly: boolean = false;\n\n  // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n  // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n  // depending on how the strategy calculates the scrolled index, it may come at a cost to\n  // performance.\n  /** Emits when the index of the first element visible in the viewport changes. */\n  @Output()\n  readonly scrolledIndexChange: Observable<number> = new Observable((observer: Observer<number>) =>\n    this._scrollStrategy.scrolledIndexChange.subscribe(index =>\n      Promise.resolve().then(() => this.ngZone.run(() => observer.next(index))),\n    ),\n  );\n\n  /** The element that wraps the rendered content. */\n  @ViewChild('contentWrapper', {static: true}) _contentWrapper: ElementRef<HTMLElement>;\n\n  /** A stream that emits whenever the rendered range changes. */\n  readonly renderedRangeStream: Observable<ListRange> = this._renderedRangeSubject;\n\n  /**\n   * The total size of all content (in pixels), including content that is not currently rendered.\n   */\n  private _totalContentSize = 0;\n\n  /** A string representing the `style.width` property value to be used for the spacer element. */\n  _totalContentWidth = signal('');\n\n  /** A string representing the `style.height` property value to be used for the spacer element. */\n  _totalContentHeight = signal('');\n\n  /**\n   * The CSS transform applied to the rendered subset of items so that they appear within the bounds\n   * of the visible viewport.\n   */\n  private _renderedContentTransform: string;\n\n  /** The currently rendered range of indices. */\n  private _renderedRange: ListRange = {start: 0, end: 0};\n\n  /** The length of the data bound to this viewport (in number of items). */\n  private _dataLength = 0;\n\n  /** The size of the viewport (in pixels). */\n  private _viewportSize = 0;\n\n  /** the currently attached CdkVirtualScrollRepeater. */\n  private _forOf: CdkVirtualScrollRepeater<any> | null;\n\n  /** The last rendered content offset that was set. */\n  private _renderedContentOffset = 0;\n\n  /**\n   * Whether the last rendered content offset was to the end of the content (and therefore needs to\n   * be rewritten as an offset to the start of the content).\n   */\n  private _renderedContentOffsetNeedsRewrite = false;\n\n  /** Whether there is a pending change detection cycle. */\n  private _isChangeDetectionPending = false;\n\n  /** A list of functions to run after the next change detection cycle. */\n  private _runAfterChangeDetection: Function[] = [];\n\n  /** Subscription to changes in the viewport size. */\n  private _viewportChanges = Subscription.EMPTY;\n\n  private _injector = inject(Injector);\n\n  private _isDestroyed = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    const viewportRuler = inject(ViewportRuler);\n\n    if (!this._scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n\n    if (!this.scrollable) {\n      // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n      this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n      this.scrollable = this;\n    }\n  }\n\n  override ngOnInit() {\n    // Scrolling depends on the element dimensions which we can't get during SSR.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    if (this.scrollable === this) {\n      super.ngOnInit();\n    }\n    // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n    this.ngZone.runOutsideAngular(() =>\n      Promise.resolve().then(() => {\n        this._measureViewportSize();\n        this._scrollStrategy.attach(this);\n\n        this.scrollable\n          .elementScrolled()\n          .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null),\n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER),\n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed),\n          )\n          .subscribe(() => this._scrollStrategy.onContentScrolled());\n\n        this._markChangeDetectionNeeded();\n      }),\n    );\n  }\n\n  override ngOnDestroy() {\n    this.detach();\n    this._scrollStrategy.detach();\n\n    // Complete all subjects\n    this._renderedRangeSubject.complete();\n    this._detachedSubject.complete();\n    this._viewportChanges.unsubscribe();\n\n    this._isDestroyed = true;\n\n    super.ngOnDestroy();\n  }\n\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n  attach(forOf: CdkVirtualScrollRepeater<any>) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    }\n\n    // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n          this._scrollStrategy.onDataLengthChanged();\n        }\n        this._doChangeDetection();\n      });\n    });\n  }\n\n  /** Detaches the current `CdkVirtualForOf`. */\n  detach() {\n    this._forOf = null;\n    this._detachedSubject.next();\n  }\n\n  /** Gets the length of the data bound to this viewport (in number of items). */\n  getDataLength(): number {\n    return this._dataLength;\n  }\n\n  /** Gets the size of the viewport (in pixels). */\n  getViewportSize(): number {\n    return this._viewportSize;\n  }\n\n  // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n\n  /** Get the current rendered range of items. */\n  getRenderedRange(): ListRange {\n    return this._renderedRange;\n  }\n\n  measureBoundingClientRectWithScrollOffset(from: 'left' | 'top' | 'right' | 'bottom'): number {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n  setTotalContentSize(size: number) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n      this._calculateSpacerSize();\n      this._markChangeDetectionNeeded();\n    }\n  }\n\n  /** Sets the currently rendered range of indices. */\n  setRenderedRange(range: ListRange) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {start: 0, end: Math.max(this._renderedRange.end, range.end)};\n      }\n      this._renderedRangeSubject.next((this._renderedRange = range));\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n  getOffsetToRenderedContentStart(): number | null {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n  setRenderedContentOffset(offset: number, to: 'to-start' | 'to-end' = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`;\n      // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToOffset(offset: number, behavior: ScrollBehavior = 'auto') {\n    const options: ExtendedScrollToOptions = {behavior};\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n    this.scrollable.scrollTo(options);\n  }\n\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToIndex(index: number, behavior: ScrollBehavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n\n  /**\n   * Gets the current scroll offset from the start of the scrollable (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n  override measureScrollOffset(\n    from?: 'top' | 'left' | 'right' | 'bottom' | 'start' | 'end',\n  ): number {\n    // This is to break the call cycle\n    let measureScrollOffset: InstanceType<typeof CdkVirtualScrollable>['measureScrollOffset'];\n    if (this.scrollable == this) {\n      measureScrollOffset = (_from: NonNullable<typeof from>) => super.measureScrollOffset(_from);\n    } else {\n      measureScrollOffset = (_from: NonNullable<typeof from>) =>\n        this.scrollable.measureScrollOffset(_from);\n    }\n\n    return Math.max(\n      0,\n      measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n        this.measureViewportOffset(),\n    );\n  }\n\n  /**\n   * Measures the offset of the viewport from the scrolling container\n   * @param from The edge to measure from.\n   */\n  measureViewportOffset(from?: 'top' | 'left' | 'right' | 'bottom' | 'start' | 'end') {\n    let fromRect: 'left' | 'top' | 'right' | 'bottom';\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const isRtl = this.dir?.value == 'rtl';\n    if (from == 'start') {\n      fromRect = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      fromRect = isRtl ? LEFT : RIGHT;\n    } else if (from) {\n      fromRect = from;\n    } else {\n      fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n    }\n\n    const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n    const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n\n    return viewportClientRect - scrollerClientRect;\n  }\n\n  /** Measure the combined size of all of the rendered items. */\n  measureRenderedContentSize(): number {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n  measureRangeSize(range: ListRange): number {\n    if (!this._forOf) {\n      return 0;\n    }\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n\n  /** Update the viewport dimensions and re-render. */\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n    this._scrollStrategy.onDataLengthChanged();\n  }\n\n  /** Measure the viewport size. */\n  private _measureViewportSize() {\n    this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n  }\n\n  /** Queue up change detection to run. */\n  private _markChangeDetectionNeeded(runAfter?: Function) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    }\n\n    // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() =>\n        Promise.resolve().then(() => {\n          this._doChangeDetection();\n        }),\n      );\n    }\n  }\n\n  /** Run change detection. */\n  private _doChangeDetection() {\n    if (this._isDestroyed) {\n      return;\n    }\n\n    this.ngZone.run(() => {\n      // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n      // from the root, since the repeated items are content projected in. Calling `detectChanges`\n      // instead does not properly check the projected content.\n      this._changeDetectorRef.markForCheck();\n\n      // Apply the content transform. The transform can't be set via an Angular binding because\n      // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n      // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n      // the `Number` function first to coerce it to a numeric value.\n      this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n\n      afterNextRender(\n        () => {\n          this._isChangeDetectionPending = false;\n          const runAfterChangeDetection = this._runAfterChangeDetection;\n          this._runAfterChangeDetection = [];\n          for (const fn of runAfterChangeDetection) {\n            fn();\n          }\n        },\n        {injector: this._injector},\n      );\n    });\n  }\n\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n  private _calculateSpacerSize() {\n    this._totalContentHeight.set(\n      this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`,\n    );\n    this._totalContentWidth.set(\n      this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '',\n    );\n  }\n}\n", "<!--\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\n  position.\n-->\n<div #contentWrapper class=\"cdk-virtual-scroll-content-wrapper\">\n  <ng-content></ng-content>\n</div>\n<!--\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\n  so that the scrollbar captures the size of the entire data set.\n-->\n<div class=\"cdk-virtual-scroll-spacer\"\n     [style.width]=\"_totalContentWidth()\" [style.height]=\"_totalContentHeight()\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ArrayDataSource,\n  CollectionViewer,\n  DataSource,\n  ListRange,\n  isDataSource,\n  _RecycleViewRepeaterStrategy,\n  _VIEW_REPEATER_STRATEGY,\n  _ViewRepeaterItemInsertArgs,\n} from '../collections';\nimport {\n  Directive,\n  DoCheck,\n  EmbeddedViewRef,\n  Input,\n  IterableChangeRecord,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  NgIterable,\n  NgZone,\n  OnDestroy,\n  TemplateRef,\n  TrackByFunction,\n  ViewContainerRef,\n  inject,\n} from '@angular/core';\nimport {NumberInput, coerceNumberProperty} from '../coercion';\nimport {Observable, Subject, of as observableOf, isObservable} from 'rxjs';\nimport {pairwise, shareReplay, startWith, switchMap, takeUntil} from 'rxjs/operators';\nimport {CdkVirtualScrollRepeater} from './virtual-scroll-repeater';\nimport {CdkVirtualScrollViewport} from './virtual-scroll-viewport';\n\n/** The context for an item rendered by `CdkVirtualForOf` */\nexport type CdkVirtualForOfContext<T> = {\n  /** The item value. */\n  $implicit: T;\n  /** The DataSource, Observable, or NgIterable that was passed to *cdkVirtualFor. */\n  cdkVirtualForOf: DataSource<T> | Observable<T[]> | NgIterable<T>;\n  /** The index of the item in the DataSource. */\n  index: number;\n  /** The number of items in the DataSource. */\n  count: number;\n  /** Whether this is the first item in the DataSource. */\n  first: boolean;\n  /** Whether this is the last item in the DataSource. */\n  last: boolean;\n  /** Whether the index is even. */\n  even: boolean;\n  /** Whether the index is odd. */\n  odd: boolean;\n};\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation: 'horizontal' | 'vertical', direction: 'start' | 'end', node: Node) {\n  const el = node as Element;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\n@Directive({\n  selector: '[cdkVirtualFor][cdkVirtualForOf]',\n  providers: [{provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy}],\n})\nexport class CdkVirtualForOf<T>\n  implements CdkVirtualScrollRepeater<T>, CollectionViewer, DoCheck, OnDestroy\n{\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _template = inject<TemplateRef<CdkVirtualForOfContext<T>>>(TemplateRef);\n  private _differs = inject(IterableDiffers);\n  private _viewRepeater =\n    inject<_RecycleViewRepeaterStrategy<T, T, CdkVirtualForOfContext<T>>>(_VIEW_REPEATER_STRATEGY);\n  private _viewport = inject(CdkVirtualScrollViewport, {skipSelf: true});\n\n  /** Emits when the rendered view of the data changes. */\n  readonly viewChange = new Subject<ListRange>();\n\n  /** Subject that emits when a new DataSource instance is given. */\n  private readonly _dataSourceChanges = new Subject<DataSource<T>>();\n\n  /** The DataSource to display. */\n  @Input()\n  get cdkVirtualForOf(): DataSource<T> | Observable<T[]> | NgIterable<T> | null | undefined {\n    return this._cdkVirtualForOf;\n  }\n  set cdkVirtualForOf(value: DataSource<T> | Observable<T[]> | NgIterable<T> | null | undefined) {\n    this._cdkVirtualForOf = value;\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(\n        new ArrayDataSource<T>(isObservable(value) ? value : Array.from(value || [])),\n      );\n    }\n  }\n\n  _cdkVirtualForOf: DataSource<T> | Observable<T[]> | NgIterable<T> | null | undefined;\n\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n  @Input()\n  get cdkVirtualForTrackBy(): TrackByFunction<T> | undefined {\n    return this._cdkVirtualForTrackBy;\n  }\n  set cdkVirtualForTrackBy(fn: TrackByFunction<T> | undefined) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn\n      ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n      : undefined;\n  }\n  private _cdkVirtualForTrackBy: TrackByFunction<T> | undefined;\n\n  /** The template used to stamp out new elements. */\n  @Input()\n  set cdkVirtualForTemplate(value: TemplateRef<CdkVirtualForOfContext<T>>) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n  @Input()\n  get cdkVirtualForTemplateCacheSize(): number {\n    return this._viewRepeater.viewCacheSize;\n  }\n  set cdkVirtualForTemplateCacheSize(size: NumberInput) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n\n  /** Emits whenever the data in the current DataSource changes. */\n  readonly dataStream: Observable<readonly T[]> = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null),\n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(),\n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1),\n  );\n\n  /** The differ used to calculate changes to the data. */\n  private _differ: IterableDiffer<T> | null = null;\n\n  /** The most recent data emitted from the DataSource. */\n  private _data: readonly T[];\n\n  /** The currently rendered items. */\n  private _renderedItems: T[];\n\n  /** The currently rendered range of indices. */\n  private _renderedRange: ListRange;\n\n  /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n  private _needsUpdate = false;\n\n  private readonly _destroyed = new Subject<void>();\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const ngZone = inject(NgZone);\n\n    this.dataStream.subscribe(data => {\n      this._data = data;\n      this._onRenderedDataChange();\n    });\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n      this._onRenderedDataChange();\n    });\n    this._viewport.attach(this);\n  }\n\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n  measureRangeSize(range: ListRange, orientation: 'horizontal' | 'vertical'): number {\n    if (range.start >= range.end) {\n      return 0;\n    }\n    if (\n      (range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    }\n\n    // The index into the list of rendered views for the first item in the range.\n    const renderedStartIndex = range.start - this._renderedRange.start;\n    // The length of the range we're measuring.\n    const rangeLen = range.end - range.start;\n\n    // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n    let firstNode: HTMLElement | undefined;\n    let lastNode: HTMLElement | undefined;\n\n    // Find the first node by starting from the beginning and going forwards.\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex) as EmbeddedViewRef<\n        CdkVirtualForOfContext<T>\n      > | null;\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    }\n\n    // Find the last node by starting from the end and going backwards.\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex) as EmbeddedViewRef<\n        CdkVirtualForOfContext<T>\n      > | null;\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n\n    return firstNode && lastNode\n      ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n      : 0;\n  }\n\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n      this._needsUpdate = false;\n    }\n  }\n\n  ngOnDestroy() {\n    this._viewport.detach();\n\n    this._dataSourceChanges.next(undefined!);\n    this._dataSourceChanges.complete();\n    this.viewChange.complete();\n\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._viewRepeater.detach();\n  }\n\n  /** React to scroll state changes in the viewport. */\n  private _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n    this._needsUpdate = true;\n  }\n\n  /** Swap out one `DataSource` for another. */\n  private _changeDataSource(\n    oldDs: DataSource<T> | null,\n    newDs: DataSource<T> | null,\n  ): Observable<readonly T[]> {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : observableOf();\n  }\n\n  /** Update the `CdkVirtualForOfContext` for all views. */\n  private _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i) as EmbeddedViewRef<CdkVirtualForOfContext<T>>;\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n      view.detectChanges();\n    }\n  }\n\n  /** Apply changes to the DOM. */\n  private _applyChanges(changes: IterableChanges<T>) {\n    this._viewRepeater.applyChanges(\n      changes,\n      this._viewContainerRef,\n      (\n        record: IterableChangeRecord<T>,\n        _adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => this._getEmbeddedViewArgs(record, currentIndex!),\n      record => record.item,\n    );\n\n    // Update $implicit for any items that had an identity change.\n    changes.forEachIdentityChange((record: IterableChangeRecord<T>) => {\n      const view = this._viewContainerRef.get(record.currentIndex!) as EmbeddedViewRef<\n        CdkVirtualForOfContext<T>\n      >;\n      view.context.$implicit = record.item;\n    });\n\n    // Update the context variables on all items.\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i) as EmbeddedViewRef<CdkVirtualForOfContext<T>>;\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n  private _updateComputedContextProperties(context: CdkVirtualForOfContext<any>) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n\n  private _getEmbeddedViewArgs(\n    record: IterableChangeRecord<T>,\n    index: number,\n  ): _ViewRepeaterItemInsertArgs<CdkVirtualForOfContext<T>> {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf!,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false,\n      },\n      index,\n    };\n  }\n\n  static ngTemplateContextGuard<T>(\n    directive: CdkVirtualForOf<T>,\n    context: unknown,\n  ): context is CdkVirtualForOfContext<T> {\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive} from '@angular/core';\nimport {CdkVirtualScrollable, VIRTUAL_SCROLLABLE} from './virtual-scrollable';\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\n@Directive({\n  selector: '[cdkVirtualScrollingElement]',\n  providers: [{provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement}],\n  host: {\n    'class': 'cdk-virtual-scrollable',\n  },\n})\nexport class CdkVirtualScrollableElement extends CdkVirtualScrollable {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n  }\n\n  override measureBoundingClientRectWithScrollOffset(\n    from: 'left' | 'top' | 'right' | 'bottom',\n  ): number {\n    return (\n      this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n      this.measureScrollOffset(from)\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, ElementRef, inject, DOCUMENT} from '@angular/core';\n\nimport {CdkVirtualScrollable, VIRTUAL_SCROLLABLE} from './virtual-scrollable';\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\n@Directive({\n  selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n  providers: [{provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow}],\n})\nexport class CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    const document = inject(DOCUMENT);\n    this.elementRef = new ElementRef(document.documentElement);\n    this._scrollElement = document;\n  }\n\n  override measureBoundingClientRectWithScrollOffset(\n    from: 'left' | 'top' | 'right' | 'bottom',\n  ): number {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BidiModule} from '../bidi';\nimport {NgModule} from '@angular/core';\nimport {CdkFixedSizeVirtualScroll} from './fixed-size-virtual-scroll';\nimport {CdkScrollable} from './scrollable';\nimport {CdkVirtualForOf} from './virtual-for-of';\nimport {CdkVirtualScrollViewport} from './virtual-scroll-viewport';\nimport {CdkVirtualScrollableElement} from './virtual-scrollable-element';\nimport {CdkVirtualScrollableWindow} from './virtual-scrollable-window';\n\n@NgModule({\n  exports: [CdkScrollable],\n  imports: [CdkScrollable],\n})\nexport class CdkScrollableModule {}\n\n/**\n * @docs-primary-export\n */\n@NgModule({\n  imports: [\n    BidiModule,\n    CdkScrollableModule,\n    CdkVirtualScrollViewport,\n    CdkFixedSizeVirtualScroll,\n    CdkVirtualForOf,\n    CdkVirtualScrollableWindow,\n    CdkVirtualScrollableElement,\n  ],\n  exports: [\n    BidiModule,\n    CdkScrollableModule,\n    CdkFixedSizeVirtualScroll,\n    CdkVirtualForOf,\n    CdkVirtualScrollViewport,\n    CdkVirtualScrollableWindow,\n    CdkVirtualScrollableElement,\n  ],\n})\nexport class ScrollingModule {}\n\n// Re-export needed by the Angular compiler.\n// See: https://github.com/angular/components/issues/30663.\n// Note: These exports need to be stable and shouldn't be renamed unnecessarily because\n// consuming libraries might have references to them in their own partial compilation output.\nexport {Dir as ɵɵDir} from '../bidi';\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;AAYA;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,yBAAyB;;ACC3B;MACa,8BAA8B,CAAA;AACxB,IAAA,oBAAoB,GAAG,IAAI,OAAO,EAAU;;IAG7D,mBAAmB,GAAuB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;;IAGxF,SAAS,GAAoC,IAAI;;AAGjD,IAAA,SAAS;;AAGT,IAAA,YAAY;;AAGZ,IAAA,YAAY;AAEpB;;;;AAIG;AACH,IAAA,WAAA,CAAY,QAAgB,EAAE,WAAmB,EAAE,WAAmB,EAAA;AACpE,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;;AAGjC;;;AAGG;AACH,IAAA,MAAM,CAAC,QAAkC,EAAA;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;QACzB,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,oBAAoB,EAAE;;;IAI7B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;AACpC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB;;;;;AAKG;AACH,IAAA,uBAAuB,CAAC,QAAgB,EAAE,WAAmB,EAAE,WAAmB,EAAA;AAChF,QAAA,IAAI,WAAW,GAAG,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAChF,YAAA,MAAM,KAAK,CAAC,8EAA8E,CAAC;;AAE7F,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;QAC/B,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,oBAAoB,EAAE;;;IAI7B,iBAAiB,GAAA;QACf,IAAI,CAAC,oBAAoB,EAAE;;;IAI7B,mBAAmB,GAAA;QACjB,IAAI,CAAC,uBAAuB,EAAE;QAC9B,IAAI,CAAC,oBAAoB,EAAE;;;IAI7B,iBAAiB,GAAA;;;;IAKjB,uBAAuB,GAAA;;;AAIvB;;;;AAIG;IACH,aAAa,CAAC,KAAa,EAAE,QAAwB,EAAA;AACnD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC;;;;IAK3D,uBAAuB,GAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;AAGF,QAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;;;IAI7E,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;QAGF,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;AACvD,QAAA,MAAM,QAAQ,GAAG,EAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,GAAG,EAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;QACjD,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;;AAEvD,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC;;AAG9E,QAAA,IAAI,QAAQ,CAAC,GAAG,GAAG,UAAU,EAAE;;AAE7B,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;AAChE,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAC9B,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,GAAG,eAAe,CAAC,CAC1D;;;AAID,YAAA,IAAI,iBAAiB,IAAI,eAAe,EAAE;gBACxC,iBAAiB,GAAG,eAAe;AACnC,gBAAA,YAAY,GAAG,eAAe,GAAG,IAAI,CAAC,SAAS;gBAC/C,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;;YAGhD,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,GAAG,eAAe,CAAC,CAAC;;QAGpF,MAAM,WAAW,GAAG,YAAY,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS;AAClE,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK,IAAI,CAAC,EAAE;AAC1D,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC;AACjF,YAAA,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;AAC1D,YAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CACrB,UAAU,EACV,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,CACnF;;aACI;AACL,YAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,YAAY,GAAG,YAAY,CAAC;AAC/E,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,IAAI,QAAQ,CAAC,GAAG,IAAI,UAAU,EAAE;AAC/D,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;AAC7E,gBAAA,IAAI,SAAS,GAAG,CAAC,EAAE;AACjB,oBAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,GAAG,SAAS,CAAC;oBAC7D,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CACvB,CAAC,EACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CACnE;;;;AAKP,QAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;AACxE,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;;AAEhE;AAED;;;;;AAKG;AACG,SAAU,sCAAsC,CAAC,YAAuC,EAAA;IAC5F,OAAO,YAAY,CAAC,eAAe;AACrC;AAEA;MAWa,yBAAyB,CAAA;;AAEpC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAkB,EAAA;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,KAAK,CAAC;;IAE9C,SAAS,GAAG,EAAE;AAEd;;;AAGG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,KAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,CAAC;;IAEjD,YAAY,GAAG,GAAG;AAElB;;AAEG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,KAAkB,EAAA;AAChC,QAAA,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,CAAC;;IAEjD,YAAY,GAAG,GAAG;;AAGlB,IAAA,eAAe,GAAG,IAAI,8BAA8B,CAClD,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,WAAW,CACjB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC;;uGA5CtF,yBAAyB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,yBAAyB,EARzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,uBAAuB;AAChC,gBAAA,UAAU,EAAE,sCAAsC;gBAClD,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,yBAAyB,CAAC,CAAC;AACpD,aAAA;AACF,SAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBAVrC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uCAAuC;AACjD,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,uBAAuB;AAChC,4BAAA,UAAU,EAAE,sCAAsC;4BAClD,IAAI,EAAE,CAAC,UAAU,CAAC,MAAK,yBAA0B,CAAC,CAAC;AACpD,yBAAA;AACF,qBAAA;AACF,iBAAA;8BAIK,QAAQ,EAAA,CAAA;sBADX;gBAcG,WAAW,EAAA,CAAA;sBADd;gBAaG,WAAW,EAAA,CAAA;sBADd;;;ACtNH;AACO,MAAM,mBAAmB,GAAG;AAEnC;;;AAGG;MAEU,gBAAgB,CAAA;AACnB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAC/D,IAAA,sBAAsB;AAG9B,IAAA,WAAA,GAAA;;AAGiB,IAAA,SAAS,GAAG,IAAI,OAAO,EAAwB;;IAGxD,cAAc,GAAG,CAAC;AAE1B;;;AAGG;AACH,IAAA,gBAAgB,GAAqC,IAAI,GAAG,EAAE;AAE9D;;;;AAIG;AACH,IAAA,QAAQ,CAAC,UAAyB,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YAC1C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CACvB,UAAU,EACV,UAAU,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAC9E;;;AAIL;;;AAGG;AACH,IAAA,UAAU,CAAC,UAAyB,EAAA;QAClC,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC;QAEjE,IAAI,mBAAmB,EAAE;YACvB,mBAAmB,CAAC,WAAW,EAAE;AACjC,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC;;;AAI5C;;;;;;;;;AASG;IACH,QAAQ,CAAC,gBAAwB,mBAAmB,EAAA;AAClD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAOA,EAAY,EAAQ;;AAG7B,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,QAAwC,KAAI;AACjE,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAChC,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAC3D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CACzE;;;;AAKH,YAAA,MAAM,YAAY,GAChB,aAAa,GAAG;AACd,kBAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ;kBAChE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;YAExC,IAAI,CAAC,cAAc,EAAE;AAErB,YAAA,OAAO,MAAK;gBACV,YAAY,CAAC,WAAW,EAAE;gBAC1B,IAAI,CAAC,cAAc,EAAE;AAErB,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,oBAAA,IAAI,CAAC,sBAAsB,IAAI;AAC/B,oBAAA,IAAI,CAAC,sBAAsB,GAAG,SAAS;;AAE3C,aAAC;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,IAAI;AAC/B,QAAA,IAAI,CAAC,sBAAsB,GAAG,SAAS;AACvC,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;;AAG3B;;;;;AAKG;IACH,gBAAgB,CACd,mBAA6C,EAC7C,aAAsB,EAAA;QAEtB,MAAM,SAAS,GAAG,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC;AAEvE,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CACtC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAC5D;;;AAIH,IAAA,2BAA2B,CAAC,mBAA6C,EAAA;QACvE,MAAM,mBAAmB,GAAoB,EAAE;QAE/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,aAA2B,EAAE,UAAyB,KAAI;YACvF,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE;AACpE,gBAAA,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC;;AAExC,SAAC,CAAC;AAEF,QAAA,OAAO,mBAAmB;;;IAIpB,0BAA0B,CAChC,UAAyB,EACzB,mBAA6C,EAAA;AAE7C,QAAA,IAAI,OAAO,GAAuB,aAAa,CAAC,mBAAmB,CAAC;QACpE,IAAI,iBAAiB,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,aAAa;;;AAIhE,QAAA,GAAG;AACD,YAAA,IAAI,OAAO,IAAI,iBAAiB,EAAE;AAChC,gBAAA,OAAO,IAAI;;SAEd,SAAS,OAAO,GAAG,OAAQ,CAAC,aAAa;AAE1C,QAAA,OAAO,KAAK;;uGAhJH,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACWhC;;;;AAIG;MAIU,aAAa,CAAA;AACd,IAAA,UAAU,GAAG,MAAM,CAA0B,UAAU,CAAC;AACxD,IAAA,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC3C,IAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IACvB,GAAG,GAAI,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,cAAc,GAAgB,IAAI,CAAC,UAAU,CAAC,aAAa;AAClD,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;AAC3C,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAC7B,IAAA,cAAc;AACd,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAAS;AAG/C,IAAA,WAAA,GAAA;IAEA,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAClD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,IACxD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAClC,CACF;AACD,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;;IAGtC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,IAAI;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;IAI5B,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,gBAAgB;;;IAI9B,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;AAGxB;;;;;;;AAOG;AACH,IAAA,QAAQ,CAAC,OAAgC,EAAA;AACvC,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AACxC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK;;AAGjD,QAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AACxB,YAAA,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK;;AAGpD,QAAA,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;AACzB,YAAA,OAAO,CAAC,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG;;;AAIrD,QAAA,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;AACzB,YAAA,OAAoC,CAAC,GAAG;gBACvC,EAAE,CAAC,YAAY,GAAG,EAAE,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM;;;QAItD,IAAI,KAAK,IAAI,oBAAoB,EAAE,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC/D,YAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AACvB,gBAAA,OAAoC,CAAC,KAAK;oBACzC,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI;;AAGlD,YAAA,IAAI,oBAAoB,EAAE,IAAI,iBAAiB,CAAC,QAAQ,EAAE;AACxD,gBAAA,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK;;AACvB,iBAAA,IAAI,oBAAoB,EAAE,IAAI,iBAAiB,CAAC,OAAO,EAAE;AAC9D,gBAAA,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;;;aAE1D;AACL,YAAA,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;AACxB,gBAAA,OAAoC,CAAC,IAAI;oBACxC,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK;;;AAIrD,QAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;;AAG7B,IAAA,qBAAqB,CAAC,OAAwB,EAAA;AACpD,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;QAExC,IAAI,sBAAsB,EAAE,EAAE;AAC5B,YAAA,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;;aACf;AACL,YAAA,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE;AACvB,gBAAA,EAAE,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG;;AAE5B,YAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AACxB,gBAAA,EAAE,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI;;;;AAKlC;;;;;;;;AAQG;AACH,IAAA,mBAAmB,CAAC,IAA2D,EAAA;QAC7E,MAAM,IAAI,GAAG,MAAM;QACnB,MAAM,KAAK,GAAG,OAAO;AACrB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AACxC,QAAA,IAAI,IAAI,IAAI,KAAK,EAAE;YACjB,OAAO,EAAE,CAAC,SAAS;;AAErB,QAAA,IAAI,IAAI,IAAI,QAAQ,EAAE;YACpB,OAAO,EAAE,CAAC,YAAY,GAAG,EAAE,CAAC,YAAY,GAAG,EAAE,CAAC,SAAS;;;AAIzD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK;AACjD,QAAA,IAAI,IAAI,IAAI,OAAO,EAAE;YACnB,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI;;AACtB,aAAA,IAAI,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;;QAG7B,IAAI,KAAK,IAAI,oBAAoB,EAAE,IAAI,iBAAiB,CAAC,QAAQ,EAAE;;;AAGjE,YAAA,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,UAAU;;iBACjD;gBACL,OAAO,EAAE,CAAC,UAAU;;;aAEjB,IAAI,KAAK,IAAI,oBAAoB,EAAE,IAAI,iBAAiB,CAAC,OAAO,EAAE;;;AAGvE,YAAA,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW;;iBACjD;AACL,gBAAA,OAAO,CAAC,EAAE,CAAC,UAAU;;;aAElB;;;AAGL,YAAA,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO,EAAE,CAAC,UAAU;;iBACf;gBACL,OAAO,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,UAAU;;;;uGA3JjD,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mCAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mCAAmC;AAC9C,iBAAA;;;AC3BD;AACO,MAAM,mBAAmB,GAAG;AAQnC;;;AAGG;MAEU,aAAa,CAAA;AAChB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,UAAU;;AAGV,IAAA,aAAa;;AAGJ,IAAA,OAAO,GAAG,IAAI,OAAO,EAAS;;IAGrC,SAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAIzD,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;AAEpE,QAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC5B,gBAAA,MAAM,cAAc,GAAG,CAAC,KAAY,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBACjE,IAAI,CAAC,UAAU,GAAG;oBAChB,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC;oBACnD,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,mBAAmB,EAAE,cAAc,CAAC;iBAC/D;;;;AAKH,YAAA,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;AAC5D,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;;;IAIzB,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,mBAAmB,EAAE;;AAG5B,QAAA,MAAM,MAAM,GAAG,EAAC,KAAK,EAAE,IAAI,CAAC,aAAc,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,aAAc,CAAC,MAAM,EAAC;;AAGrF,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAK;;AAG5B,QAAA,OAAO,MAAM;;;IAIf,eAAe,GAAA;;;;;;;;;;AAUb,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,EAAE;QACvD,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC,eAAe,EAAE;QAE9C,OAAO;YACL,GAAG,EAAE,cAAc,CAAC,GAAG;YACvB,IAAI,EAAE,cAAc,CAAC,IAAI;AACzB,YAAA,MAAM,EAAE,cAAc,CAAC,GAAG,GAAG,MAAM;AACnC,YAAA,KAAK,EAAE,cAAc,CAAC,IAAI,GAAG,KAAK;YAClC,MAAM;YACN,KAAK;SACN;;;IAIH,yBAAyB,GAAA;;;AAGvB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B,OAAO,EAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAC;;;;;;;;AAS1B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;AAC/B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;AAChC,QAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAgB;AACjD,QAAA,MAAM,YAAY,GAAG,eAAe,CAAC,qBAAqB,EAAE;AAE5D,QAAA,MAAM,GAAG,GACP,CAAC,YAAY,CAAC,GAAG;YACjB,QAAQ,CAAC,IAAI,CAAC,SAAS;AACvB,YAAA,MAAM,CAAC,OAAO;AACd,YAAA,eAAe,CAAC,SAAS;AACzB,YAAA,CAAC;AAEH,QAAA,MAAM,IAAI,GACR,CAAC,YAAY,CAAC,IAAI;YAClB,QAAQ,CAAC,IAAI,CAAC,UAAU;AACxB,YAAA,MAAM,CAAC,OAAO;AACd,YAAA,eAAe,CAAC,UAAU;AAC1B,YAAA,CAAC;AAEH,QAAA,OAAO,EAAC,GAAG,EAAE,IAAI,EAAC;;AAGpB;;;;AAIG;IACH,MAAM,CAAC,eAAuB,mBAAmB,EAAA;QAC/C,OAAO,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO;;;IAI7E,UAAU,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,MAAM;;;IAIrC,mBAAmB,GAAA;AACzB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,cAAE,EAAC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW;cACrD,EAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAC;;uGAtIhB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAb,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cADD,MAAM,EAAA,CAAA;;2FAClB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;MCfnB,kBAAkB,GAAG,IAAI,cAAc,CAAuB,oBAAoB;AAE/F;;AAEG;AAEG,MAAgB,oBAAqB,SAAQ,aAAa,CAAA;AAE9D,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;AAGT;;;;AAIG;AACH,IAAA,mBAAmB,CAAC,WAAsC,EAAA;AACxD,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AAChD,QAAA,OAAO,WAAW,KAAK,YAAY,GAAG,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,YAAY;;uGAbpE,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADzC;;;AC4BD;AACA,SAAS,WAAW,CAAC,EAAa,EAAE,EAAa,EAAA;AAC/C,IAAA,OAAO,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG;AACjD;AAEA;;;;AAIG;AACH,MAAM,gBAAgB,GACpB,OAAO,qBAAqB,KAAK,WAAW,GAAG,uBAAuB,GAAG,aAAa;AAExF;AAuBM,MAAO,wBAAyB,SAAQ,oBAAoB,CAAA;AACvD,IAAA,UAAU,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,eAAe,GAAG,MAAM,CAAwB,uBAAuB,EAAE;AAC/E,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAE;IACH,UAAU,GAAG,MAAM,CAAuB,kBAAkB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAExE,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAGnB,IAAA,gBAAgB,GAAG,IAAI,OAAO,EAAQ;;AAGtC,IAAA,qBAAqB,GAAG,IAAI,OAAO,EAAa;;AAGjE,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAG1B,IAAI,WAAW,CAAC,WAAsC,EAAA;AACpD,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW,EAAE;AACrC,YAAA,IAAI,CAAC,YAAY,GAAG,WAAW;YAC/B,IAAI,CAAC,oBAAoB,EAAE;;;IAGvB,YAAY,GAA8B,UAAU;AAE5D;;;AAGG;IACmC,UAAU,GAAY,KAAK;;;;;;IAQxD,mBAAmB,GAAuB,IAAI,UAAU,CAAC,CAAC,QAA0B,KAC3F,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,IACtD,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1E,CACF;;AAG4C,IAAA,eAAe;;AAGnD,IAAA,mBAAmB,GAA0B,IAAI,CAAC,qBAAqB;AAEhF;;AAEG;IACK,iBAAiB,GAAG,CAAC;;AAG7B,IAAA,kBAAkB,GAAG,MAAM,CAAC,EAAE,CAAC;;AAG/B,IAAA,mBAAmB,GAAG,MAAM,CAAC,EAAE,CAAC;AAEhC;;;AAGG;AACK,IAAA,yBAAyB;;IAGzB,cAAc,GAAc,EAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAC;;IAG9C,WAAW,GAAG,CAAC;;IAGf,aAAa,GAAG,CAAC;;AAGjB,IAAA,MAAM;;IAGN,sBAAsB,GAAG,CAAC;AAElC;;;AAGG;IACK,kCAAkC,GAAG,KAAK;;IAG1C,yBAAyB,GAAG,KAAK;;IAGjC,wBAAwB,GAAe,EAAE;;AAGzC,IAAA,gBAAgB,GAAG,YAAY,CAAC,KAAK;AAErC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;IAE5B,YAAY,GAAG,KAAK;AAI5B,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAE3C,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC5E,YAAA,MAAM,KAAK,CAAC,gFAAgF,CAAC;;QAG/F,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAK;YAC5D,IAAI,CAAC,iBAAiB,EAAE;AAC1B,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;;YAEpB,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,wBAAwB,CAAC;AACrE,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;;IAIjB,QAAQ,GAAA;;AAEf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC7B;;AAGF,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC5B,KAAK,CAAC,QAAQ,EAAE;;;;;;AAMlB,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAC5B,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YAC1B,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;AAEjC,YAAA,IAAI,CAAC;AACF,iBAAA,eAAe;iBACf,IAAI;;YAEH,SAAS,CAAC,IAAI,CAAC;;;;AAIf,YAAA,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC;;;;AAI9B,YAAA,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;iBAE3B,SAAS,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;YAE5D,IAAI,CAAC,0BAA0B,EAAE;SAClC,CAAC,CACH;;IAGM,WAAW,GAAA;QAClB,IAAI,CAAC,MAAM,EAAE;AACb,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;;AAG7B,QAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE;AACrC,QAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;AAEnC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QAExB,KAAK,CAAC,WAAW,EAAE;;;AAIrB,IAAA,MAAM,CAAC,KAAoC,EAAA;AACzC,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAClE,YAAA,MAAM,KAAK,CAAC,+CAA+C,CAAC;;;;;AAM9D,QAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK;AACnB,YAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,IAAG;AAC7E,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM;AAC7B,gBAAA,IAAI,SAAS,KAAK,IAAI,CAAC,WAAW,EAAE;AAClC,oBAAA,IAAI,CAAC,WAAW,GAAG,SAAS;AAC5B,oBAAA,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;;gBAE5C,IAAI,CAAC,kBAAkB,EAAE;AAC3B,aAAC,CAAC;AACJ,SAAC,CAAC;;;IAIJ,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAClB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;;;IAI9B,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,WAAW;;;IAIzB,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,aAAa;;;;;;;IAS3B,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,cAAc;;AAG5B,IAAA,yCAAyC,CAAC,IAAyC,EAAA;AACjF,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;;AAGzE;;;AAGG;AACH,IAAA,mBAAmB,CAAC,IAAY,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;AACnC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,0BAA0B,EAAE;;;;AAKrC,IAAA,gBAAgB,CAAC,KAAgB,EAAA;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE;AAC5C,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,KAAK,GAAG,EAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAC;;AAEvE,YAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE;AAC9D,YAAA,IAAI,CAAC,0BAA0B,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;;;AAInF;;AAEG;IACH,+BAA+B,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAAC,sBAAsB;;AAGrF;;;AAGG;AACH,IAAA,wBAAwB,CAAC,MAAc,EAAE,EAAA,GAA4B,UAAU,EAAA;;AAE7E,QAAA,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,KAAK,UAAU,GAAG,CAAC,GAAG,MAAM;;;AAI1D,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK;AACjD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,IAAI,YAAY;QACrD,MAAM,IAAI,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG;AACrC,QAAA,MAAM,aAAa,GAAG,YAAY,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;AACpD,QAAA,IAAI,SAAS,GAAG,CAAY,SAAA,EAAA,IAAI,CAAI,CAAA,EAAA,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,CAAA,GAAA,CAAK;AACvE,QAAA,IAAI,CAAC,sBAAsB,GAAG,MAAM;AACpC,QAAA,IAAI,EAAE,KAAK,QAAQ,EAAE;AACnB,YAAA,SAAS,IAAI,CAAA,UAAA,EAAa,IAAI,CAAA,OAAA,CAAS;;;;AAIvC,YAAA,IAAI,CAAC,kCAAkC,GAAG,IAAI;;AAEhD,QAAA,IAAI,IAAI,CAAC,yBAAyB,IAAI,SAAS,EAAE;;;AAG/C,YAAA,IAAI,CAAC,yBAAyB,GAAG,SAAS;AAC1C,YAAA,IAAI,CAAC,0BAA0B,CAAC,MAAK;AACnC,gBAAA,IAAI,IAAI,CAAC,kCAAkC,EAAE;AAC3C,oBAAA,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,0BAA0B,EAAE;AAChE,oBAAA,IAAI,CAAC,kCAAkC,GAAG,KAAK;AAC/C,oBAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,sBAAsB,CAAC;;qBACrD;AACL,oBAAA,IAAI,CAAC,eAAe,CAAC,uBAAuB,EAAE;;AAElD,aAAC,CAAC;;;AAIN;;;;;;AAMG;AACH,IAAA,cAAc,CAAC,MAAc,EAAE,QAAA,GAA2B,MAAM,EAAA;AAC9D,QAAA,MAAM,OAAO,GAA4B,EAAC,QAAQ,EAAC;AACnD,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;AACrC,YAAA,OAAO,CAAC,KAAK,GAAG,MAAM;;aACjB;AACL,YAAA,OAAO,CAAC,GAAG,GAAG,MAAM;;AAEtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;;AAGnC;;;;AAIG;AACH,IAAA,aAAa,CAAC,KAAa,EAAE,QAAA,GAA2B,MAAM,EAAA;QAC5D,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAGrD;;;;AAIG;AACM,IAAA,mBAAmB,CAC1B,IAA4D,EAAA;;AAG5D,QAAA,IAAI,mBAAqF;AACzF,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;AAC3B,YAAA,mBAAmB,GAAG,CAAC,KAA+B,KAAK,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC;;aACtF;AACL,YAAA,mBAAmB,GAAG,CAAC,KAA+B,KACpD,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC;;QAG9C,OAAO,IAAI,CAAC,GAAG,CACb,CAAC,EACD,mBAAmB,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AAChF,YAAA,IAAI,CAAC,qBAAqB,EAAE,CAC/B;;AAGH;;;AAGG;AACH,IAAA,qBAAqB,CAAC,IAA4D,EAAA;AAChF,QAAA,IAAI,QAA6C;QACjD,MAAM,IAAI,GAAG,MAAM;QACnB,MAAM,KAAK,GAAG,OAAO;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,KAAK;AACtC,QAAA,IAAI,IAAI,IAAI,OAAO,EAAE;YACnB,QAAQ,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI;;AAC1B,aAAA,IAAI,IAAI,IAAI,KAAK,EAAE;YACxB,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;;aAC1B,IAAI,IAAI,EAAE;YACf,QAAQ,GAAG,IAAI;;aACV;AACL,YAAA,QAAQ,GAAG,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;;QAG/D,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,yCAAyC,CAAC,QAAQ,CAAC;AAC9F,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC;QAE1F,OAAO,kBAAkB,GAAG,kBAAkB;;;IAIhD,0BAA0B,GAAA;AACxB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa;AACpD,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,YAAY;;AAG3F;;;AAGG;AACH,IAAA,gBAAgB,CAAC,KAAgB,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,OAAO,CAAC;;AAEV,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC;;;IAI9D,iBAAiB,GAAA;;QAEf,IAAI,CAAC,oBAAoB,EAAE;AAC3B,QAAA,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;;;IAIpC,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC;;;AAIpE,IAAA,0BAA0B,CAAC,QAAmB,EAAA;QACpD,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;AAK9C,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACnC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;AACrC,YAAA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAC5B,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,kBAAkB,EAAE;aAC1B,CAAC,CACH;;;;IAKG,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB;;AAGF,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAK;;;;AAInB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;;AAMtC,YAAA,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,yBAAyB;YAEnF,eAAe,CACb,MAAK;AACH,gBAAA,IAAI,CAAC,yBAAyB,GAAG,KAAK;AACtC,gBAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,wBAAwB;AAC7D,gBAAA,IAAI,CAAC,wBAAwB,GAAG,EAAE;AAClC,gBAAA,KAAK,MAAM,EAAE,IAAI,uBAAuB,EAAE;AACxC,oBAAA,EAAE,EAAE;;aAEP,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;AACH,SAAC,CAAC;;;IAII,oBAAoB,GAAA;QAC1B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAC1B,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,EAAE,GAAG,CAAA,EAAG,IAAI,CAAC,iBAAiB,CAAI,EAAA,CAAA,CACvE;QACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACzB,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,CAAA,EAAG,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,GAAG,EAAE,CACvE;;uGA7cQ,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAxB,wBAAwB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAkChB,gBAAgB,CA7CxB,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iDAAA,EAAA,gCAAA,EAAA,+CAAA,EAAA,gCAAA,EAAA,EAAA,cAAA,EAAA,6BAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,CACV,iBAA8C,EAC9C,QAAkC,KAC/B,iBAAiB,IAAI,QAAQ;AAClC,gBAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE,wBAAwB,CAAC;AACnF,aAAA;AACF,SAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC9EH,0hBAaA,EAAA,MAAA,EAAA,CAAA,upDAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDmEa,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAtBpC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,6BAA6B,EAGjC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,6BAA6B;AACtC,wBAAA,mDAAmD,EAAE,8BAA8B;AACnF,wBAAA,iDAAiD,EAAE,8BAA8B;AAClF,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,aAAa;4BACtB,UAAU,EAAE,CACV,iBAA8C,EAC9C,QAAkC,KAC/B,iBAAiB,IAAI,QAAQ;AAClC,4BAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAA2B,wBAAA,CAAA;AACnF,yBAAA;AACF,qBAAA,EAAA,QAAA,EAAA,0hBAAA,EAAA,MAAA,EAAA,CAAA,upDAAA,CAAA,EAAA;wDAoBG,WAAW,EAAA,CAAA;sBADd;gBAiBqC,UAAU,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAQ3B,mBAAmB,EAAA,CAAA;sBAD3B;gBAQ4C,eAAe,EAAA,CAAA;sBAA3D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;;;AEpE7C;AACA,SAAS,SAAS,CAAC,WAAsC,EAAE,SAA0B,EAAE,IAAU,EAAA;IAC/F,MAAM,EAAE,GAAG,IAAe;AAC1B,IAAA,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE;AAC7B,QAAA,OAAO,CAAC;;AAEV,IAAA,MAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE;AAEvC,IAAA,IAAI,WAAW,KAAK,YAAY,EAAE;AAChC,QAAA,OAAO,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;;AAGvD,IAAA,OAAO,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;AACvD;AAEA;;;AAGG;MAKU,eAAe,CAAA;AAGlB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC5C,IAAA,SAAS,GAAG,MAAM,CAAyC,WAAW,CAAC;AACvE,IAAA,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;AAClC,IAAA,aAAa,GACnB,MAAM,CAAgE,uBAAuB,CAAC;IACxF,SAAS,GAAG,MAAM,CAAC,wBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG7D,IAAA,UAAU,GAAG,IAAI,OAAO,EAAa;;AAG7B,IAAA,kBAAkB,GAAG,IAAI,OAAO,EAAiB;;AAGlE,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB;;IAE9B,IAAI,eAAe,CAAC,KAAyE,EAAA;AAC3F,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK;AAC7B,QAAA,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;;aAC9B;;AAEL,YAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAC1B,IAAI,eAAe,CAAI,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAC9E;;;AAIL,IAAA,gBAAgB;AAEhB;;;AAGG;AACH,IAAA,IACI,oBAAoB,GAAA;QACtB,OAAO,IAAI,CAAC,qBAAqB;;IAEnC,IAAI,oBAAoB,CAAC,EAAkC,EAAA;AACzD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,qBAAqB,GAAG;AAC3B,cAAE,CAAC,KAAK,EAAE,IAAI,KAAK,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI;cACvF,SAAS;;AAEP,IAAA,qBAAqB;;IAG7B,IACI,qBAAqB,CAAC,KAA6C,EAAA;QACrE,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;;AAI1B;;;AAGG;AACH,IAAA,IACI,8BAA8B,GAAA;AAChC,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa;;IAEzC,IAAI,8BAA8B,CAAC,IAAiB,EAAA;QAClD,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC;;;AAItD,IAAA,UAAU,GAA6B,IAAI,CAAC,kBAAkB,CAAC,IAAI;;IAE1E,SAAS,CAAC,IAAI,CAAC;;AAEf,IAAA,QAAQ,EAAE;;;;AAIV,IAAA,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;AAE7D,IAAA,WAAW,CAAC,CAAC,CAAC,CACf;;IAGO,OAAO,GAA6B,IAAI;;AAGxC,IAAA,KAAK;;AAGL,IAAA,cAAc;;AAGd,IAAA,cAAc;;IAGd,YAAY,GAAG,KAAK;AAEX,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;AAIjD,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAE7B,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,IAAG;AAC/B,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI;YACjB,IAAI,CAAC,qBAAqB,EAAE;AAC9B,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,IAAG;AACpF,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;YAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE;AACpC,gBAAA,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;YAE7D,IAAI,CAAC,qBAAqB,EAAE;AAC9B,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;;AAG7B;;;;AAIG;IACH,gBAAgB,CAAC,KAAgB,EAAE,WAAsC,EAAA;QACvE,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,EAAE;AAC5B,YAAA,OAAO,CAAC;;QAEV,IACE,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG;aAC9E,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,KAAK,CAAC,CAA0D,wDAAA,CAAA,CAAC;;;QAIzE,MAAM,kBAAkB,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;;QAElE,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK;;;AAIxC,QAAA,IAAI,SAAkC;AACtC,QAAA,IAAI,QAAiC;;AAGrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACjC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAEtD;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACjC,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACxC;;;;AAKJ,QAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAEtD;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACjC,gBAAA,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;gBACpD;;;QAIJ,OAAO,SAAS,IAAI;AAClB,cAAE,SAAS,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS;cACnF,CAAC;;IAGP,SAAS,GAAA;QACP,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;;;;AAIrC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;YACtD,IAAI,CAAC,OAAO,EAAE;gBACZ,IAAI,CAAC,cAAc,EAAE;;iBAChB;AACL,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;;AAE7B,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;;IAI7B,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAEvB,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAU,CAAC;AACxC,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;AAClC,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAE1B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;;;IAIrB,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;;QAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AAC1F,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;;;YAGjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AAC5E,gBAAA,OAAO,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI;AAClF,aAAC,CAAC;;AAEJ,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;IAIlB,iBAAiB,CACvB,KAA2B,EAC3B,KAA2B,EAAA;QAE3B,IAAI,KAAK,EAAE;AACT,YAAA,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGxB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AACxB,QAAA,OAAO,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAGA,EAAY,EAAE;;;IAI7C,cAAc,GAAA;AACpB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AAC/B,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM;QACrC,OAAO,CAAC,EAAE,EAAE;YACV,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAA+C;AACxF,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC;AAClD,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK;AAC1B,YAAA,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,OAAO,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE;;;;AAKhB,IAAA,aAAa,CAAC,OAA2B,EAAA;AAC/C,QAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAC7B,OAAO,EACP,IAAI,CAAC,iBAAiB,EACtB,CACE,MAA+B,EAC/B,sBAAqC,EACrC,YAA2B,KACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,YAAa,CAAC,EACrD,MAAM,IAAI,MAAM,CAAC,IAAI,CACtB;;AAGD,QAAA,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAA+B,KAAI;AAChE,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAa,CAE3D;YACD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI;AACtC,SAAC,CAAC;;AAGF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AAC/B,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM;QACrC,OAAO,CAAC,EAAE,EAAE;YACV,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAA+C;AACxF,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC;AAClD,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK;AAC1B,YAAA,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,OAAO,CAAC;;;;AAK/C,IAAA,gCAAgC,CAAC,OAAoC,EAAA;QAC3E,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC;AACnC,QAAA,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,GAAG,CAAC;QAClD,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC;AACtC,QAAA,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI;;IAGrB,oBAAoB,CAC1B,MAA+B,EAC/B,KAAa,EAAA;;;;;QAMb,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,SAAS;AAC3B,YAAA,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM,CAAC,IAAI;;;gBAGtB,eAAe,EAAE,IAAI,CAAC,gBAAiB;gBACvC,KAAK,EAAE,CAAC,CAAC;gBACT,KAAK,EAAE,CAAC,CAAC;AACT,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,IAAI,EAAE,KAAK;AACX,gBAAA,GAAG,EAAE,KAAK;AACV,gBAAA,IAAI,EAAE,KAAK;AACZ,aAAA;YACD,KAAK;SACN;;AAGH,IAAA,OAAO,sBAAsB,CAC3B,SAA6B,EAC7B,OAAgB,EAAA;AAEhB,QAAA,OAAO,IAAI;;uGA1TF,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,EAAA,SAAA,EAFf,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5E,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;oBAC5C,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC;AACxF,iBAAA;wDAmBK,eAAe,EAAA,CAAA;sBADlB;gBAuBG,oBAAoB,EAAA,CAAA;sBADvB;gBAcG,qBAAqB,EAAA,CAAA;sBADxB;gBAaG,8BAA8B,EAAA,CAAA;sBADjC;;;ACzIH;;AAEG;AAQG,MAAO,2BAA4B,SAAQ,oBAAoB,CAAA;AAGnE,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;AAGA,IAAA,yCAAyC,CAChD,IAAyC,EAAA;AAEzC,QAAA,QACE,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;AAChE,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;uGAZvB,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA3B,2BAA2B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,SAAA,EAL3B,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,2BAA2B,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAKzE,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBAPvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,8BAA8B;oBACxC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAA6B,2BAAA,EAAC,CAAC;AACpF,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wBAAwB;AAClC,qBAAA;AACF,iBAAA;;;ACRD;;AAEG;AAKG,MAAO,0BAA2B,SAAQ,oBAAoB,CAAA;AAGlE,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC1D,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ;;AAGvB,IAAA,yCAAyC,CAChD,IAAyC,EAAA;AAEzC,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;;uGAb9D,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAA1B,0BAA0B,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,SAAA,EAF1B,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,0BAA0B,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAExE,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAJtC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;oBACrD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAA4B,0BAAA,EAAC,CAAC;AACpF,iBAAA;;;MCGY,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAnB,mBAAmB,EAAA,OAAA,EAAA,CAFpB,aAAa,CAAA,EAAA,OAAA,EAAA,CADb,aAAa,CAAA,EAAA,CAAA;wGAGZ,mBAAmB,EAAA,CAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;oBACxB,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;;AAEG;MAqBU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAlBxB,OAAA,EAAA,CAAA,UAAU,EAPD,mBAAmB,EAS5B,wBAAwB;YACxB,yBAAyB;YACzB,eAAe;YACf,0BAA0B;AAC1B,YAAA,2BAA2B,CAG3B,EAAA,OAAA,EAAA,CAAA,UAAU,EAhBD,mBAAmB,EAkB5B,yBAAyB;YACzB,eAAe;YACf,wBAAwB;YACxB,0BAA0B;YAC1B,2BAA2B,CAAA,EAAA,CAAA;AAGlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAlBxB,UAAU;YACV,mBAAmB,EAQnB,UAAU,EAhBD,mBAAmB,CAAA,EAAA,CAAA;;2FAyBnB,eAAe,EAAA,UAAA,EAAA,CAAA;kBApB3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,UAAU;wBACV,mBAAmB;wBACnB,wBAAwB;wBACxB,yBAAyB;wBACzB,eAAe;wBACf,0BAA0B;wBAC1B,2BAA2B;AAC5B,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,UAAU;wBACV,mBAAmB;wBACnB,yBAAyB;wBACzB,eAAe;wBACf,wBAAwB;wBACxB,0BAA0B;wBAC1B,2BAA2B;AAC5B,qBAAA;AACF,iBAAA;;;;;"}