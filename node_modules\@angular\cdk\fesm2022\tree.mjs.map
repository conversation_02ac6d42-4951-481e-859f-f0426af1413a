{"version": 3, "file": "tree.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/control/base-tree-control.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/control/flat-tree-control.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/control/nested-tree-control.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/outlet.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/tree-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/tree.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/nested-node.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/padding.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/toggle.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/tree/tree-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {SelectionModel} from '../../collections';\nimport {Observable} from 'rxjs';\nimport {TreeControl} from './tree-control';\n\n/**\n * Base tree control. It has basic toggle/expand/collapse operations on a single data node.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor. To be removed in a future version.\n * @breaking-change 21.0.0\n */\nexport abstract class BaseTreeControl<T, K = T> implements TreeControl<T, K> {\n  /** Gets a list of descendent data nodes of a subtree rooted at given data node recursively. */\n  abstract getDescendants(dataNode: T): T[];\n\n  /** Expands all data nodes in the tree. */\n  abstract expandAll(): void;\n\n  /** Saved data node for `expandAll` action. */\n  dataNodes: T[];\n\n  /** A selection model with multi-selection to track expansion status. */\n  expansionModel: SelectionModel<K> = new SelectionModel<K>(true);\n\n  /**\n   * Returns the identifier by which a dataNode should be tracked, should its\n   * reference change.\n   *\n   * Similar to trackBy for *ngFor\n   */\n  trackBy?: (dataNode: T) => K;\n\n  /** Get depth of a given data node, return the level number. This is for flat tree node. */\n  getLevel: (dataNode: T) => number;\n\n  /**\n   * Whether the data node is expandable. Returns true if expandable.\n   * This is for flat tree node.\n   */\n  isExpandable: (dataNode: T) => boolean;\n\n  /** Gets a stream that emits whenever the given data node's children change. */\n  getChildren: (dataNode: T) => Observable<T[]> | T[] | undefined | null;\n\n  /** Toggles one single data node's expanded/collapsed state. */\n  toggle(dataNode: T): void {\n    this.expansionModel.toggle(this._trackByValue(dataNode));\n  }\n\n  /** Expands one single data node. */\n  expand(dataNode: T): void {\n    this.expansionModel.select(this._trackByValue(dataNode));\n  }\n\n  /** Collapses one single data node. */\n  collapse(dataNode: T): void {\n    this.expansionModel.deselect(this._trackByValue(dataNode));\n  }\n\n  /** Whether a given data node is expanded or not. Returns true if the data node is expanded. */\n  isExpanded(dataNode: T): boolean {\n    return this.expansionModel.isSelected(this._trackByValue(dataNode));\n  }\n\n  /** Toggles a subtree rooted at `node` recursively. */\n  toggleDescendants(dataNode: T): void {\n    this.expansionModel.isSelected(this._trackByValue(dataNode))\n      ? this.collapseDescendants(dataNode)\n      : this.expandDescendants(dataNode);\n  }\n\n  /** Collapse all dataNodes in the tree. */\n  collapseAll(): void {\n    this.expansionModel.clear();\n  }\n\n  /** Expands a subtree rooted at given data node recursively. */\n  expandDescendants(dataNode: T): void {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.select(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n\n  /** Collapses a subtree rooted at given data node recursively. */\n  collapseDescendants(dataNode: T): void {\n    let toBeProcessed = [dataNode];\n    toBeProcessed.push(...this.getDescendants(dataNode));\n    this.expansionModel.deselect(...toBeProcessed.map(value => this._trackByValue(value)));\n  }\n\n  protected _trackByValue(value: T | K): K {\n    return this.trackBy ? this.trackBy(value as T) : (value as K);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BaseTreeControl} from './base-tree-control';\n\n/** Optional set of configuration that can be provided to the FlatTreeControl. */\nexport interface FlatTreeControlOptions<T, K> {\n  trackBy?: (dataNode: T) => K;\n}\n\n/**\n * Flat tree control. Able to expand/collapse a subtree recursively for flattened tree.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nexport class FlatTreeControl<T, K = T> extends BaseTreeControl<T, K> {\n  /** Construct with flat tree data node functions getLevel and isExpandable. */\n  constructor(\n    public override getLevel: (dataNode: T) => number,\n    public override isExpandable: (dataNode: T) => boolean,\n    public options?: FlatTreeControlOptions<T, K>,\n  ) {\n    super();\n\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n  }\n\n  /**\n   * Gets a list of the data node's subtree of descendent data nodes.\n   *\n   * To make this working, the `dataNodes` of the TreeControl must be flattened tree nodes\n   * with correct levels.\n   */\n  getDescendants(dataNode: T): T[] {\n    const startIndex = this.dataNodes.indexOf(dataNode);\n    const results: T[] = [];\n\n    // Goes through flattened tree nodes in the `dataNodes` array, and get all descendants.\n    // The level of descendants of a tree node must be greater than the level of the given\n    // tree node.\n    // If we reach a node whose level is equal to the level of the tree node, we hit a sibling.\n    // If we reach a node whose level is greater than the level of the tree node, we hit a\n    // sibling of an ancestor.\n    for (\n      let i = startIndex + 1;\n      i < this.dataNodes.length && this.getLevel(dataNode) < this.getLevel(this.dataNodes[i]);\n      i++\n    ) {\n      results.push(this.dataNodes[i]);\n    }\n    return results;\n  }\n\n  /**\n   * Expands all data nodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all flattened\n   * data nodes of the tree.\n   */\n  expandAll(): void {\n    this.expansionModel.select(...this.dataNodes.map(node => this._trackByValue(node)));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Observable, isObservable} from 'rxjs';\nimport {take, filter} from 'rxjs/operators';\nimport {BaseTreeControl} from './base-tree-control';\n\n/** Optional set of configuration that can be provided to the NestedTreeControl. */\nexport interface NestedTreeControlOptions<T, K> {\n  /** Function to determine if the provided node is expandable. */\n  isExpandable?: (dataNode: T) => boolean;\n  trackBy?: (dataNode: T) => K;\n}\n\n/**\n * Nested tree control. Able to expand/collapse a subtree recursively for NestedNode type.\n *\n * @deprecated Use one of levelAccessor or childrenAccessor instead. To be removed in a future\n * version.\n * @breaking-change 21.0.0\n */\nexport class NestedTreeControl<T, K = T> extends BaseTreeControl<T, K> {\n  /** Construct with nested tree function getChildren. */\n  constructor(\n    public override getChildren: (dataNode: T) => Observable<T[]> | T[] | undefined | null,\n    public options?: NestedTreeControlOptions<T, K>,\n  ) {\n    super();\n\n    if (this.options) {\n      this.trackBy = this.options.trackBy;\n    }\n\n    if (this.options?.isExpandable) {\n      this.isExpandable = this.options.isExpandable;\n    }\n  }\n\n  /**\n   * Expands all dataNodes in the tree.\n   *\n   * To make this working, the `dataNodes` variable of the TreeControl must be set to all root level\n   * data nodes of the tree.\n   */\n  expandAll(): void {\n    this.expansionModel.clear();\n    const allNodes = this.dataNodes.reduce(\n      (accumulator: T[], dataNode) => [...accumulator, ...this.getDescendants(dataNode), dataNode],\n      [],\n    );\n    this.expansionModel.select(...allNodes.map(node => this._trackByValue(node)));\n  }\n\n  /** Gets a list of descendant dataNodes of a subtree rooted at given data node recursively. */\n  getDescendants(dataNode: T): T[] {\n    const descendants: T[] = [];\n\n    this._getDescendants(descendants, dataNode);\n    // Remove the node itself\n    return descendants.splice(1);\n  }\n\n  /** A helper function to get descendants recursively. */\n  protected _getDescendants(descendants: T[], dataNode: T): void {\n    descendants.push(dataNode);\n    const childrenNodes = this.getChildren(dataNode);\n    if (Array.isArray(childrenNodes)) {\n      childrenNodes.forEach((child: T) => this._getDescendants(descendants, child));\n    } else if (isObservable(childrenNodes)) {\n      // TypeScript as of version 3.5 doesn't seem to treat `Boolean` like a function that\n      // returns a `boolean` specifically in the context of `filter`, so we manually clarify that.\n      childrenNodes.pipe(take(1), filter(Boolean as () => boolean)).subscribe(children => {\n        for (const child of children) {\n          this._getDescendants(descendants, child);\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Directive, InjectionToken, ViewContainerRef, inject} from '@angular/core';\n\n/**\n * Injection token used to provide a `CdkTreeNode` to its outlet.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const CDK_TREE_NODE_OUTLET_NODE = new InjectionToken<{}>('CDK_TREE_NODE_OUTLET_NODE');\n\n/**\n * Outlet for nested CdkNode. Put `[cdkTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\n@Directive({\n  selector: '[cdkTreeNodeOutlet]',\n})\nexport class CdkTreeNodeOutlet {\n  viewContainer = inject(ViewContainerRef);\n  _node? = inject(CDK_TREE_NODE_OUTLET_NODE, {optional: true});\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, TemplateRef, inject} from '@angular/core';\n\n/** Context provided to the tree node component. */\nexport class CdkTreeNodeOutletContext<T> {\n  /** Data for the node. */\n  $implicit: T;\n\n  /** Depth of the node. */\n  level: number;\n\n  /** Index location of the node. */\n  index?: number;\n\n  /** Length of the number of total dataNodes. */\n  count?: number;\n\n  constructor(data: T) {\n    this.$implicit = data;\n  }\n}\n\n/**\n * Data node definition for the CdkTree.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\n@Directive({\n  selector: '[cdkTreeNodeDef]',\n  inputs: [{name: 'when', alias: 'cdkTreeNodeDefWhen'}],\n})\nexport class CdkTreeNodeDef<T> {\n  /** @docs-private */\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  /**\n   * Function that should return true if this node template should be used for the provided node\n   * data and index. If left undefined, this node will be considered the default node template to\n   * use when no other when functions return true for the data.\n   * For every node, there must be at least one when function that passes or an undefined to\n   * default.\n   */\n  when: (index: number, nodeData: T) => boolean;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Returns an error to be thrown when there is no usable data.\n * @docs-private\n */\nexport function getTreeNoValidDataSourceError() {\n  return Error(`A valid data source must be provided.`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple nodes that are missing a when function.\n * @docs-private\n */\nexport function getTreeMultipleDefaultNodeDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n\n/**\n * Returns an error to be thrown when there are no matching node defs for a particular set of data.\n * @docs-private\n */\nexport function getTreeMissingMatchingNodeDefError() {\n  return Error(`Could not find a matching node definition for the provided node data.`);\n}\n\n/**\n * Returns an error to be thrown when there is no tree control.\n * @docs-private\n */\nexport function getTreeControlMissingError() {\n  return Error(`Could not find a tree control, levelAccessor, or childrenAccessor for the tree.`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple ways of specifying children or level\n * provided to the tree.\n * @docs-private\n */\nexport function getMultipleTreeControlsError() {\n  return Error(`More than one of tree control, levelAccessor, or childrenAccessor were provided.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  TREE_KEY_MANAGER,\n  TreeKeyManagerFactory,\n  TreeKeyManagerItem,\n  TreeKeyManagerOptions,\n  TreeKeyManagerStrategy,\n} from '../a11y';\nimport {Directionality} from '../bidi';\nimport {\n  CollectionViewer,\n  DataSource,\n  isDataSource,\n  SelectionChange,\n  SelectionModel,\n} from '../collections';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  EmbeddedViewRef,\n  Input,\n  IterableChangeRecord,\n  IterableDiffer,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  TrackByFunction,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  numberAttribute,\n  inject,\n  booleanAttribute,\n} from '@angular/core';\nimport {coerceObservable} from '../coercion/private';\nimport {\n  BehaviorSubject,\n  combineLatest,\n  concat,\n  EMPTY,\n  Observable,\n  Subject,\n  Subscription,\n  isObservable,\n  of as observableOf,\n} from 'rxjs';\nimport {\n  distinctUntilChanged,\n  concatMap,\n  map,\n  reduce,\n  startWith,\n  switchMap,\n  take,\n  takeUntil,\n  tap,\n} from 'rxjs/operators';\nimport {TreeControl} from './control/tree-control';\nimport {CdkTreeNodeDef, CdkTreeNodeOutletContext} from './node';\nimport {CdkTreeNodeOutlet} from './outlet';\nimport {\n  getMultipleTreeControlsError,\n  getTreeControlMissingError,\n  getTreeMissingMatchingNodeDefError,\n  getTreeMultipleDefaultNodeDefsError,\n  getTreeNoValidDataSourceError,\n} from './tree-errors';\n\ntype RenderingData<T> =\n  | {\n      flattenedNodes: null;\n      nodeType: null;\n      renderNodes: readonly T[];\n    }\n  | {\n      flattenedNodes: readonly T[];\n      nodeType: 'nested' | 'flat';\n      renderNodes: readonly T[];\n    };\n\n/**\n * CDK tree component that connects with a data source to retrieve data of type `T` and renders\n * dataNodes with hierarchy. Updates the dataNodes when new data is provided by the data source.\n */\n@Component({\n  selector: 'cdk-tree',\n  exportAs: 'cdkTree',\n  template: `<ng-container cdkTreeNodeOutlet></ng-container>`,\n  host: {\n    'class': 'cdk-tree',\n    'role': 'tree',\n    '(keydown)': '_sendKeydownToKeyManager($event)',\n  },\n  encapsulation: ViewEncapsulation.None,\n  // The \"OnPush\" status for the `CdkTree` component is effectively a noop, so we are removing it.\n  // The view for `CdkTree` consists entirely of templates declared in other views. As they are\n  // declared elsewhere, they are checked when their declaration points are checked.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [CdkTreeNodeOutlet],\n})\nexport class CdkTree<T, K = T>\n  implements\n    AfterContentChecked,\n    AfterContentInit,\n    AfterViewInit,\n    CollectionViewer,\n    OnDestroy,\n    OnInit\n{\n  private _differs = inject(IterableDiffers);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  private _elementRef = inject(ElementRef);\n\n  private _dir = inject(Directionality);\n\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _onDestroy = new Subject<void>();\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** Stores the node definition that does not have a when predicate. */\n  private _defaultNodeDef: CdkTreeNodeDef<T> | null;\n\n  /** Data subscription */\n  private _dataSubscription: Subscription | null;\n\n  /** Level of nodes */\n  private _levels: Map<K, number> = new Map<K, number>();\n\n  /** The immediate parents for a node. This is `null` if there is no parent. */\n  private _parents: Map<K, T | null> = new Map<K, T | null>();\n\n  /**\n   * Nodes grouped into each set, which is a list of nodes displayed together in the DOM.\n   *\n   * Lookup key is the parent of a set. Root nodes have key of null.\n   *\n   * Values is a 'set' of tree nodes. Each tree node maps to a treeitem element. Sets are in the\n   * order that it is rendered. Each set maps directly to aria-posinset and aria-setsize attributes.\n   */\n  private _ariaSets: Map<K | null, T[]> = new Map<K | null, T[]>();\n\n  /**\n   * Provides a stream containing the latest data array to render. Influenced by the tree's\n   * stream of view window (what dataNodes are currently on screen).\n   * Data source can be an observable of data array, or a data array to render.\n   */\n  @Input()\n  get dataSource(): DataSource<T> | Observable<T[]> | T[] {\n    return this._dataSource;\n  }\n  set dataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  private _dataSource: DataSource<T> | Observable<T[]> | T[];\n\n  /**\n   * The tree controller\n   *\n   * @deprecated Use one of `levelAccessor` or `childrenAccessor` instead. To be removed in a\n   * future version.\n   * @breaking-change 21.0.0\n   */\n  @Input() treeControl?: TreeControl<T, K>;\n\n  /**\n   * Given a data node, determines what tree level the node is at.\n   *\n   * One of levelAccessor or childrenAccessor must be specified, not both.\n   * This is enforced at run-time.\n   */\n  @Input() levelAccessor?: (dataNode: T) => number;\n\n  /**\n   * Given a data node, determines what the children of that node are.\n   *\n   * One of levelAccessor or childrenAccessor must be specified, not both.\n   * This is enforced at run-time.\n   */\n  @Input() childrenAccessor?: (dataNode: T) => T[] | Observable<T[]>;\n\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize node operations by identifying a node based on its data\n   * relative to the function to know if a node should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  @Input() trackBy: TrackByFunction<T>;\n\n  /**\n   * Given a data node, determines the key by which we determine whether or not this node is expanded.\n   */\n  @Input() expansionKey?: (dataNode: T) => K;\n\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  @ViewChild(CdkTreeNodeOutlet, {static: true}) _nodeOutlet: CdkTreeNodeOutlet;\n\n  /** The tree node template for the tree */\n  @ContentChildren(CdkTreeNodeDef, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  _nodeDefs: QueryList<CdkTreeNodeDef<T>>;\n\n  // TODO(tinayuangao): Setup a listener for scrolling, emit the calculated view to viewChange.\n  //     Remove the MAX_VALUE in viewChange\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   */\n  readonly viewChange = new BehaviorSubject<{start: number; end: number}>({\n    start: 0,\n    end: Number.MAX_VALUE,\n  });\n\n  /** Keep track of which nodes are expanded. */\n  private _expansionModel?: SelectionModel<K>;\n\n  /**\n   * Maintain a synchronous cache of flattened data nodes. This will only be\n   * populated after initial render, and in certain cases, will be delayed due to\n   * relying on Observable `getChildren` calls.\n   */\n  private _flattenedNodes: BehaviorSubject<readonly T[]> = new BehaviorSubject<readonly T[]>([]);\n\n  /** The automatically determined node type for the tree. */\n  private _nodeType: BehaviorSubject<'flat' | 'nested' | null> = new BehaviorSubject<\n    'flat' | 'nested' | null\n  >(null);\n\n  /** The mapping between data and the node that is rendered. */\n  private _nodes: BehaviorSubject<Map<K, CdkTreeNode<T, K>>> = new BehaviorSubject(\n    new Map<K, CdkTreeNode<T, K>>(),\n  );\n\n  /**\n   * Synchronous cache of nodes for the `TreeKeyManager`. This is separate\n   * from `_flattenedNodes` so they can be independently updated at different\n   * times.\n   */\n  private _keyManagerNodes: BehaviorSubject<readonly T[]> = new BehaviorSubject<readonly T[]>([]);\n\n  private _keyManagerFactory = inject(TREE_KEY_MANAGER) as TreeKeyManagerFactory<CdkTreeNode<T, K>>;\n\n  /** The key manager for this tree. Handles focus and activation based on user keyboard input. */\n  _keyManager: TreeKeyManagerStrategy<CdkTreeNode<T, K>>;\n  private _viewInit = false;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterContentInit() {\n    this._initializeKeyManager();\n  }\n\n  ngAfterContentChecked() {\n    this._updateDefaultNodeDefinition();\n    this._subscribeToDataChanges();\n  }\n\n  ngOnDestroy() {\n    this._nodeOutlet.viewContainer.clear();\n\n    this.viewChange.complete();\n    this._onDestroy.next();\n    this._onDestroy.complete();\n\n    if (this._dataSource && typeof (this._dataSource as DataSource<T>).disconnect === 'function') {\n      (this.dataSource as DataSource<T>).disconnect(this);\n    }\n\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n\n    // In certain tests, the tree might be destroyed before this is initialized\n    // in `ngAfterContentInit`.\n    this._keyManager?.destroy();\n  }\n\n  ngOnInit() {\n    this._checkTreeControlUsage();\n    this._initializeDataDiffer();\n  }\n\n  ngAfterViewInit() {\n    this._viewInit = true;\n  }\n\n  private _updateDefaultNodeDefinition() {\n    const defaultNodeDefs = this._nodeDefs.filter(def => !def.when);\n    if (defaultNodeDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMultipleDefaultNodeDefsError();\n    }\n    this._defaultNodeDef = defaultNodeDefs[0];\n  }\n\n  /**\n   * Sets the node type for the tree, if it hasn't been set yet.\n   *\n   * This will be called by the first node that's rendered in order for the tree\n   * to determine what data transformations are required.\n   */\n  _setNodeTypeIfUnset(newType: 'flat' | 'nested') {\n    const currentType = this._nodeType.value;\n\n    if (currentType === null) {\n      this._nodeType.next(newType);\n    } else if ((typeof ngDevMode === 'undefined' || ngDevMode) && currentType !== newType) {\n      console.warn(\n        `Tree is using conflicting node types which can cause unexpected behavior. ` +\n          `Please use tree nodes of the same type (e.g. only flat or only nested). ` +\n          `Current node type: \"${currentType}\", new node type \"${newType}\".`,\n      );\n    }\n  }\n\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the node outlet. Otherwise start listening for new data.\n   */\n  private _switchDataSource(dataSource: DataSource<T> | Observable<T[]> | T[]) {\n    if (this._dataSource && typeof (this._dataSource as DataSource<T>).disconnect === 'function') {\n      (this.dataSource as DataSource<T>).disconnect(this);\n    }\n\n    if (this._dataSubscription) {\n      this._dataSubscription.unsubscribe();\n      this._dataSubscription = null;\n    }\n\n    // Remove the all dataNodes if there is now no data source\n    if (!dataSource) {\n      this._nodeOutlet.viewContainer.clear();\n    }\n\n    this._dataSource = dataSource;\n    if (this._nodeDefs) {\n      this._subscribeToDataChanges();\n    }\n  }\n\n  _getExpansionModel() {\n    if (!this.treeControl) {\n      this._expansionModel ??= new SelectionModel<K>(true);\n      return this._expansionModel;\n    }\n    return this.treeControl.expansionModel;\n  }\n\n  /** Set up a subscription for the data provided by the data source. */\n  private _subscribeToDataChanges() {\n    if (this._dataSubscription) {\n      return;\n    }\n\n    let dataStream: Observable<readonly T[]> | undefined;\n\n    if (isDataSource(this._dataSource)) {\n      dataStream = this._dataSource.connect(this);\n    } else if (isObservable(this._dataSource)) {\n      dataStream = this._dataSource;\n    } else if (Array.isArray(this._dataSource)) {\n      dataStream = observableOf(this._dataSource);\n    }\n\n    if (!dataStream) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        throw getTreeNoValidDataSourceError();\n      }\n      return;\n    }\n\n    this._dataSubscription = this._getRenderData(dataStream)\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(renderingData => {\n        this._renderDataChanges(renderingData);\n      });\n  }\n\n  /** Given an Observable containing a stream of the raw data, returns an Observable containing the RenderingData */\n  private _getRenderData(dataStream: Observable<readonly T[]>): Observable<RenderingData<T>> {\n    const expansionModel = this._getExpansionModel();\n    return combineLatest([\n      dataStream,\n      this._nodeType,\n      // We don't use the expansion data directly, however we add it here to essentially\n      // trigger data rendering when expansion changes occur.\n      expansionModel.changed.pipe(\n        startWith(null),\n        tap(expansionChanges => {\n          this._emitExpansionChanges(expansionChanges);\n        }),\n      ),\n    ]).pipe(\n      switchMap(([data, nodeType]) => {\n        if (nodeType === null) {\n          return observableOf({renderNodes: data, flattenedNodes: null, nodeType} as const);\n        }\n\n        // If we're here, then we know what our node type is, and therefore can\n        // perform our usual rendering pipeline, which necessitates converting the data\n        return this._computeRenderingData(data, nodeType).pipe(\n          map(convertedData => ({...convertedData, nodeType}) as const),\n        );\n      }),\n    );\n  }\n\n  private _renderDataChanges(data: RenderingData<T>) {\n    if (data.nodeType === null) {\n      this.renderNodeChanges(data.renderNodes);\n      return;\n    }\n\n    // If we're here, then we know what our node type is, and therefore can\n    // perform our usual rendering pipeline.\n    this._updateCachedData(data.flattenedNodes);\n    this.renderNodeChanges(data.renderNodes);\n    this._updateKeyManagerItems(data.flattenedNodes);\n  }\n\n  private _emitExpansionChanges(expansionChanges: SelectionChange<K> | null) {\n    if (!expansionChanges) {\n      return;\n    }\n\n    const nodes = this._nodes.value;\n    for (const added of expansionChanges.added) {\n      const node = nodes.get(added);\n      node?._emitExpansionState(true);\n    }\n    for (const removed of expansionChanges.removed) {\n      const node = nodes.get(removed);\n      node?._emitExpansionState(false);\n    }\n  }\n\n  private _initializeKeyManager() {\n    const items = combineLatest([this._keyManagerNodes, this._nodes]).pipe(\n      map(([keyManagerNodes, renderNodes]) =>\n        keyManagerNodes.reduce<CdkTreeNode<T, K>[]>((items, data) => {\n          const node = renderNodes.get(this._getExpansionKey(data));\n          if (node) {\n            items.push(node);\n          }\n          return items;\n        }, []),\n      ),\n    );\n\n    const keyManagerOptions: TreeKeyManagerOptions<CdkTreeNode<T, K>> = {\n      trackBy: node => this._getExpansionKey(node.data),\n      skipPredicate: node => !!node.isDisabled,\n      typeAheadDebounceInterval: true,\n      horizontalOrientation: this._dir.value,\n    };\n\n    this._keyManager = this._keyManagerFactory(items, keyManagerOptions);\n  }\n\n  private _initializeDataDiffer() {\n    // Provide a default trackBy based on `_getExpansionKey` if one isn't provided.\n    const trackBy = this.trackBy ?? ((_index: number, item: T) => this._getExpansionKey(item));\n    this._dataDiffer = this._differs.find([]).create(trackBy);\n  }\n\n  private _checkTreeControlUsage() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Verify that Tree follows API contract of using one of TreeControl, levelAccessor or\n      // childrenAccessor. Throw an appropriate error if contract is not met.\n      let numTreeControls = 0;\n\n      if (this.treeControl) {\n        numTreeControls++;\n      }\n      if (this.levelAccessor) {\n        numTreeControls++;\n      }\n      if (this.childrenAccessor) {\n        numTreeControls++;\n      }\n\n      if (!numTreeControls) {\n        throw getTreeControlMissingError();\n      } else if (numTreeControls > 1) {\n        throw getMultipleTreeControlsError();\n      }\n    }\n  }\n\n  /** Check for changes made in the data and render each change (node added/removed/moved). */\n  renderNodeChanges(\n    data: readonly T[],\n    dataDiffer: IterableDiffer<T> = this._dataDiffer,\n    viewContainer: ViewContainerRef = this._nodeOutlet.viewContainer,\n    parentData?: T,\n  ) {\n    const changes = dataDiffer.diff(data);\n\n    // Some tree consumers expect change detection to propagate to nodes\n    // even when the array itself hasn't changed; we explicitly detect changes\n    // anyways in order for nodes to update their data.\n    //\n    // However, if change detection is called while the component's view is\n    // still initing, then the order of child views initing will be incorrect;\n    // to prevent this, we only exit early if the view hasn't initialized yet.\n    if (!changes && !this._viewInit) {\n      return;\n    }\n\n    changes?.forEachOperation(\n      (\n        item: IterableChangeRecord<T>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        if (item.previousIndex == null) {\n          this.insertNode(data[currentIndex!], currentIndex!, viewContainer, parentData);\n        } else if (currentIndex == null) {\n          viewContainer.remove(adjustedPreviousIndex!);\n        } else {\n          const view = viewContainer.get(adjustedPreviousIndex!);\n          viewContainer.move(view!, currentIndex);\n        }\n      },\n    );\n\n    // If the data itself changes, but keeps the same trackBy, we need to update the templates'\n    // context to reflect the new object.\n    changes?.forEachIdentityChange((record: IterableChangeRecord<T>) => {\n      const newData = record.item;\n      if (record.currentIndex != undefined) {\n        const view = viewContainer.get(record.currentIndex);\n        (view as EmbeddedViewRef<any>).context.$implicit = newData;\n      }\n    });\n\n    // Note: we only `detectChanges` from a top-level call, otherwise we risk overflowing\n    // the call stack since this method is called recursively (see #29733.)\n    // TODO: change to `this._changeDetectorRef.markForCheck()`,\n    // or just switch this component to use signals.\n    if (parentData) {\n      this._changeDetectorRef.markForCheck();\n    } else {\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n\n  /**\n   * Finds the matching node definition that should be used for this node data. If there is only\n   * one node definition, it is returned. Otherwise, find the node definition that has a when\n   * predicate that returns true with the data. If none return true, return the default node\n   * definition.\n   */\n  _getNodeDef(data: T, i: number): CdkTreeNodeDef<T> {\n    if (this._nodeDefs.length === 1) {\n      return this._nodeDefs.first!;\n    }\n\n    const nodeDef =\n      this._nodeDefs.find(def => def.when && def.when(i, data)) || this._defaultNodeDef;\n\n    if (!nodeDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTreeMissingMatchingNodeDefError();\n    }\n\n    return nodeDef!;\n  }\n\n  /**\n   * Create the embedded view for the data node template and place it in the correct index location\n   * within the data node view container.\n   */\n  insertNode(nodeData: T, index: number, viewContainer?: ViewContainerRef, parentData?: T) {\n    const levelAccessor = this._getLevelAccessor();\n\n    const node = this._getNodeDef(nodeData, index);\n    const key = this._getExpansionKey(nodeData);\n\n    // Node context that will be provided to created embedded view\n    const context = new CdkTreeNodeOutletContext<T>(nodeData);\n\n    parentData ??= this._parents.get(key) ?? undefined;\n    // If the tree is flat tree, then use the `getLevel` function in flat tree control\n    // Otherwise, use the level of parent node.\n    if (levelAccessor) {\n      context.level = levelAccessor(nodeData);\n    } else if (parentData !== undefined && this._levels.has(this._getExpansionKey(parentData))) {\n      context.level = this._levels.get(this._getExpansionKey(parentData))! + 1;\n    } else {\n      context.level = 0;\n    }\n    this._levels.set(key, context.level);\n\n    // Use default tree nodeOutlet, or nested node's nodeOutlet\n    const container = viewContainer ? viewContainer : this._nodeOutlet.viewContainer;\n    container.createEmbeddedView(node.template, context, index);\n\n    // Set the data to just created `CdkTreeNode`.\n    // The `CdkTreeNode` created from `createEmbeddedView` will be saved in static variable\n    //     `mostRecentTreeNode`. We get it from static variable and pass the node data to it.\n    if (CdkTreeNode.mostRecentTreeNode) {\n      CdkTreeNode.mostRecentTreeNode.data = nodeData;\n    }\n  }\n\n  /** Whether the data node is expanded or collapsed. Returns true if it's expanded. */\n  isExpanded(dataNode: T): boolean {\n    return !!(\n      this.treeControl?.isExpanded(dataNode) ||\n      this._expansionModel?.isSelected(this._getExpansionKey(dataNode))\n    );\n  }\n\n  /** If the data node is currently expanded, collapse it. Otherwise, expand it. */\n  toggle(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.toggle(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.toggle(this._getExpansionKey(dataNode));\n    }\n  }\n\n  /** Expand the data node. If it is already expanded, does nothing. */\n  expand(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.expand(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.select(this._getExpansionKey(dataNode));\n    }\n  }\n\n  /** Collapse the data node. If it is already collapsed, does nothing. */\n  collapse(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.collapse(dataNode);\n    } else if (this._expansionModel) {\n      this._expansionModel.deselect(this._getExpansionKey(dataNode));\n    }\n  }\n\n  /**\n   * If the data node is currently expanded, collapse it and all its descendants.\n   * Otherwise, expand it and all its descendants.\n   */\n  toggleDescendants(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.toggleDescendants(dataNode);\n    } else if (this._expansionModel) {\n      if (this.isExpanded(dataNode)) {\n        this.collapseDescendants(dataNode);\n      } else {\n        this.expandDescendants(dataNode);\n      }\n    }\n  }\n\n  /**\n   * Expand the data node and all its descendants. If they are already expanded, does nothing.\n   */\n  expandDescendants(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.expandDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.select(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode)\n        .pipe(take(1), takeUntil(this._onDestroy))\n        .subscribe(children => {\n          expansionModel.select(...children.map(child => this._getExpansionKey(child)));\n        });\n    }\n  }\n\n  /** Collapse the data node and all its descendants. If it is already collapsed, does nothing. */\n  collapseDescendants(dataNode: T): void {\n    if (this.treeControl) {\n      this.treeControl.collapseDescendants(dataNode);\n    } else if (this._expansionModel) {\n      const expansionModel = this._expansionModel;\n      expansionModel.deselect(this._getExpansionKey(dataNode));\n      this._getDescendants(dataNode)\n        .pipe(take(1), takeUntil(this._onDestroy))\n        .subscribe(children => {\n          expansionModel.deselect(...children.map(child => this._getExpansionKey(child)));\n        });\n    }\n  }\n\n  /** Expands all data nodes in the tree. */\n  expandAll(): void {\n    if (this.treeControl) {\n      this.treeControl.expandAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => this._expansionModel?.select(...keys));\n    }\n  }\n\n  /** Collapse all data nodes in the tree. */\n  collapseAll(): void {\n    if (this.treeControl) {\n      this.treeControl.collapseAll();\n    } else if (this._expansionModel) {\n      this._forEachExpansionKey(keys => this._expansionModel?.deselect(...keys));\n    }\n  }\n\n  /** Level accessor, used for compatibility between the old Tree and new Tree */\n  _getLevelAccessor() {\n    return this.treeControl?.getLevel?.bind(this.treeControl) ?? this.levelAccessor;\n  }\n\n  /** Children accessor, used for compatibility between the old Tree and new Tree */\n  _getChildrenAccessor() {\n    return this.treeControl?.getChildren?.bind(this.treeControl) ?? this.childrenAccessor;\n  }\n\n  /**\n   * Gets the direct children of a node; used for compatibility between the old tree and the\n   * new tree.\n   */\n  _getDirectChildren(dataNode: T): Observable<T[]> {\n    const levelAccessor = this._getLevelAccessor();\n    const expansionModel = this._expansionModel ?? this.treeControl?.expansionModel;\n    if (!expansionModel) {\n      return observableOf([]);\n    }\n\n    const key = this._getExpansionKey(dataNode);\n\n    const isExpanded = expansionModel.changed.pipe(\n      switchMap(changes => {\n        if (changes.added.includes(key)) {\n          return observableOf(true);\n        } else if (changes.removed.includes(key)) {\n          return observableOf(false);\n        }\n        return EMPTY;\n      }),\n      startWith(this.isExpanded(dataNode)),\n    );\n\n    if (levelAccessor) {\n      return combineLatest([isExpanded, this._flattenedNodes]).pipe(\n        map(([expanded, flattenedNodes]) => {\n          if (!expanded) {\n            return [];\n          }\n          return this._findChildrenByLevel(levelAccessor, flattenedNodes, dataNode, 1);\n        }),\n      );\n    }\n    const childrenAccessor = this._getChildrenAccessor();\n    if (childrenAccessor) {\n      return coerceObservable(childrenAccessor(dataNode) ?? []);\n    }\n    throw getTreeControlMissingError();\n  }\n\n  /**\n   * Given the list of flattened nodes, the level accessor, and the level range within\n   * which to consider children, finds the children for a given node.\n   *\n   * For example, for direct children, `levelDelta` would be 1. For all descendants,\n   * `levelDelta` would be Infinity.\n   */\n  private _findChildrenByLevel(\n    levelAccessor: (node: T) => number,\n    flattenedNodes: readonly T[],\n    dataNode: T,\n    levelDelta: number,\n  ): T[] {\n    const key = this._getExpansionKey(dataNode);\n    const startIndex = flattenedNodes.findIndex(node => this._getExpansionKey(node) === key);\n    const dataNodeLevel = levelAccessor(dataNode);\n    const expectedLevel = dataNodeLevel + levelDelta;\n    const results: T[] = [];\n\n    // Goes through flattened tree nodes in the `flattenedNodes` array, and get all\n    // descendants within a certain level range.\n    //\n    // If we reach a node whose level is equal to or less than the level of the tree node,\n    // we hit a sibling or parent's sibling, and should stop.\n    for (let i = startIndex + 1; i < flattenedNodes.length; i++) {\n      const currentLevel = levelAccessor(flattenedNodes[i]);\n      if (currentLevel <= dataNodeLevel) {\n        break;\n      }\n      if (currentLevel <= expectedLevel) {\n        results.push(flattenedNodes[i]);\n      }\n    }\n    return results;\n  }\n\n  /**\n   * Adds the specified node component to the tree's internal registry.\n   *\n   * This primarily facilitates keyboard navigation.\n   */\n  _registerNode(node: CdkTreeNode<T, K>) {\n    this._nodes.value.set(this._getExpansionKey(node.data), node);\n    this._nodes.next(this._nodes.value);\n  }\n\n  /** Removes the specified node component from the tree's internal registry. */\n  _unregisterNode(node: CdkTreeNode<T, K>) {\n    this._nodes.value.delete(this._getExpansionKey(node.data));\n    this._nodes.next(this._nodes.value);\n  }\n\n  /**\n   * For the given node, determine the level where this node appears in the tree.\n   *\n   * This is intended to be used for `aria-level` but is 0-indexed.\n   */\n  _getLevel(node: T) {\n    return this._levels.get(this._getExpansionKey(node));\n  }\n\n  /**\n   * For the given node, determine the size of the parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize(dataNode: T) {\n    const set = this._getAriaSet(dataNode);\n    return set.length;\n  }\n\n  /**\n   * For the given node, determine the index (starting from 1) of the node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet(dataNode: T) {\n    const set = this._getAriaSet(dataNode);\n    const key = this._getExpansionKey(dataNode);\n    return set.findIndex(node => this._getExpansionKey(node) === key) + 1;\n  }\n\n  /** Given a CdkTreeNode, gets the node that renders that node's parent's data. */\n  _getNodeParent(node: CdkTreeNode<T, K>) {\n    const parent = this._parents.get(this._getExpansionKey(node.data));\n    return parent && this._nodes.value.get(this._getExpansionKey(parent));\n  }\n\n  /** Given a CdkTreeNode, gets the nodes that renders that node's child data. */\n  _getNodeChildren(node: CdkTreeNode<T, K>) {\n    return this._getDirectChildren(node.data).pipe(\n      map(children =>\n        children.reduce<CdkTreeNode<T, K>[]>((nodes, child) => {\n          const value = this._nodes.value.get(this._getExpansionKey(child));\n          if (value) {\n            nodes.push(value);\n          }\n\n          return nodes;\n        }, []),\n      ),\n    );\n  }\n\n  /** `keydown` event handler; this just passes the event to the `TreeKeyManager`. */\n  protected _sendKeydownToKeyManager(event: KeyboardEvent): void {\n    // Only handle events directly on the tree or directly on one of the nodes, otherwise\n    // we risk interfering with events in the projected content (see #29828).\n    if (event.target === this._elementRef.nativeElement) {\n      this._keyManager.onKeydown(event);\n    } else {\n      const nodes = this._nodes.getValue();\n      for (const [, node] of nodes) {\n        if (event.target === node._elementRef.nativeElement) {\n          this._keyManager.onKeydown(event);\n          break;\n        }\n      }\n    }\n  }\n\n  /** Gets all nested descendants of a given node. */\n  private _getDescendants(dataNode: T): Observable<T[]> {\n    if (this.treeControl) {\n      return observableOf(this.treeControl.getDescendants(dataNode));\n    }\n    if (this.levelAccessor) {\n      const results = this._findChildrenByLevel(\n        this.levelAccessor,\n        this._flattenedNodes.value,\n        dataNode,\n        Infinity,\n      );\n      return observableOf(results);\n    }\n    if (this.childrenAccessor) {\n      return this._getAllChildrenRecursively(dataNode).pipe(\n        reduce((allChildren: T[], nextChildren) => {\n          allChildren.push(...nextChildren);\n          return allChildren;\n        }, []),\n      );\n    }\n    throw getTreeControlMissingError();\n  }\n\n  /**\n   * Gets all children and sub-children of the provided node.\n   *\n   * This will emit multiple times, in the order that the children will appear\n   * in the tree, and can be combined with a `reduce` operator.\n   */\n  private _getAllChildrenRecursively(dataNode: T): Observable<T[]> {\n    if (!this.childrenAccessor) {\n      return observableOf([]);\n    }\n\n    return coerceObservable(this.childrenAccessor(dataNode)).pipe(\n      take(1),\n      switchMap(children => {\n        // Here, we cache the parents of a particular child so that we can compute the levels.\n        for (const child of children) {\n          this._parents.set(this._getExpansionKey(child), dataNode);\n        }\n        return observableOf(...children).pipe(\n          concatMap(child => concat(observableOf([child]), this._getAllChildrenRecursively(child))),\n        );\n      }),\n    );\n  }\n\n  private _getExpansionKey(dataNode: T): K {\n    // In the case that a key accessor function was not provided by the\n    // tree user, we'll default to using the node object itself as the key.\n    //\n    // This cast is safe since:\n    // - if an expansionKey is provided, TS will infer the type of K to be\n    //   the return type.\n    // - if it's not, then K will be defaulted to T.\n    return this.expansionKey?.(dataNode) ?? (dataNode as unknown as K);\n  }\n\n  private _getAriaSet(node: T) {\n    const key = this._getExpansionKey(node);\n    const parent = this._parents.get(key);\n    const parentKey = parent ? this._getExpansionKey(parent) : null;\n    const set = this._ariaSets.get(parentKey);\n    return set ?? [node];\n  }\n\n  /**\n   * Finds the parent for the given node. If this is a root node, this\n   * returns null. If we're unable to determine the parent, for example,\n   * if we don't have cached node data, this returns undefined.\n   */\n  private _findParentForNode(node: T, index: number, cachedNodes: readonly T[]): T | null {\n    // In all cases, we have a mapping from node to level; all we need to do here is backtrack in\n    // our flattened list of nodes to determine the first node that's of a level lower than the\n    // provided node.\n    if (!cachedNodes.length) {\n      return null;\n    }\n    const currentLevel = this._levels.get(this._getExpansionKey(node)) ?? 0;\n    for (let parentIndex = index - 1; parentIndex >= 0; parentIndex--) {\n      const parentNode = cachedNodes[parentIndex];\n      const parentLevel = this._levels.get(this._getExpansionKey(parentNode)) ?? 0;\n\n      if (parentLevel < currentLevel) {\n        return parentNode;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Given a set of root nodes and the current node level, flattens any nested\n   * nodes into a single array.\n   *\n   * If any nodes are not expanded, then their children will not be added into the array.\n   * This will still traverse all nested children in order to build up our internal data\n   * models, but will not include them in the returned array.\n   */\n  private _flattenNestedNodesWithExpansion(nodes: readonly T[], level = 0): Observable<T[]> {\n    const childrenAccessor = this._getChildrenAccessor();\n    // If we're using a level accessor, we don't need to flatten anything.\n    if (!childrenAccessor) {\n      return observableOf([...nodes]);\n    }\n\n    return observableOf(...nodes).pipe(\n      concatMap(node => {\n        const parentKey = this._getExpansionKey(node);\n        if (!this._parents.has(parentKey)) {\n          this._parents.set(parentKey, null);\n        }\n        this._levels.set(parentKey, level);\n\n        const children = coerceObservable(childrenAccessor(node));\n        return concat(\n          observableOf([node]),\n          children.pipe(\n            take(1),\n            tap(childNodes => {\n              this._ariaSets.set(parentKey, [...(childNodes ?? [])]);\n              for (const child of childNodes ?? []) {\n                const childKey = this._getExpansionKey(child);\n                this._parents.set(childKey, node);\n                this._levels.set(childKey, level + 1);\n              }\n            }),\n            switchMap(childNodes => {\n              if (!childNodes) {\n                return observableOf([]);\n              }\n              return this._flattenNestedNodesWithExpansion(childNodes, level + 1).pipe(\n                map(nestedNodes => (this.isExpanded(node) ? nestedNodes : [])),\n              );\n            }),\n          ),\n        );\n      }),\n      reduce((results, children) => {\n        results.push(...children);\n        return results;\n      }, [] as T[]),\n    );\n  }\n\n  /**\n   * Converts children for certain tree configurations.\n   *\n   * This also computes parent, level, and group data.\n   */\n  private _computeRenderingData(\n    nodes: readonly T[],\n    nodeType: 'flat' | 'nested',\n  ): Observable<{\n    renderNodes: readonly T[];\n    flattenedNodes: readonly T[];\n  }> {\n    // The only situations where we have to convert children types is when\n    // they're mismatched; i.e. if the tree is using a childrenAccessor and the\n    // nodes are flat, or if the tree is using a levelAccessor and the nodes are\n    // nested.\n    if (this.childrenAccessor && nodeType === 'flat') {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // This flattens children into a single array.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(\n        map(flattenedNodes => ({\n          renderNodes: flattenedNodes,\n          flattenedNodes,\n        })),\n      );\n    } else if (this.levelAccessor && nodeType === 'nested') {\n      // In the nested case, we only look for root nodes. The CdkNestedNode\n      // itself will handle rendering each individual node's children.\n      const levelAccessor = this.levelAccessor;\n      return observableOf(nodes.filter(node => levelAccessor(node) === 0)).pipe(\n        map(rootNodes => ({\n          renderNodes: rootNodes,\n          flattenedNodes: nodes,\n        })),\n        tap(({flattenedNodes}) => {\n          this._calculateParents(flattenedNodes);\n        }),\n      );\n    } else if (nodeType === 'flat') {\n      // In the case of a TreeControl, we know that the node type matches up\n      // with the TreeControl, and so no conversions are necessary. Otherwise,\n      // we've already confirmed that the data model matches up with the\n      // desired node type here.\n      return observableOf({renderNodes: nodes, flattenedNodes: nodes}).pipe(\n        tap(({flattenedNodes}) => {\n          this._calculateParents(flattenedNodes);\n        }),\n      );\n    } else {\n      // clear previously generated data so we don't keep end up retaining data overtime causing\n      // memory leaks.\n      this._clearPreviousCache();\n      // For nested nodes, we still need to perform the node flattening in order\n      // to maintain our caches for various tree operations.\n      this._ariaSets.set(null, [...nodes]);\n      return this._flattenNestedNodesWithExpansion(nodes).pipe(\n        map(flattenedNodes => ({\n          renderNodes: nodes,\n          flattenedNodes,\n        })),\n      );\n    }\n  }\n\n  private _updateCachedData(flattenedNodes: readonly T[]) {\n    this._flattenedNodes.next(flattenedNodes);\n  }\n\n  private _updateKeyManagerItems(flattenedNodes: readonly T[]) {\n    this._keyManagerNodes.next(flattenedNodes);\n  }\n\n  /** Traverse the flattened node data and compute parents, levels, and group data. */\n  private _calculateParents(flattenedNodes: readonly T[]): void {\n    const levelAccessor = this._getLevelAccessor();\n    if (!levelAccessor) {\n      return;\n    }\n\n    // clear previously generated data so we don't keep end up retaining data overtime causing\n    // memory leaks.\n    this._clearPreviousCache();\n\n    for (let index = 0; index < flattenedNodes.length; index++) {\n      const dataNode = flattenedNodes[index];\n      const key = this._getExpansionKey(dataNode);\n      this._levels.set(key, levelAccessor(dataNode));\n      const parent = this._findParentForNode(dataNode, index, flattenedNodes);\n      this._parents.set(key, parent);\n      const parentKey = parent ? this._getExpansionKey(parent) : null;\n\n      const group = this._ariaSets.get(parentKey) ?? [];\n      group.splice(index, 0, dataNode);\n      this._ariaSets.set(parentKey, group);\n    }\n  }\n\n  /** Invokes a callback with all node expansion keys. */\n  private _forEachExpansionKey(callback: (keys: K[]) => void) {\n    const toToggle: K[] = [];\n    const observables: Observable<T[]>[] = [];\n\n    this._nodes.value.forEach(node => {\n      toToggle.push(this._getExpansionKey(node.data));\n      observables.push(this._getDescendants(node.data));\n    });\n\n    if (observables.length > 0) {\n      combineLatest(observables)\n        .pipe(take(1), takeUntil(this._onDestroy))\n        .subscribe(results => {\n          results.forEach(inner => inner.forEach(r => toToggle.push(this._getExpansionKey(r))));\n          callback(toToggle);\n        });\n    } else {\n      callback(toToggle);\n    }\n  }\n\n  /** Clears the maps we use to store parents, level & aria-sets in. */\n  private _clearPreviousCache() {\n    this._parents.clear();\n    this._levels.clear();\n    this._ariaSets.clear();\n  }\n}\n\n/**\n * Tree node for CdkTree. It contains the data in the tree node.\n */\n@Directive({\n  selector: 'cdk-tree-node',\n  exportAs: 'cdkTreeNode',\n  host: {\n    'class': 'cdk-tree-node',\n    '[attr.aria-expanded]': '_getAriaExpanded()',\n    '[attr.aria-level]': 'level + 1',\n    '[attr.aria-posinset]': '_getPositionInSet()',\n    '[attr.aria-setsize]': '_getSetSize()',\n    '[tabindex]': '_tabindex',\n    'role': 'treeitem',\n    '(click)': '_setActiveItem()',\n    '(focus)': '_focusItem()',\n  },\n})\nexport class CdkTreeNode<T, K = T> implements OnDestroy, OnInit, TreeKeyManagerItem {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _tree = inject<CdkTree<T, K>>(CdkTree);\n  protected _tabindex: number | null = -1;\n  protected readonly _type: 'flat' | 'nested' = 'flat';\n\n  /**\n   * The role of the tree node.\n   *\n   * @deprecated This will be ignored; the tree will automatically determine the appropriate role\n   * for tree node. This input will be removed in a future version.\n   * @breaking-change 21.0.0\n   */\n  @Input() get role(): 'treeitem' | 'group' {\n    return 'treeitem';\n  }\n\n  set role(_role: 'treeitem' | 'group') {\n    // ignore any role setting, we handle this internally.\n  }\n\n  /**\n   * Whether or not this node is expandable.\n   *\n   * If not using `FlatTreeControl`, or if `isExpandable` is not provided to\n   * `NestedTreeControl`, this should be provided for correct node a11y.\n   */\n  @Input({transform: booleanAttribute})\n  get isExpandable() {\n    return this._isExpandable();\n  }\n  set isExpandable(isExpandable: boolean) {\n    this._inputIsExpandable = isExpandable;\n    if ((this.data && !this._isExpandable) || !this._inputIsExpandable) {\n      return;\n    }\n    // If the node is being set to expandable, ensure that the status of the\n    // node is propagated\n    if (this._inputIsExpanded) {\n      this.expand();\n    } else if (this._inputIsExpanded === false) {\n      this.collapse();\n    }\n  }\n\n  @Input()\n  get isExpanded(): boolean {\n    return this._tree.isExpanded(this._data);\n  }\n  set isExpanded(isExpanded: boolean) {\n    this._inputIsExpanded = isExpanded;\n    if (isExpanded) {\n      this.expand();\n    } else {\n      this.collapse();\n    }\n  }\n\n  /**\n   * Whether or not this node is disabled. If it's disabled, then the user won't be able to focus\n   * or activate this node.\n   */\n  @Input({transform: booleanAttribute}) isDisabled: boolean;\n\n  /**\n   * The text used to locate this item during typeahead. If not specified, the `textContent` will\n   * will be used.\n   */\n  @Input('cdkTreeNodeTypeaheadLabel') typeaheadLabel: string | null;\n\n  getLabel(): string {\n    return this.typeaheadLabel || this._elementRef.nativeElement.textContent?.trim() || '';\n  }\n\n  /** This emits when the node has been programatically activated or activated by keyboard. */\n  @Output()\n  readonly activation: EventEmitter<T> = new EventEmitter<T>();\n\n  /** This emits when the node's expansion status has been changed. */\n  @Output()\n  readonly expandedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /**\n   * The most recently created `CdkTreeNode`. We save it in static variable so we can retrieve it\n   * in `CdkTree` and set the data to it.\n   */\n  static mostRecentTreeNode: CdkTreeNode<any> | null = null;\n\n  /** Subject that emits when the component has been destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /** Emits when the node's data has changed. */\n  readonly _dataChanges = new Subject<void>();\n\n  private _inputIsExpandable: boolean = false;\n  private _inputIsExpanded: boolean | undefined = undefined;\n  /**\n   * Flag used to determine whether or not we should be focusing the actual element based on\n   * some user interaction (click or focus). On click, we don't forcibly focus the element\n   * since the click could trigger some other component that wants to grab its own focus\n   * (e.g. menu, dialog).\n   */\n  private _shouldFocus = true;\n  private _parentNodeAriaLevel: number;\n\n  /** The tree node's data. */\n  get data(): T {\n    return this._data;\n  }\n  set data(value: T) {\n    if (value !== this._data) {\n      this._data = value;\n      this._dataChanges.next();\n    }\n  }\n  protected _data: T;\n\n  /* If leaf node, return true to not assign aria-expanded attribute */\n  get isLeafNode(): boolean {\n    // If flat tree node data returns false for expandable property, it's a leaf node\n    if (\n      this._tree.treeControl?.isExpandable !== undefined &&\n      !this._tree.treeControl.isExpandable(this._data)\n    ) {\n      return true;\n\n      // If nested tree node data returns 0 descendants, it's a leaf node\n    } else if (\n      this._tree.treeControl?.isExpandable === undefined &&\n      this._tree.treeControl?.getDescendants(this._data).length === 0\n    ) {\n      return true;\n    }\n\n    return false;\n  }\n\n  get level(): number {\n    // If the tree has a levelAccessor, use it to get the level. Otherwise read the\n    // aria-level off the parent node and use it as the level for this node (note aria-level is\n    // 1-indexed, while this property is 0-indexed, so we don't need to increment).\n    return this._tree._getLevel(this._data) ?? this._parentNodeAriaLevel;\n  }\n\n  /** Determines if the tree node is expandable. */\n  _isExpandable(): boolean {\n    if (this._tree.treeControl) {\n      if (this.isLeafNode) {\n        return false;\n      }\n\n      // For compatibility with trees created using TreeControl before we added\n      // CdkTreeNode#isExpandable.\n      return true;\n    }\n    return this._inputIsExpandable;\n  }\n\n  /**\n   * Determines the value for `aria-expanded`.\n   *\n   * For non-expandable nodes, this is `null`.\n   */\n  _getAriaExpanded(): string | null {\n    if (!this._isExpandable()) {\n      return null;\n    }\n    return String(this.isExpanded);\n  }\n\n  /**\n   * Determines the size of this node's parent's child set.\n   *\n   * This is intended to be used for `aria-setsize`.\n   */\n  _getSetSize(): number {\n    return this._tree._getSetSize(this._data);\n  }\n\n  /**\n   * Determines the index (starting from 1) of this node in its parent's child set.\n   *\n   * This is intended to be used for `aria-posinset`.\n   */\n  _getPositionInSet(): number {\n    return this._tree._getPositionInSet(this._data);\n  }\n\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    CdkTreeNode.mostRecentTreeNode = this as CdkTreeNode<T, K>;\n  }\n\n  ngOnInit(): void {\n    this._parentNodeAriaLevel = getParentNodeAriaLevel(this._elementRef.nativeElement);\n    this._tree\n      ._getExpansionModel()\n      .changed.pipe(\n        map(() => this.isExpanded),\n        distinctUntilChanged(),\n      )\n      .subscribe(() => this._changeDetectorRef.markForCheck());\n    this._tree._setNodeTypeIfUnset(this._type);\n    this._tree._registerNode(this);\n  }\n\n  ngOnDestroy() {\n    // If this is the last tree node being destroyed,\n    // clear out the reference to avoid leaking memory.\n    if (CdkTreeNode.mostRecentTreeNode === this) {\n      CdkTreeNode.mostRecentTreeNode = null;\n    }\n\n    this._dataChanges.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  getParent(): CdkTreeNode<T, K> | null {\n    return this._tree._getNodeParent(this) ?? null;\n  }\n\n  getChildren(): CdkTreeNode<T, K>[] | Observable<CdkTreeNode<T, K>[]> {\n    return this._tree._getNodeChildren(this);\n  }\n\n  /** Focuses this data node. Implemented for TreeKeyManagerItem. */\n  focus(): void {\n    this._tabindex = 0;\n    if (this._shouldFocus) {\n      this._elementRef.nativeElement.focus();\n    }\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Defocus this data node. */\n  unfocus(): void {\n    this._tabindex = -1;\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits an activation event. Implemented for TreeKeyManagerItem. */\n  activate(): void {\n    if (this.isDisabled) {\n      return;\n    }\n    this.activation.next(this._data);\n  }\n\n  /** Collapses this data node. Implemented for TreeKeyManagerItem. */\n  collapse(): void {\n    if (this.isExpandable) {\n      this._tree.collapse(this._data);\n    }\n  }\n\n  /** Expands this data node. Implemented for TreeKeyManagerItem. */\n  expand(): void {\n    if (this.isExpandable) {\n      this._tree.expand(this._data);\n    }\n  }\n\n  /** Makes the node focusable. Implemented for TreeKeyManagerItem. */\n  makeFocusable(): void {\n    this._tabindex = 0;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _focusItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._tree._keyManager.focusItem(this);\n  }\n\n  _setActiveItem() {\n    if (this.isDisabled) {\n      return;\n    }\n    this._shouldFocus = false;\n    this._tree._keyManager.focusItem(this);\n    this._shouldFocus = true;\n  }\n\n  _emitExpansionState(expanded: boolean) {\n    this.expandedChange.emit(expanded);\n  }\n}\n\nfunction getParentNodeAriaLevel(nodeElement: HTMLElement): number {\n  let parent = nodeElement.parentElement;\n  while (parent && !isNodeElement(parent)) {\n    parent = parent.parentElement;\n  }\n  if (!parent) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw Error('Incorrect tree structure containing detached node.');\n    } else {\n      return -1;\n    }\n  } else if (parent.classList.contains('cdk-nested-tree-node')) {\n    return numberAttribute(parent.getAttribute('aria-level')!);\n  } else {\n    // The ancestor element is the cdk-tree itself\n    return 0;\n  }\n}\n\nfunction isNodeElement(element: HTMLElement) {\n  const classList = element.classList;\n  return !!(classList?.contains('cdk-nested-tree-node') || classList?.contains('cdk-tree'));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AfterContentInit,\n  ContentChildren,\n  Directive,\n  IterableDiffer,\n  Iterable<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Query<PERSON>ist,\n  inject,\n} from '@angular/core';\nimport {takeUntil} from 'rxjs/operators';\n\nimport {CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodeOutlet} from './outlet';\nimport {CdkTreeNode} from './tree';\n\n/**\n * Nested node is a child of `<cdk-tree>`. It works with nested tree.\n * By using `cdk-nested-tree-node` component in tree node template, children of the parent node will\n * be added in the `cdkTreeNodeOutlet` in tree node template.\n * The children of node will be automatically added to `cdkTreeNodeOutlet`.\n */\n@Directive({\n  selector: 'cdk-nested-tree-node',\n  exportAs: 'cdkNestedTreeNode',\n  providers: [\n    {provide: CdkTreeNode, useExisting: CdkNestedTreeNode},\n    {provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: CdkNestedTreeNode},\n  ],\n  host: {\n    'class': 'cdk-nested-tree-node',\n  },\n})\nexport class CdkNestedTreeNode<T, K = T>\n  extends CdkTreeNode<T, K>\n  implements AfterContentInit, OnDestroy\n{\n  protected override _type: 'flat' | 'nested' = 'nested';\n  protected _differs = inject(IterableDiffers);\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<T>;\n\n  /** The children data dataNodes of current node. They will be placed in `CdkTreeNodeOutlet`. */\n  protected _children: T[];\n\n  /** The children node placeholder. */\n  @ContentChildren(CdkTreeNodeOutlet, {\n    // We need to use `descendants: true`, because Ivy will no longer match\n    // indirect descendants if it's left as false.\n    descendants: true,\n  })\n  nodeOutlet: QueryList<CdkTreeNodeOutlet>;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n  }\n\n  ngAfterContentInit() {\n    this._dataDiffer = this._differs.find([]).create(this._tree.trackBy);\n    this._tree\n      ._getDirectChildren(this.data)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(result => this.updateChildrenNodes(result));\n    this.nodeOutlet.changes\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this.updateChildrenNodes());\n  }\n\n  override ngOnDestroy() {\n    this._clear();\n    super.ngOnDestroy();\n  }\n\n  /** Add children dataNodes to the NodeOutlet */\n  protected updateChildrenNodes(children?: T[]): void {\n    const outlet = this._getNodeOutlet();\n    if (children) {\n      this._children = children;\n    }\n    if (outlet && this._children) {\n      const viewContainer = outlet.viewContainer;\n      this._tree.renderNodeChanges(this._children, this._dataDiffer, viewContainer, this._data);\n    } else {\n      // Reset the data differ if there's no children nodes displayed\n      this._dataDiffer.diff([]);\n    }\n  }\n\n  /** Clear the children dataNodes. */\n  protected _clear(): void {\n    const outlet = this._getNodeOutlet();\n    if (outlet) {\n      outlet.viewContainer.clear();\n      this._dataDiffer.diff([]);\n    }\n  }\n\n  /** Gets the outlet for the current node. */\n  private _getNodeOutlet() {\n    const outlets = this.nodeOutlet;\n\n    // Note that since we use `descendants: true` on the query, we have to ensure\n    // that we don't pick up the outlet of a child node by accident.\n    return outlets && outlets.find(outlet => !outlet._node || outlet._node === this);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '../bidi';\nimport {Directive, ElementRef, Input, numberAttribute, OnDestroy, inject} from '@angular/core';\nimport {takeUntil} from 'rxjs/operators';\nimport {Subject} from 'rxjs';\nimport {CdkTree, CdkTreeNode} from './tree';\n\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n\n/**\n * Indent for the children tree dataNodes.\n * This directive will add left-padding to the node to show hierarchy.\n */\n@Directive({\n  selector: '[cdkTreeNodePadding]',\n})\nexport class CdkTreeNodePadding<T, K = T> implements OnDestroy {\n  private _treeNode = inject<CdkTreeNode<T, K>>(CdkTreeNode);\n  private _tree = inject<CdkTree<T, K>>(CdkTree);\n  private _element = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dir = inject(Directionality, {optional: true});\n\n  /** Current padding value applied to the element. Used to avoid unnecessarily hitting the DOM. */\n  private _currentPadding: string | null;\n\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** CSS units used for the indentation value. */\n  indentUnits = 'px';\n\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  @Input({alias: 'cdkTreeNodePadding', transform: numberAttribute})\n  get level(): number {\n    return this._level;\n  }\n  set level(value: number) {\n    this._setLevelInput(value);\n  }\n  _level: number;\n\n  /**\n   * The indent for each level. Can be a number or a CSS string.\n   * Default number 40px from material design menu sub-menu spec.\n   */\n  @Input('cdkTreeNodePaddingIndent')\n  get indent(): number | string {\n    return this._indent;\n  }\n  set indent(indent: number | string) {\n    this._setIndentInput(indent);\n  }\n  _indent: number = 40;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._setPadding();\n    this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(() => this._setPadding(true));\n\n    // In Ivy the indentation binding might be set before the tree node's data has been added,\n    // which means that we'll miss the first render. We have to subscribe to changes in the\n    // data to ensure that everything is up to date.\n    this._treeNode._dataChanges.subscribe(() => this._setPadding());\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n\n  /** The padding indent value for the tree node. Returns a string with px numbers if not null. */\n  _paddingIndent(): string | null {\n    const nodeLevel = (this._treeNode.data && this._tree._getLevel(this._treeNode.data)) ?? null;\n    const level = this._level == null ? nodeLevel : this._level;\n    return typeof level === 'number' ? `${level * this._indent}${this.indentUnits}` : null;\n  }\n\n  _setPadding(forceChange = false) {\n    const padding = this._paddingIndent();\n\n    if (padding !== this._currentPadding || forceChange) {\n      const element = this._element.nativeElement;\n      const paddingProp = this._dir && this._dir.value === 'rtl' ? 'paddingRight' : 'paddingLeft';\n      const resetProp = paddingProp === 'paddingLeft' ? 'paddingRight' : 'paddingLeft';\n      element.style[paddingProp] = padding || '';\n      element.style[resetProp] = '';\n      this._currentPadding = padding;\n    }\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  protected _setLevelInput(value: number) {\n    // Set to null as the fallback value so that _setPadding can fall back to the node level if the\n    // consumer set the directive as `cdkTreeNodePadding=\"\"`. We still want to take this value if\n    // they set 0 explicitly.\n    this._level = isNaN(value) ? null! : value;\n    this._setPadding();\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  protected _setIndentInput(indent: number | string) {\n    let value = indent;\n    let units = 'px';\n\n    if (typeof indent === 'string') {\n      const parts = indent.split(cssUnitPattern);\n      value = parts[0];\n      units = parts[1] || units;\n    }\n\n    this.indentUnits = units;\n    this._indent = numberAttribute(value);\n    this._setPadding();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directive, Input, booleanAttribute, inject} from '@angular/core';\n\nimport {CdkTree, CdkTreeNode} from './tree';\n\n/**\n * Node toggle to expand and collapse the node.\n */\n@Directive({\n  selector: '[cdkTreeNodeToggle]',\n  host: {\n    '(click)': '_toggle(); $event.stopPropagation();',\n    '(keydown.Enter)': '_toggle(); $event.preventDefault();',\n    '(keydown.Space)': '_toggle(); $event.preventDefault();',\n    'tabindex': '-1',\n  },\n})\nexport class CdkTreeNodeToggle<T, K = T> {\n  protected _tree = inject<CdkTree<T, K>>(CdkTree);\n  protected _treeNode = inject<CdkTreeNode<T, K>>(CdkTreeNode);\n\n  /** Whether expand/collapse the node recursively. */\n  @Input({alias: 'cdkTreeNodeToggleRecursive', transform: booleanAttribute})\n  recursive: boolean = false;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  // Toggle the expanded or collapsed state of this node.\n  //\n  // Focus this node with expanding or collapsing it. This ensures that the active node will always\n  // be visible when expanding and collapsing.\n  _toggle(): void {\n    this.recursive\n      ? this._tree.toggleDescendants(this._treeNode.data)\n      : this._tree.toggle(this._treeNode.data);\n\n    this._tree._keyManager.focusItem(this._treeNode);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkTreeNodeOutlet} from './outlet';\nimport {CdkTreeNodePadding} from './padding';\nimport {CdkTreeNodeToggle} from './toggle';\nimport {CdkTree, CdkTreeNode} from './tree';\nimport {CdkTreeNodeDef} from './node';\nimport {CdkNestedTreeNode} from './nested-node';\n\nconst EXPORTED_DECLARATIONS = [\n  CdkNestedTreeNode,\n  CdkTreeNodeDef,\n  CdkTreeNodePadding,\n  CdkTreeNodeToggle,\n  CdkTree,\n  CdkTreeNode,\n  CdkTreeNodeOutlet,\n];\n\n@NgModule({\n  imports: EXPORTED_DECLARATIONS,\n  exports: EXPORTED_DECLARATIONS,\n})\nexport class CdkTreeModule {}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;AAWA;;;;;AAKG;MACmB,eAAe,CAAA;;AAQnC,IAAA,SAAS;;AAGT,IAAA,cAAc,GAAsB,IAAI,cAAc,CAAI,IAAI,CAAC;AAE/D;;;;;AAKG;AACH,IAAA,OAAO;;AAGP,IAAA,QAAQ;AAER;;;AAGG;AACH,IAAA,YAAY;;AAGZ,IAAA,WAAW;;AAGX,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;AAI1D,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;AAI1D,IAAA,QAAQ,CAAC,QAAW,EAAA;AAClB,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;AAI5D,IAAA,UAAU,CAAC,QAAW,EAAA;AACpB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;;;AAIrE,IAAA,iBAAiB,CAAC,QAAW,EAAA;QAC3B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;AACzD,cAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ;AACnC,cAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;;;IAItC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;;AAI7B,IAAA,iBAAiB,CAAC,QAAW,EAAA;AAC3B,QAAA,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC;QAC9B,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;;AAItF,IAAA,mBAAmB,CAAC,QAAW,EAAA;AAC7B,QAAA,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC;QAC9B,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;AAG9E,IAAA,aAAa,CAAC,KAAY,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAU,CAAC,GAAI,KAAW;;AAEhE;;ACpFD;;;;;;AAMG;AACG,MAAO,eAA0B,SAAQ,eAAqB,CAAA;AAGhD,IAAA,QAAA;AACA,IAAA,YAAA;AACT,IAAA,OAAA;;AAHT,IAAA,WAAA,CACkB,QAAiC,EACjC,YAAsC,EAC/C,OAAsC,EAAA;AAE7C,QAAA,KAAK,EAAE;QAJS,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAY,CAAA,YAAA,GAAZ,YAAY;QACrB,IAAO,CAAA,OAAA,GAAP,OAAO;AAId,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;;;AAIvC;;;;;AAKG;AACH,IAAA,cAAc,CAAC,QAAW,EAAA;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;QACnD,MAAM,OAAO,GAAQ,EAAE;;;;;;;AAQvB,QAAA,KACE,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EACtB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACvF,CAAC,EAAE,EACH;YACA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;AAEjC,QAAA,OAAO,OAAO;;AAGhB;;;;;AAKG;IACH,SAAS,GAAA;QACP,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEtF;;ACrDD;;;;;;AAMG;AACG,MAAO,iBAA4B,SAAQ,eAAqB,CAAA;AAGlD,IAAA,WAAA;AACT,IAAA,OAAA;;IAFT,WACkB,CAAA,WAAsE,EAC/E,OAAwC,EAAA;AAE/C,QAAA,KAAK,EAAE;QAHS,IAAW,CAAA,WAAA,GAAX,WAAW;QACpB,IAAO,CAAA,OAAA,GAAP,OAAO;AAId,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;;AAGrC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE;YAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;;;AAIjD;;;;;AAKG;IACH,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACpC,CAAC,WAAgB,EAAE,QAAQ,KAAK,CAAC,GAAG,WAAW,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAC5F,EAAE,CACH;QACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;;;AAI/E,IAAA,cAAc,CAAC,QAAW,EAAA;QACxB,MAAM,WAAW,GAAQ,EAAE;AAE3B,QAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC;;AAE3C,QAAA,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;;;IAIpB,eAAe,CAAC,WAAgB,EAAE,QAAW,EAAA;AACrD,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AAChD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAChC,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,KAAQ,KAAK,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;;AACxE,aAAA,IAAI,YAAY,CAAC,aAAa,CAAC,EAAE;;;AAGtC,YAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;AACjF,gBAAA,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;AAC5B,oBAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC;;AAE5C,aAAC,CAAC;;;AAGP;;ACzED;;;;AAIG;MACU,yBAAyB,GAAG,IAAI,cAAc,CAAK,2BAA2B;AAE3F;;;AAGG;MAIU,iBAAiB,CAAA;AAC5B,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACxC,KAAK,GAAI,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAG5D,IAAA,WAAA,GAAA;uGALW,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAH7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAChC,iBAAA;;;ACZD;MACa,wBAAwB,CAAA;;AAEnC,IAAA,SAAS;;AAGT,IAAA,KAAK;;AAGL,IAAA,KAAK;;AAGL,IAAA,KAAK;AAEL,IAAA,WAAA,CAAY,IAAO,EAAA;AACjB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAExB;AAED;;;AAGG;MAKU,cAAc,CAAA;;AAEzB,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AAEhD;;;;;;AAMG;AACH,IAAA,IAAI;AAGJ,IAAA,WAAA,GAAA;uGAdW,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAC,CAAC;AACtD,iBAAA;;;AC5BD;;;AAGG;SACa,6BAA6B,GAAA;AAC3C,IAAA,OAAO,KAAK,CAAC,CAAuC,qCAAA,CAAA,CAAC;AACvD;AAEA;;;AAGG;SACa,mCAAmC,GAAA;AACjD,IAAA,OAAO,KAAK,CAAC,CAAsE,oEAAA,CAAA,CAAC;AACtF;AAEA;;;AAGG;SACa,kCAAkC,GAAA;AAChD,IAAA,OAAO,KAAK,CAAC,CAAuE,qEAAA,CAAA,CAAC;AACvF;AAEA;;;AAGG;SACa,0BAA0B,GAAA;AACxC,IAAA,OAAO,KAAK,CAAC,CAAiF,+EAAA,CAAA,CAAC;AACjG;AAEA;;;;AAIG;SACa,4BAA4B,GAAA;AAC1C,IAAA,OAAO,KAAK,CAAC,CAAkF,gFAAA,CAAA,CAAC;AAClG;;ACiDA;;;AAGG;MAkBU,OAAO,CAAA;AASV,IAAA,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;AAClC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AAEhC,IAAA,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC;;AAGpB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAGzC,IAAA,WAAW;;AAGX,IAAA,eAAe;;AAGf,IAAA,iBAAiB;;AAGjB,IAAA,OAAO,GAAmB,IAAI,GAAG,EAAa;;AAG9C,IAAA,QAAQ,GAAqB,IAAI,GAAG,EAAe;AAE3D;;;;;;;AAOG;AACK,IAAA,SAAS,GAAuB,IAAI,GAAG,EAAiB;AAEhE;;;;AAIG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,UAAiD,EAAA;AAC9D,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;AACnC,YAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;;;AAG9B,IAAA,WAAW;AAEnB;;;;;;AAMG;AACM,IAAA,WAAW;AAEpB;;;;;AAKG;AACM,IAAA,aAAa;AAEtB;;;;;AAKG;AACM,IAAA,gBAAgB;AAEzB;;;;;AAKG;AACM,IAAA,OAAO;AAEhB;;AAEG;AACM,IAAA,YAAY;;AAGyB,IAAA,WAAW;;AAQzD,IAAA,SAAS;;;AAIT;;;AAGG;IACM,UAAU,GAAG,IAAI,eAAe,CAA+B;AACtE,QAAA,KAAK,EAAE,CAAC;QACR,GAAG,EAAE,MAAM,CAAC,SAAS;AACtB,KAAA,CAAC;;AAGM,IAAA,eAAe;AAEvB;;;;AAIG;AACK,IAAA,eAAe,GAAkC,IAAI,eAAe,CAAe,EAAE,CAAC;;AAGtF,IAAA,SAAS,GAA8C,IAAI,eAAe,CAEhF,IAAI,CAAC;;IAGC,MAAM,GAA+C,IAAI,eAAe,CAC9E,IAAI,GAAG,EAAwB,CAChC;AAED;;;;AAIG;AACK,IAAA,gBAAgB,GAAkC,IAAI,eAAe,CAAe,EAAE,CAAC;AAEvF,IAAA,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAA6C;;AAGjG,IAAA,WAAW;IACH,SAAS,GAAG,KAAK;AAGzB,IAAA,WAAA,GAAA;IAEA,kBAAkB,GAAA;QAChB,IAAI,CAAC,qBAAqB,EAAE;;IAG9B,qBAAqB,GAAA;QACnB,IAAI,CAAC,4BAA4B,EAAE;QACnC,IAAI,CAAC,uBAAuB,EAAE;;IAGhC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;AAEtC,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAE1B,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,OAAQ,IAAI,CAAC,WAA6B,CAAC,UAAU,KAAK,UAAU,EAAE;AAC3F,YAAA,IAAI,CAAC,UAA4B,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGrD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;AACpC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;;AAK/B,QAAA,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;;IAG7B,QAAQ,GAAA;QACN,IAAI,CAAC,sBAAsB,EAAE;QAC7B,IAAI,CAAC,qBAAqB,EAAE;;IAG9B,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;IAGf,4BAA4B,GAAA;AAClC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;AAC/D,QAAA,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACjF,MAAM,mCAAmC,EAAE;;AAE7C,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC;;AAG3C;;;;;AAKG;AACH,IAAA,mBAAmB,CAAC,OAA0B,EAAA;AAC5C,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK;AAExC,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;;AACvB,aAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,WAAW,KAAK,OAAO,EAAE;YACrF,OAAO,CAAC,IAAI,CACV,CAA4E,0EAAA,CAAA;gBAC1E,CAA0E,wEAAA,CAAA;AAC1E,gBAAA,CAAA,oBAAA,EAAuB,WAAW,CAAA,kBAAA,EAAqB,OAAO,CAAA,EAAA,CAAI,CACrE;;;AAIL;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,UAAiD,EAAA;AACzE,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,OAAQ,IAAI,CAAC,WAA6B,CAAC,UAAU,KAAK,UAAU,EAAE;AAC3F,YAAA,IAAI,CAAC,UAA4B,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGrD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;AACpC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;QAI/B,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;AAGxC,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;AAC7B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,uBAAuB,EAAE;;;IAIlC,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,eAAe,KAAK,IAAI,cAAc,CAAI,IAAI,CAAC;YACpD,OAAO,IAAI,CAAC,eAAe;;AAE7B,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc;;;IAIhC,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B;;AAGF,QAAA,IAAI,UAAgD;AAEpD,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YAClC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;;AACtC,aAAA,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AACzC,YAAA,UAAU,GAAG,IAAI,CAAC,WAAW;;aACxB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC1C,YAAA,UAAU,GAAGA,EAAY,CAAC,IAAI,CAAC,WAAW,CAAC;;QAG7C,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;gBACjD,MAAM,6BAA6B,EAAE;;YAEvC;;QAGF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU;AACpD,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,aAAa,IAAG;AACzB,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;AACxC,SAAC,CAAC;;;AAIE,IAAA,cAAc,CAAC,UAAoC,EAAA;AACzD,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE;AAChD,QAAA,OAAO,aAAa,CAAC;YACnB,UAAU;AACV,YAAA,IAAI,CAAC,SAAS;;;AAGd,YAAA,cAAc,CAAC,OAAO,CAAC,IAAI,CACzB,SAAS,CAAC,IAAI,CAAC,EACf,GAAG,CAAC,gBAAgB,IAAG;AACrB,gBAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;AAC9C,aAAC,CAAC,CACH;AACF,SAAA,CAAC,CAAC,IAAI,CACL,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAI;AAC7B,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;AACrB,gBAAA,OAAOA,EAAY,CAAC,EAAC,WAAW,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAU,CAAC;;;;YAKnF,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CACpD,GAAG,CAAC,aAAa,KAAK,EAAC,GAAG,aAAa,EAAE,QAAQ,EAAC,CAAU,CAAC,CAC9D;SACF,CAAC,CACH;;AAGK,IAAA,kBAAkB,CAAC,IAAsB,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC1B,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;YACxC;;;;AAKF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;AACxC,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC;;AAG1C,IAAA,qBAAqB,CAAC,gBAA2C,EAAA;QACvE,IAAI,CAAC,gBAAgB,EAAE;YACrB;;AAGF,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;AAC/B,QAAA,KAAK,MAAM,KAAK,IAAI,gBAAgB,CAAC,KAAK,EAAE;YAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC;;AAEjC,QAAA,KAAK,MAAM,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE;YAC9C,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/B,YAAA,IAAI,EAAE,mBAAmB,CAAC,KAAK,CAAC;;;IAI5B,qBAAqB,GAAA;AAC3B,QAAA,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CACpE,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,KACjC,eAAe,CAAC,MAAM,CAAsB,CAAC,KAAK,EAAE,IAAI,KAAI;AAC1D,YAAA,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,IAAI,EAAE;AACR,gBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAElB,YAAA,OAAO,KAAK;AACd,SAAC,EAAE,EAAE,CAAC,CACP,CACF;AAED,QAAA,MAAM,iBAAiB,GAA6C;AAClE,YAAA,OAAO,EAAE,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;YACjD,aAAa,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU;AACxC,YAAA,yBAAyB,EAAE,IAAI;AAC/B,YAAA,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;SACvC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,CAAC;;IAG9D,qBAAqB,GAAA;;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC,MAAc,EAAE,IAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC1F,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;;IAGnD,sBAAsB,GAAA;AAC5B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;YAGjD,IAAI,eAAe,GAAG,CAAC;AAEvB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,eAAe,EAAE;;AAEnB,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,eAAe,EAAE;;AAEnB,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,gBAAA,eAAe,EAAE;;YAGnB,IAAI,CAAC,eAAe,EAAE;gBACpB,MAAM,0BAA0B,EAAE;;AAC7B,iBAAA,IAAI,eAAe,GAAG,CAAC,EAAE;gBAC9B,MAAM,4BAA4B,EAAE;;;;;AAM1C,IAAA,iBAAiB,CACf,IAAkB,EAClB,UAAA,GAAgC,IAAI,CAAC,WAAW,EAChD,aAAA,GAAkC,IAAI,CAAC,WAAW,CAAC,aAAa,EAChE,UAAc,EAAA;QAEd,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;QASrC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC/B;;QAGF,OAAO,EAAE,gBAAgB,CACvB,CACE,IAA6B,EAC7B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;AAC9B,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAa,CAAC,EAAE,YAAa,EAAE,aAAa,EAAE,UAAU,CAAC;;AACzE,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,aAAa,CAAC,MAAM,CAAC,qBAAsB,CAAC;;iBACvC;gBACL,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,qBAAsB,CAAC;AACtD,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAK,EAAE,YAAY,CAAC;;AAE3C,SAAC,CACF;;;AAID,QAAA,OAAO,EAAE,qBAAqB,CAAC,CAAC,MAA+B,KAAI;AACjE,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI;AAC3B,YAAA,IAAI,MAAM,CAAC,YAAY,IAAI,SAAS,EAAE;gBACpC,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;AAClD,gBAAA,IAA6B,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO;;AAE9D,SAAC,CAAC;;;;;QAMF,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;aACjC;AACL,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;;;AAI3C;;;;;AAKG;IACH,WAAW,CAAC,IAAO,EAAE,CAAS,EAAA;QAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,KAAM;;AAG9B,QAAA,MAAM,OAAO,GACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe;AAEnF,QAAA,IAAI,CAAC,OAAO,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC/D,MAAM,kCAAkC,EAAE;;AAG5C,QAAA,OAAO,OAAQ;;AAGjB;;;AAGG;AACH,IAAA,UAAU,CAAC,QAAW,EAAE,KAAa,EAAE,aAAgC,EAAE,UAAc,EAAA;AACrF,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE;QAE9C,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;AAG3C,QAAA,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAAI,QAAQ,CAAC;QAEzD,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,SAAS;;;QAGlD,IAAI,aAAa,EAAE;AACjB,YAAA,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC;;AAClC,aAAA,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,EAAE;AAC1F,YAAA,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAE,GAAG,CAAC;;aACnE;AACL,YAAA,OAAO,CAAC,KAAK,GAAG,CAAC;;QAEnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC;;AAGpC,QAAA,MAAM,SAAS,GAAG,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAChF,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;;;;AAK3D,QAAA,IAAI,WAAW,CAAC,kBAAkB,EAAE;AAClC,YAAA,WAAW,CAAC,kBAAkB,CAAC,IAAI,GAAG,QAAQ;;;;AAKlD,IAAA,UAAU,CAAC,QAAW,EAAA;QACpB,OAAO,CAAC,EACN,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC;AACtC,YAAA,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAClE;;;AAIH,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAC5B,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;;;AAKhE,IAAA,MAAM,CAAC,QAAW,EAAA;AAChB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;;AAC5B,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;;;AAKhE,IAAA,QAAQ,CAAC,QAAW,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC;;AAC9B,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;;AAIlE;;;AAGG;AACH,IAAA,iBAAiB,CAAC,QAAW,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC;;AACvC,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC7B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;;iBAC7B;AACL,gBAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;;;;AAKtC;;AAEG;AACH,IAAA,iBAAiB,CAAC,QAAW,EAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC;;AACvC,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;YAC3C,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACtD,YAAA,IAAI,CAAC,eAAe,CAAC,QAAQ;AAC1B,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;iBACxC,SAAS,CAAC,QAAQ,IAAG;gBACpB,cAAc,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E,aAAC,CAAC;;;;AAKR,IAAA,mBAAmB,CAAC,QAAW,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,CAAC;;AACzC,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;YAC3C,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACxD,YAAA,IAAI,CAAC,eAAe,CAAC,QAAQ;AAC1B,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;iBACxC,SAAS,CAAC,QAAQ,IAAG;gBACpB,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF,aAAC,CAAC;;;;IAKR,SAAS,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;;AACvB,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;;;;IAK5E,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;;AACzB,aAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC/B,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;;;;IAK9E,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,aAAa;;;IAIjF,oBAAoB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,gBAAgB;;AAGvF;;;AAGG;AACH,IAAA,kBAAkB,CAAC,QAAW,EAAA;AAC5B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,EAAE,cAAc;QAC/E,IAAI,CAAC,cAAc,EAAE;AACnB,YAAA,OAAOA,EAAY,CAAC,EAAE,CAAC;;QAGzB,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAE3C,QAAA,MAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC5C,SAAS,CAAC,OAAO,IAAG;YAClB,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/B,gBAAA,OAAOA,EAAY,CAAC,IAAI,CAAC;;iBACpB,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxC,gBAAA,OAAOA,EAAY,CAAC,KAAK,CAAC;;AAE5B,YAAA,OAAO,KAAK;AACd,SAAC,CAAC,EACF,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CACrC;QAED,IAAI,aAAa,EAAE;YACjB,OAAO,aAAa,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAC3D,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,cAAc,CAAC,KAAI;gBACjC,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,OAAO,EAAE;;AAEX,gBAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,CAAC;aAC7E,CAAC,CACH;;AAEH,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE;QACpD,IAAI,gBAAgB,EAAE;YACpB,OAAO,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;QAE3D,MAAM,0BAA0B,EAAE;;AAGpC;;;;;;AAMG;AACK,IAAA,oBAAoB,CAC1B,aAAkC,EAClC,cAA4B,EAC5B,QAAW,EACX,UAAkB,EAAA;QAElB,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAC3C,QAAA,MAAM,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;AACxF,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC7C,QAAA,MAAM,aAAa,GAAG,aAAa,GAAG,UAAU;QAChD,MAAM,OAAO,GAAQ,EAAE;;;;;;AAOvB,QAAA,KAAK,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3D,MAAM,YAAY,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACrD,YAAA,IAAI,YAAY,IAAI,aAAa,EAAE;gBACjC;;AAEF,YAAA,IAAI,YAAY,IAAI,aAAa,EAAE;gBACjC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;;;AAGnC,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;AACH,IAAA,aAAa,CAAC,IAAuB,EAAA;AACnC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;;AAIrC,IAAA,eAAe,CAAC,IAAuB,EAAA;AACrC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;AAGrC;;;;AAIG;AACH,IAAA,SAAS,CAAC,IAAO,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;;AAGtD;;;;AAIG;AACH,IAAA,WAAW,CAAC,QAAW,EAAA;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;QACtC,OAAO,GAAG,CAAC,MAAM;;AAGnB;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,QAAW,EAAA;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAC3C,QAAA,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC;;;AAIvE,IAAA,cAAc,CAAC,IAAuB,EAAA;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClE,QAAA,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;;;AAIvE,IAAA,gBAAgB,CAAC,IAAuB,EAAA;QACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAC5C,GAAG,CAAC,QAAQ,IACV,QAAQ,CAAC,MAAM,CAAsB,CAAC,KAAK,EAAE,KAAK,KAAI;AACpD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,KAAK,EAAE;AACT,gBAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGnB,YAAA,OAAO,KAAK;AACd,SAAC,EAAE,EAAE,CAAC,CACP,CACF;;;AAIO,IAAA,wBAAwB,CAAC,KAAoB,EAAA;;;QAGrD,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;AACnD,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;;aAC5B;YACL,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACpC,KAAK,MAAM,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE;gBAC5B,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;AACnD,oBAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC;oBACjC;;;;;;AAOA,IAAA,eAAe,CAAC,QAAW,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAOA,EAAY,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;;AAEhE,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CACvC,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,eAAe,CAAC,KAAK,EAC1B,QAAQ,EACR,QAAQ,CACT;AACD,YAAA,OAAOA,EAAY,CAAC,OAAO,CAAC;;AAE9B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC,IAAI,CACnD,MAAM,CAAC,CAAC,WAAgB,EAAE,YAAY,KAAI;AACxC,gBAAA,WAAW,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;AACjC,gBAAA,OAAO,WAAW;AACpB,aAAC,EAAE,EAAE,CAAC,CACP;;QAEH,MAAM,0BAA0B,EAAE;;AAGpC;;;;;AAKG;AACK,IAAA,0BAA0B,CAAC,QAAW,EAAA;AAC5C,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC1B,YAAA,OAAOA,EAAY,CAAC,EAAE,CAAC;;QAGzB,OAAO,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAC3D,IAAI,CAAC,CAAC,CAAC,EACP,SAAS,CAAC,QAAQ,IAAG;;AAEnB,YAAA,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC;;AAE3D,YAAA,OAAOA,EAAY,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CACnC,SAAS,CAAC,KAAK,IAAI,MAAM,CAACA,EAAY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1F;SACF,CAAC,CACH;;AAGK,IAAA,gBAAgB,CAAC,QAAW,EAAA;;;;;;;;QAQlC,OAAO,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,IAAK,QAAyB;;AAG5D,IAAA,WAAW,CAAC,IAAO,EAAA;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;AACrC,QAAA,MAAM,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,IAAI;QAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;AACzC,QAAA,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;;AAGtB;;;;AAIG;AACK,IAAA,kBAAkB,CAAC,IAAO,EAAE,KAAa,EAAE,WAAyB,EAAA;;;;AAI1E,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AACvB,YAAA,OAAO,IAAI;;AAEb,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACvE,QAAA,KAAK,IAAI,WAAW,GAAG,KAAK,GAAG,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE;AACjE,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,CAAC;AAC3C,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAE5E,YAAA,IAAI,WAAW,GAAG,YAAY,EAAE;AAC9B,gBAAA,OAAO,UAAU;;;AAGrB,QAAA,OAAO,IAAI;;AAGb;;;;;;;AAOG;AACK,IAAA,gCAAgC,CAAC,KAAmB,EAAE,KAAK,GAAG,CAAC,EAAA;AACrE,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE;;QAEpD,IAAI,CAAC,gBAAgB,EAAE;AACrB,YAAA,OAAOA,EAAY,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;;AAGjC,QAAA,OAAOA,EAAY,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAChC,SAAS,CAAC,IAAI,IAAG;YACf,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;;YAEpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;YAElC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzD,OAAO,MAAM,CACXA,EAAY,CAAC,CAAC,IAAI,CAAC,CAAC,EACpB,QAAQ,CAAC,IAAI,CACX,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,UAAU,IAAG;AACf,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;AACtD,gBAAA,KAAK,MAAM,KAAK,IAAI,UAAU,IAAI,EAAE,EAAE;oBACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBAC7C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;oBACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC;;AAEzC,aAAC,CAAC,EACF,SAAS,CAAC,UAAU,IAAG;gBACrB,IAAI,CAAC,UAAU,EAAE;AACf,oBAAA,OAAOA,EAAY,CAAC,EAAE,CAAC;;AAEzB,gBAAA,OAAO,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CACtE,GAAG,CAAC,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC,CAC/D;aACF,CAAC,CACH,CACF;SACF,CAAC,EACF,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,KAAI;AAC3B,YAAA,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;AACzB,YAAA,OAAO,OAAO;AAChB,SAAC,EAAE,EAAS,CAAC,CACd;;AAGH;;;;AAIG;IACK,qBAAqB,CAC3B,KAAmB,EACnB,QAA2B,EAAA;;;;;QAS3B,IAAI,IAAI,CAAC,gBAAgB,IAAI,QAAQ,KAAK,MAAM,EAAE;;;YAGhD,IAAI,CAAC,mBAAmB,EAAE;;AAE1B,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AACpC,YAAA,OAAO,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC,IAAI,CACtD,GAAG,CAAC,cAAc,KAAK;AACrB,gBAAA,WAAW,EAAE,cAAc;gBAC3B,cAAc;aACf,CAAC,CAAC,CACJ;;aACI,IAAI,IAAI,CAAC,aAAa,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;AAGtD,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa;YACxC,OAAOA,EAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CACvE,GAAG,CAAC,SAAS,KAAK;AAChB,gBAAA,WAAW,EAAE,SAAS;AACtB,gBAAA,cAAc,EAAE,KAAK;aACtB,CAAC,CAAC,EACH,GAAG,CAAC,CAAC,EAAC,cAAc,EAAC,KAAI;AACvB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;aACvC,CAAC,CACH;;AACI,aAAA,IAAI,QAAQ,KAAK,MAAM,EAAE;;;;;YAK9B,OAAOA,EAAY,CAAC,EAAC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAC,CAAC,CAAC,IAAI,CACnE,GAAG,CAAC,CAAC,EAAC,cAAc,EAAC,KAAI;AACvB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;aACvC,CAAC,CACH;;aACI;;;YAGL,IAAI,CAAC,mBAAmB,EAAE;;;AAG1B,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AACpC,YAAA,OAAO,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC,IAAI,CACtD,GAAG,CAAC,cAAc,KAAK;AACrB,gBAAA,WAAW,EAAE,KAAK;gBAClB,cAAc;aACf,CAAC,CAAC,CACJ;;;AAIG,IAAA,iBAAiB,CAAC,cAA4B,EAAA;AACpD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGnC,IAAA,sBAAsB,CAAC,cAA4B,EAAA;AACzD,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC;;;AAIpC,IAAA,iBAAiB,CAAC,cAA4B,EAAA;AACpD,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE;QAC9C,IAAI,CAAC,aAAa,EAAE;YAClB;;;;QAKF,IAAI,CAAC,mBAAmB,EAAE;AAE1B,QAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAC1D,YAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;YACtC,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAC3C,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC9C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,EAAE,cAAc,CAAC;YACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC;AAC9B,YAAA,MAAM,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,IAAI;AAE/D,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;YACjD,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;;;;AAKhC,IAAA,oBAAoB,CAAC,QAA6B,EAAA;QACxD,MAAM,QAAQ,GAAQ,EAAE;QACxB,MAAM,WAAW,GAAsB,EAAE;QAEzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AAC/B,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,YAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,SAAC,CAAC;AAEF,QAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,aAAa,CAAC,WAAW;AACtB,iBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;iBACxC,SAAS,CAAC,OAAO,IAAG;AACnB,gBAAA,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrF,QAAQ,CAAC,QAAQ,CAAC;AACpB,aAAC,CAAC;;aACC;YACL,QAAQ,CAAC,QAAQ,CAAC;;;;IAKd,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACrB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;;uGApiCb,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,4ZAqGD,cAAc,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAHpB,iBAAiB,EAhHlB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,+CAAA,CAAiD,4DAYjD,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEhB,OAAO,EAAA,UAAA,EAAA,CAAA;kBAjBnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,CAAiD,+CAAA,CAAA;AAC3D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,MAAM,EAAE,MAAM;AACd,wBAAA,WAAW,EAAE,kCAAkC;AAChD,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;;;;;oBAKrC,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,OAAO,EAAE,CAAC,iBAAiB,CAAC;AAC7B,iBAAA;wDAkDK,UAAU,EAAA,CAAA;sBADb;gBAkBQ,WAAW,EAAA,CAAA;sBAAnB;gBAQQ,aAAa,EAAA,CAAA;sBAArB;gBAQQ,gBAAgB,EAAA,CAAA;sBAAxB;gBAQQ,OAAO,EAAA,CAAA;sBAAf;gBAKQ,YAAY,EAAA,CAAA;sBAApB;gBAG6C,WAAW,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBAQ5C,SAAS,EAAA,CAAA;sBALR,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,cAAc,EAAE;;;AAG/B,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;;AA+7BH;;AAEG;MAgBU,WAAW,CAAA;AACtB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC/C,IAAA,KAAK,GAAG,MAAM,CAAgB,OAAO,CAAC;IACtC,SAAS,GAAkB,CAAC,CAAC;IACpB,KAAK,GAAsB,MAAM;AAEpD;;;;;;AAMG;AACH,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,UAAU;;IAGnB,IAAI,IAAI,CAAC,KAA2B,EAAA;;;AAIpC;;;;;AAKG;AACH,IAAA,IACI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;IAE7B,IAAI,YAAY,CAAC,YAAqB,EAAA;AACpC,QAAA,IAAI,CAAC,kBAAkB,GAAG,YAAY;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAClE;;;;AAIF,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,MAAM,EAAE;;AACR,aAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE;YAC1C,IAAI,CAAC,QAAQ,EAAE;;;AAInB,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;;IAE1C,IAAI,UAAU,CAAC,UAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU;QAClC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,MAAM,EAAE;;aACR;YACL,IAAI,CAAC,QAAQ,EAAE;;;AAInB;;;AAGG;AACmC,IAAA,UAAU;AAEhD;;;AAGG;AACiC,IAAA,cAAc;IAElD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;;;AAK/E,IAAA,UAAU,GAAoB,IAAI,YAAY,EAAK;;AAInD,IAAA,cAAc,GAA0B,IAAI,YAAY,EAAW;AAE5E;;;AAGG;AACH,IAAA,OAAO,kBAAkB,GAA4B,IAAI;;AAGtC,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAG1C,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;IAEnC,kBAAkB,GAAY,KAAK;IACnC,gBAAgB,GAAwB,SAAS;AACzD;;;;;AAKG;IACK,YAAY,GAAG,IAAI;AACnB,IAAA,oBAAoB;;AAG5B,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,KAAQ,EAAA;AACf,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACxB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;;AAGlB,IAAA,KAAK;;AAGf,IAAA,IAAI,UAAU,GAAA;;QAEZ,IACE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,KAAK,SAAS;AAClD,YAAA,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAChD;AACA,YAAA,OAAO,IAAI;;;aAGN,IACL,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,KAAK,SAAS;AAClD,YAAA,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAC/D;AACA,YAAA,OAAO,IAAI;;AAGb,QAAA,OAAO,KAAK;;AAGd,IAAA,IAAI,KAAK,GAAA;;;;AAIP,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB;;;IAItE,aAAa,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;AAC1B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,OAAO,KAAK;;;;AAKd,YAAA,OAAO,IAAI;;QAEb,OAAO,IAAI,CAAC,kBAAkB;;AAGhC;;;;AAIG;IACH,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;AACzB,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;AAGhC;;;;AAIG;IACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG3C;;;;AAIG;IACH,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGzC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAItD,IAAA,WAAA,GAAA;AACE,QAAA,WAAW,CAAC,kBAAkB,GAAG,IAAyB;;IAG5D,QAAQ,GAAA;QACN,IAAI,CAAC,oBAAoB,GAAG,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAClF,QAAA,IAAI,CAAC;AACF,aAAA,kBAAkB;AAClB,aAAA,OAAO,CAAC,IAAI,CACX,GAAG,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,EAC1B,oBAAoB,EAAE;aAEvB,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;;IAGhC,WAAW,GAAA;;;AAGT,QAAA,IAAI,WAAW,CAAC,kBAAkB,KAAK,IAAI,EAAE;AAC3C,YAAA,WAAW,CAAC,kBAAkB,GAAG,IAAI;;AAGvC,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;IAG5B,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI;;IAGhD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC;;;IAI1C,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC;AAClB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;;AAGxC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AAEnB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAIxC,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB;;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAIlC,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;;;IAKnC,MAAM,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;;;;IAKjC,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC;AAClB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAGxC,UAAU,GAAA;AACR,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB;;QAEF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC;;IAGxC,cAAc,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB;;AAEF,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;QACzB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;AAG1B,IAAA,mBAAmB,CAAC,QAAiB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;;uGAnSzB,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EA2BH,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAmChB,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,2BAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,UAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,cAAA,EAAA,EAAA,UAAA,EAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,eAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA9DxB,WAAW,EAAA,UAAA,EAAA,CAAA;kBAfvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACxB,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,mBAAmB,EAAE,WAAW;AAChC,wBAAA,sBAAsB,EAAE,qBAAqB;AAC7C,wBAAA,qBAAqB,EAAE,eAAe;AACtC,wBAAA,YAAY,EAAE,WAAW;AACzB,wBAAA,MAAM,EAAE,UAAU;AAClB,wBAAA,SAAS,EAAE,kBAAkB;AAC7B,wBAAA,SAAS,EAAE,cAAc;AAC1B,qBAAA;AACF,iBAAA;wDAcc,IAAI,EAAA,CAAA;sBAAhB;gBAeG,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAmBhC,UAAU,EAAA,CAAA;sBADb;gBAiBqC,UAAU,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAMA,cAAc,EAAA,CAAA;sBAAjD,KAAK;uBAAC,2BAA2B;gBAQzB,UAAU,EAAA,CAAA;sBADlB;gBAKQ,cAAc,EAAA,CAAA;sBADtB;;AAwNH,SAAS,sBAAsB,CAAC,WAAwB,EAAA;AACtD,IAAA,IAAI,MAAM,GAAG,WAAW,CAAC,aAAa;IACtC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AACvC,QAAA,MAAM,GAAG,MAAM,CAAC,aAAa;;IAE/B,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,KAAK,CAAC,oDAAoD,CAAC;;aAC5D;YACL,OAAO,CAAC,CAAC;;;SAEN,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;QAC5D,OAAO,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAE,CAAC;;SACrD;;AAEL,QAAA,OAAO,CAAC;;AAEZ;AAEA,SAAS,aAAa,CAAC,OAAoB,EAAA;AACzC,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS;AACnC,IAAA,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,sBAAsB,CAAC,IAAI,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC3F;;ACt9CA;;;;;AAKG;AAYG,MAAO,iBACX,SAAQ,WAAiB,CAAA;IAGN,KAAK,GAAsB,QAAQ;AAC5C,IAAA,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;;AAGpC,IAAA,WAAW;;AAGT,IAAA,SAAS;;AAQnB,IAAA,UAAU;AAIV,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;;IAGT,kBAAkB,GAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACpE,QAAA,IAAI,CAAC;AACF,aAAA,kBAAkB,CAAC,IAAI,CAAC,IAAI;AAC5B,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AAC/B,aAAA,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU,CAAC;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;;IAGvC,WAAW,GAAA;QAClB,IAAI,CAAC,MAAM,EAAE;QACb,KAAK,CAAC,WAAW,EAAE;;;AAIX,IAAA,mBAAmB,CAAC,QAAc,EAAA;AAC1C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;QACpC,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;AAE3B,QAAA,IAAI,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa;AAC1C,YAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC;;aACpF;;AAEL,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;;;IAKnB,MAAM,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;QACpC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;;;IAKrB,cAAc,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU;;;QAI/B,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;;uGAzEvE,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EARjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACrE,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAmBgB,iBAAiB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAdvB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAX7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,mBAAmB,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,mBAAmB,EAAC;AACrE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAChC,qBAAA;AACF,iBAAA;wDAoBC,UAAU,EAAA,CAAA;sBALT,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE;;;AAGlC,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;;;AC3CH;AACA,MAAM,cAAc,GAAG,eAAe;AAEtC;;;AAGG;MAIU,kBAAkB,CAAA;AACrB,IAAA,SAAS,GAAG,MAAM,CAAoB,WAAW,CAAC;AAClD,IAAA,KAAK,GAAG,MAAM,CAAgB,OAAO,CAAC;AACtC,IAAA,QAAQ,GAAG,MAAM,CAA0B,UAAU,CAAC;IACtD,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG/C,IAAA,eAAe;;AAGN,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;IAGjD,WAAW,GAAG,IAAI;;AAGlB,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAa,EAAA;AACrB,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;AAE5B,IAAA,MAAM;AAEN;;;AAGG;AACH,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,MAAuB,EAAA;AAChC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;IAE9B,OAAO,GAAW,EAAE;AAIpB,IAAA,WAAA,GAAA;QACE,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;;;AAK1F,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;;IAGjE,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;IAI5B,cAAc,GAAA;QACZ,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI;AAC5F,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM;QAC3D,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAG,EAAA,KAAK,GAAG,IAAI,CAAC,OAAO,CAAA,EAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI;;IAGxF,WAAW,CAAC,WAAW,GAAG,KAAK,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE;QAErC,IAAI,OAAO,KAAK,IAAI,CAAC,eAAe,IAAI,WAAW,EAAE;AACnD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa;YAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,cAAc,GAAG,aAAa;AAC3F,YAAA,MAAM,SAAS,GAAG,WAAW,KAAK,aAAa,GAAG,cAAc,GAAG,aAAa;YAChF,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,OAAO,IAAI,EAAE;AAC1C,YAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;AAC7B,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO;;;AAIlC;;;;;AAKG;AACO,IAAA,cAAc,CAAC,KAAa,EAAA;;;;AAIpC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAK,GAAG,KAAK;QAC1C,IAAI,CAAC,WAAW,EAAE;;AAGpB;;;;;AAKG;AACO,IAAA,eAAe,CAAC,MAAuB,EAAA;QAC/C,IAAI,KAAK,GAAG,MAAM;QAClB,IAAI,KAAK,GAAG,IAAI;AAEhB,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC;AAC1C,YAAA,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AAChB,YAAA,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK;;AAG3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE;;uGA3GT,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,yGAgBmB,eAAe,CAAA,EAAA,MAAA,EAAA,CAAA,0BAAA,EAAA,QAAA,CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAhBpD,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAH9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AACjC,iBAAA;wDAkBK,KAAK,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,eAAe,EAAC;gBAc5D,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,0BAA0B;;;ACzCnC;;AAEG;MAUU,iBAAiB,CAAA;AAClB,IAAA,KAAK,GAAG,MAAM,CAAgB,OAAO,CAAC;AACtC,IAAA,SAAS,GAAG,MAAM,CAAoB,WAAW,CAAC;;IAI5D,SAAS,GAAY,KAAK;AAG1B,IAAA,WAAA,GAAA;;;;;IAMA,OAAO,GAAA;AACL,QAAA,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;AAClD,cAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAE1C,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;;uGApBvC,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,wHAK4B,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,sCAAA,EAAA,eAAA,EAAA,qCAAA,EAAA,eAAA,EAAA,qCAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAL7D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAT7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,sCAAsC;AACjD,wBAAA,iBAAiB,EAAE,qCAAqC;AACxD,wBAAA,iBAAiB,EAAE,qCAAqC;AACxD,wBAAA,UAAU,EAAE,IAAI;AACjB,qBAAA;AACF,iBAAA;wDAOC,SAAS,EAAA,CAAA;sBADR,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;ACb3E,MAAM,qBAAqB,GAAG;IAC5B,iBAAiB;IACjB,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,OAAO;IACP,WAAW;IACX,iBAAiB;CAClB;MAMY,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAbxB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;AACX,YAAA,iBAAiB,aANjB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,CAAA;wGAON,aAAa,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,qBAAqB;AAC9B,oBAAA,OAAO,EAAE,qBAAqB;AAC/B,iBAAA;;;;;"}