{"version": 3, "file": "selenium-webdriver.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/selenium-webdriver/selenium-webdriver-keys.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/selenium-webdriver/selenium-web-driver-element.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/testing/selenium-webdriver/selenium-web-driver-harness-environment.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Modifier<PERSON><PERSON><PERSON>, TestKey} from '../../testing';\nimport * as webdriver from 'selenium-webdriver';\n\n/**\n * Maps the `TestKey` constants to WebDriver's `webdriver.Key` constants.\n * See https://github.com/SeleniumHQ/selenium/blob/trunk/javascript/webdriver/key.js#L29\n */\nexport const seleniumWebDriverKeyMap = {\n  [TestKey.BACKSPACE]: webdriver.Key.BACK_SPACE,\n  [TestKey.TAB]: webdriver.Key.TAB,\n  [TestKey.ENTER]: webdriver.Key.ENTER,\n  [TestKey.SHIFT]: webdriver.Key.SHIFT,\n  [TestKey.CONTROL]: webdriver.Key.CONTROL,\n  [TestKey.ALT]: webdriver.Key.ALT,\n  [TestKey.ESCAPE]: webdriver.Key.ESCAPE,\n  [TestKey.PAGE_UP]: webdriver.Key.PAGE_UP,\n  [TestKey.PAGE_DOWN]: webdriver.Key.PAGE_DOWN,\n  [TestKey.END]: webdriver.Key.END,\n  [TestKey.HOME]: webdriver.Key.HOME,\n  [TestKey.LEFT_ARROW]: webdriver.Key.ARROW_LEFT,\n  [TestKey.UP_ARROW]: webdriver.Key.ARROW_UP,\n  [TestKey.RIGHT_ARROW]: webdriver.Key.ARROW_RIGHT,\n  [TestKey.DOWN_ARROW]: webdriver.Key.ARROW_DOWN,\n  [TestKey.INSERT]: webdriver.Key.INSERT,\n  [TestKey.DELETE]: webdriver.Key.DELETE,\n  [TestKey.F1]: webdriver.Key.F1,\n  [TestKey.F2]: webdriver.Key.F2,\n  [TestKey.F3]: webdriver.Key.F3,\n  [TestKey.F4]: webdriver.Key.F4,\n  [TestKey.F5]: webdriver.Key.F5,\n  [TestKey.F6]: webdriver.Key.F6,\n  [TestKey.F7]: webdriver.Key.F7,\n  [TestKey.F8]: webdriver.Key.F8,\n  [TestKey.F9]: webdriver.Key.F9,\n  [TestKey.F10]: webdriver.Key.F10,\n  [TestKey.F11]: webdriver.Key.F11,\n  [TestKey.F12]: webdriver.Key.F12,\n  [TestKey.META]: webdriver.Key.META,\n  [TestKey.COMMA]: ',',\n};\n\n/** Gets a list of WebDriver `Key`s for the given `ModifierKeys`. */\nexport function getSeleniumWebDriverModifierKeys(modifiers: ModifierKeys): string[] {\n  const result: string[] = [];\n  if (modifiers.control) {\n    result.push(webdriver.Key.CONTROL);\n  }\n  if (modifiers.alt) {\n    result.push(webdriver.Key.ALT);\n  }\n  if (modifiers.shift) {\n    result.push(webdriver.Key.SHIFT);\n  }\n  if (modifiers.meta) {\n    result.push(webdriver.Key.META);\n  }\n  return result;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport * as webdriver from 'selenium-webdriver';\nimport {ElementDimensions} from '../element-dimensions';\nimport {EventData, ModifierKeys, TestElement, TestKey, TextOptions} from '../test-element';\nimport {getNoKeysSpecifiedError} from '../test-element-errors';\nimport {_getTextWithExcludedElements} from '../text-filtering';\nimport {getSeleniumWebDriverModifierKeys, seleniumWebDriverKeyMap} from './selenium-webdriver-keys';\n\n/** A `TestElement` implementation for WebDriver. */\nexport class SeleniumWebDriverElement implements TestElement {\n  constructor(\n    readonly element: () => webdriver.WebElement,\n    private _stabilize: () => Promise<void>,\n  ) {}\n\n  /** Blur the element. */\n  async blur(): Promise<void> {\n    await this._executeScript((element: HTMLElement) => element.blur(), this.element());\n    await this._stabilize();\n  }\n\n  /** Clear the element's input (for input and textarea elements only). */\n  async clear(): Promise<void> {\n    await this.element().clear();\n    await this._stabilize();\n  }\n\n  /**\n   * Click the element at the default location for the current environment. If you need to guarantee\n   * the element is clicked at a specific location, consider using `click('center')` or\n   * `click(x, y)` instead.\n   */\n  click(modifiers?: ModifierKeys): Promise<void>;\n  /** Click the element at the element's center. */\n  click(location: 'center', modifiers?: ModifierKeys): Promise<void>;\n  /**\n   * Click the element at the specified coordinates relative to the top-left of the element.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  click(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n  async click(\n    ...args: [ModifierKeys?] | ['center', ModifierKeys?] | [number, number, ModifierKeys?]\n  ): Promise<void> {\n    await this._dispatchClickEventSequence(args, webdriver.Button.LEFT);\n    await this._stabilize();\n  }\n\n  /**\n   * Right clicks on the element at the specified coordinates relative to the top-left of it.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */\n  rightClick(relativeX: number, relativeY: number, modifiers?: ModifierKeys): Promise<void>;\n  async rightClick(\n    ...args: [ModifierKeys?] | ['center', ModifierKeys?] | [number, number, ModifierKeys?]\n  ): Promise<void> {\n    await this._dispatchClickEventSequence(args, webdriver.Button.RIGHT);\n    await this._stabilize();\n  }\n\n  /** Focus the element. */\n  async focus(): Promise<void> {\n    await this._executeScript((element: HTMLElement) => element.focus(), this.element());\n    await this._stabilize();\n  }\n\n  /** Get the computed value of the given CSS property for the element. */\n  async getCssValue(property: string): Promise<string> {\n    await this._stabilize();\n    return this.element().getCssValue(property);\n  }\n\n  /** Hovers the mouse over the element. */\n  async hover(): Promise<void> {\n    await this._actions().mouseMove(this.element()).perform();\n    await this._stabilize();\n  }\n\n  /** Moves the mouse away from the element. */\n  async mouseAway(): Promise<void> {\n    await this._actions().mouseMove(this.element(), {x: -1, y: -1}).perform();\n    await this._stabilize();\n  }\n\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value.\n   */\n  async sendKeys(...keys: (string | TestKey)[]): Promise<void>;\n  /**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value.\n   */\n  async sendKeys(modifiers: ModifierKeys, ...keys: (string | TestKey)[]): Promise<void>;\n  async sendKeys(...modifiersAndKeys: any[]): Promise<void> {\n    const first = modifiersAndKeys[0];\n    let modifiers: ModifierKeys;\n    let rest: (string | TestKey)[];\n    if (first !== undefined && typeof first !== 'string' && typeof first !== 'number') {\n      modifiers = first;\n      rest = modifiersAndKeys.slice(1);\n    } else {\n      modifiers = {};\n      rest = modifiersAndKeys;\n    }\n\n    const modifierKeys = getSeleniumWebDriverModifierKeys(modifiers);\n    const keys = rest\n      .map(k => (typeof k === 'string' ? k.split('') : [seleniumWebDriverKeyMap[k]]))\n      .reduce((arr, k) => arr.concat(k), [])\n      // webdriver.Key.chord doesn't work well with geckodriver (mozilla/geckodriver#1502),\n      // so avoid it if no modifier keys are required.\n      .map(k => (modifierKeys.length > 0 ? webdriver.Key.chord(...modifierKeys, k) : k));\n\n    // Throw an error if no keys have been specified. Calling this function with no\n    // keys should not result in a focus event being dispatched unexpectedly.\n    if (keys.length === 0) {\n      throw getNoKeysSpecifiedError();\n    }\n\n    await this.element().sendKeys(...keys);\n    await this._stabilize();\n  }\n\n  /**\n   * Gets the text from the element.\n   * @param options Options that affect what text is included.\n   */\n  async text(options?: TextOptions): Promise<string> {\n    await this._stabilize();\n    if (options?.exclude) {\n      return this._executeScript(_getTextWithExcludedElements, this.element(), options.exclude);\n    }\n    // We don't go through the WebDriver `getText`, because it excludes text from hidden elements.\n    return this._executeScript(\n      (element: Element) => (element.textContent || '').trim(),\n      this.element(),\n    );\n  }\n\n  /**\n   * Sets the value of a `contenteditable` element.\n   * @param value Value to be set on the element.\n   */\n  async setContenteditableValue(value: string): Promise<void> {\n    const contenteditableAttr = await this.getAttribute('contenteditable');\n\n    if (\n      contenteditableAttr !== '' &&\n      contenteditableAttr !== 'true' &&\n      contenteditableAttr !== 'plaintext-only'\n    ) {\n      throw new Error('setContenteditableValue can only be called on a `contenteditable` element.');\n    }\n\n    await this._stabilize();\n    return this._executeScript(\n      (element: Element, valueToSet: string) => (element.textContent = valueToSet),\n      this.element(),\n      value,\n    );\n  }\n\n  /** Gets the value for the given attribute from the element. */\n  async getAttribute(name: string): Promise<string | null> {\n    await this._stabilize();\n    return this._executeScript(\n      (element: Element, attribute: string) => element.getAttribute(attribute),\n      this.element(),\n      name,\n    );\n  }\n\n  /** Checks whether the element has the given class. */\n  async hasClass(name: string): Promise<boolean> {\n    await this._stabilize();\n    const classes = (await this.getAttribute('class')) || '';\n    return new Set(classes.split(/\\s+/).filter(c => c)).has(name);\n  }\n\n  /** Gets the dimensions of the element. */\n  async getDimensions(): Promise<ElementDimensions> {\n    await this._stabilize();\n    const {width, height} = await this.element().getSize();\n    const {x: left, y: top} = await this.element().getLocation();\n    return {width, height, left, top};\n  }\n\n  /** Gets the value of a property of an element. */\n  async getProperty<T = any>(name: string): Promise<T> {\n    await this._stabilize();\n    return this._executeScript(\n      (element: Element, property: keyof Element) => element[property],\n      this.element(),\n      name,\n    );\n  }\n\n  /** Sets the value of a property of an input. */\n  async setInputValue(newValue: string): Promise<void> {\n    await this._executeScript(\n      (element: HTMLInputElement, value: string) => (element.value = value),\n      this.element(),\n      newValue,\n    );\n    await this._stabilize();\n  }\n\n  /** Selects the options at the specified indexes inside of a native `select` element. */\n  async selectOptions(...optionIndexes: number[]): Promise<void> {\n    await this._stabilize();\n    const options = await this.element().findElements(webdriver.By.css('option'));\n    const indexes = new Set(optionIndexes); // Convert to a set to remove duplicates.\n\n    if (options.length && indexes.size) {\n      // Reset the value so all the selected states are cleared. We can\n      // reuse the input-specific method since the logic is the same.\n      await this.setInputValue('');\n\n      for (let i = 0; i < options.length; i++) {\n        if (indexes.has(i)) {\n          // We have to hold the control key while clicking on options so that multiple can be\n          // selected in multi-selection mode. The key doesn't do anything for single selection.\n          await this._actions().keyDown(webdriver.Key.CONTROL).perform();\n          await options[i].click();\n          await this._actions().keyUp(webdriver.Key.CONTROL).perform();\n        }\n      }\n\n      await this._stabilize();\n    }\n  }\n\n  /** Checks whether this element matches the given selector. */\n  async matchesSelector(selector: string): Promise<boolean> {\n    await this._stabilize();\n    return this._executeScript(\n      (element: Element, s: string) =>\n        (Element.prototype.matches || (Element.prototype as any).msMatchesSelector).call(\n          element,\n          s,\n        ),\n      this.element(),\n      selector,\n    );\n  }\n\n  /** Checks whether the element is focused. */\n  async isFocused(): Promise<boolean> {\n    await this._stabilize();\n    return webdriver.WebElement.equals(\n      this.element(),\n      this.element().getDriver().switchTo().activeElement(),\n    );\n  }\n\n  /**\n   * Dispatches an event with a particular name.\n   * @param name Name of the event to be dispatched.\n   */\n  async dispatchEvent(name: string, data?: Record<string, EventData>): Promise<void> {\n    await this._executeScript(dispatchEvent, name, this.element(), data);\n    await this._stabilize();\n  }\n\n  /** Gets the webdriver action sequence. */\n  private _actions() {\n    return this.element().getDriver().actions();\n  }\n\n  /** Executes a function in the browser. */\n  private async _executeScript<T>(script: Function, ...var_args: any[]): Promise<T> {\n    return this.element()\n      .getDriver()\n      .executeScript(script, ...var_args);\n  }\n\n  /** Dispatches all the events that are part of a click event sequence. */\n  private async _dispatchClickEventSequence(\n    args: [ModifierKeys?] | ['center', ModifierKeys?] | [number, number, ModifierKeys?],\n    button: string,\n  ) {\n    let modifiers: ModifierKeys = {};\n    if (args.length && typeof args[args.length - 1] === 'object') {\n      modifiers = args.pop() as ModifierKeys;\n    }\n    const modifierKeys = getSeleniumWebDriverModifierKeys(modifiers);\n\n    // Omitting the offset argument to mouseMove results in clicking the center.\n    // This is the default behavior we want, so we use an empty array of offsetArgs if\n    // no args remain after popping the modifiers from the args passed to this function.\n    const offsetArgs = (args.length === 2 ? [{x: args[0], y: args[1]}] : []) as [\n      {x: number; y: number},\n    ];\n\n    let actions = this._actions().mouseMove(this.element(), ...offsetArgs);\n\n    for (const modifierKey of modifierKeys) {\n      actions = actions.keyDown(modifierKey);\n    }\n    actions = actions.click(button);\n    for (const modifierKey of modifierKeys) {\n      actions = actions.keyUp(modifierKey);\n    }\n\n    await actions.perform();\n  }\n}\n\n/**\n * Dispatches an event with a particular name and data to an element. Note that this needs to be a\n * pure function, because it gets stringified by WebDriver and is executed inside the browser.\n */\nfunction dispatchEvent(name: string, element: Element, data?: Record<string, EventData>) {\n  const event = document.createEvent('Event');\n  event.initEvent(name);\n  // tslint:disable-next-line:ban Have to use `Object.assign` to preserve the original object.\n  Object.assign(event, data || {});\n  element.dispatchEvent(event);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessEnvironment, HarnessLoader, TestElement} from '../../testing';\nimport * as webdriver from 'selenium-webdriver';\nimport {SeleniumWebDriverElement} from './selenium-web-driver-element';\n\n/**\n * An Angular framework stabilizer function that takes a callback and calls it when the application\n * is stable, passing a boolean indicating if any work was done.\n */\ndeclare interface FrameworkStabilizer {\n  (callback: (didWork: boolean) => void): void;\n}\n\ndeclare global {\n  interface Window {\n    /**\n     * These hooks are exposed by <PERSON><PERSON> to register a callback for when the application is stable\n     * (no more pending tasks).\n     *\n     * For the implementation, see: https://github.com/\n     *  angular/angular/blob/main/packages/platform-browser/src/browser/testability.ts#L30-L49\n     */\n    frameworkStabilizers: FrameworkStabilizer[];\n  }\n}\n\n/** Options to configure the environment. */\nexport interface WebDriverHarnessEnvironmentOptions {\n  /** The query function used to find DOM elements. */\n  queryFn: (selector: string, root: () => webdriver.WebElement) => Promise<webdriver.WebElement[]>;\n}\n\n/** The default environment options. */\nconst defaultEnvironmentOptions: WebDriverHarnessEnvironmentOptions = {\n  queryFn: async (selector: string, root: () => webdriver.WebElement) =>\n    root().findElements(webdriver.By.css(selector)),\n};\n\n/**\n * This function is meant to be executed in the browser. It taps into the hooks exposed by Angular\n * and invokes the specified `callback` when the application is stable (no more pending tasks).\n */\nfunction whenStable(callback: (didWork: boolean[]) => void): void {\n  Promise.all(window.frameworkStabilizers.map(stabilizer => new Promise(stabilizer))).then(\n    callback,\n  );\n}\n\n/**\n * This function is meant to be executed in the browser. It checks whether the Angular framework has\n * bootstrapped yet.\n */\nfunction isBootstrapped() {\n  return !!window.frameworkStabilizers;\n}\n\n/** Waits for angular to be ready after the page load. */\nexport async function waitForAngularReady(wd: webdriver.WebDriver) {\n  await wd.wait(() => wd.executeScript(isBootstrapped));\n  await wd.executeAsyncScript(whenStable);\n}\n\n/** A `HarnessEnvironment` implementation for WebDriver. */\nexport class SeleniumWebDriverHarnessEnvironment extends HarnessEnvironment<\n  () => webdriver.WebElement\n> {\n  /** The options for this environment. */\n  private _options: WebDriverHarnessEnvironmentOptions;\n\n  /** Environment stabilization callback passed to the created test elements. */\n  private _stabilizeCallback: () => Promise<void>;\n\n  protected constructor(\n    rawRootElement: () => webdriver.WebElement,\n    options?: WebDriverHarnessEnvironmentOptions,\n  ) {\n    super(rawRootElement);\n    this._options = {...defaultEnvironmentOptions, ...options};\n    this._stabilizeCallback = () => this.forceStabilize();\n  }\n\n  /** Gets the ElementFinder corresponding to the given TestElement. */\n  static getNativeElement(el: TestElement): webdriver.WebElement {\n    if (el instanceof SeleniumWebDriverElement) {\n      return el.element();\n    }\n    throw Error('This TestElement was not created by the WebDriverHarnessEnvironment');\n  }\n\n  /** Creates a `HarnessLoader` rooted at the document root. */\n  static loader(\n    driver: webdriver.WebDriver,\n    options?: WebDriverHarnessEnvironmentOptions,\n  ): HarnessLoader {\n    return new SeleniumWebDriverHarnessEnvironment(\n      () => driver.findElement(webdriver.By.css('body')),\n      options,\n    );\n  }\n\n  /**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */\n  async forceStabilize(): Promise<void> {\n    await this.rawRootElement().getDriver().executeAsyncScript(whenStable);\n  }\n\n  /** @docs-private */\n  async waitForTasksOutsideAngular(): Promise<void> {\n    // TODO: figure out how we can do this for the webdriver environment.\n    //  https://github.com/angular/components/issues/17412\n  }\n\n  /** Gets the root element for the document. */\n  protected getDocumentRoot(): () => webdriver.WebElement {\n    return () => this.rawRootElement().getDriver().findElement(webdriver.By.css('body'));\n  }\n\n  /** Creates a `TestElement` from a raw element. */\n  protected createTestElement(element: () => webdriver.WebElement): TestElement {\n    return new SeleniumWebDriverElement(element, this._stabilizeCallback);\n  }\n\n  /** Creates a `HarnessLoader` rooted at the given raw element. */\n  protected createEnvironment(\n    element: () => webdriver.WebElement,\n  ): HarnessEnvironment<() => webdriver.WebElement> {\n    return new SeleniumWebDriverHarnessEnvironment(element, this._options);\n  }\n\n  // Note: This seems to be working, though we may need to re-evaluate if we encounter issues with\n  // stale element references. `() => Promise<webdriver.WebElement[]>` seems like a more correct\n  // return type, though supporting it would require changes to the public harness API.\n  /**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */\n  protected async getAllRawElements(selector: string): Promise<(() => webdriver.WebElement)[]> {\n    const els = await this._options.queryFn(selector, this.rawRootElement);\n    return els.map((x: webdriver.WebElement) => () => x);\n  }\n}\n"], "names": [], "mappings": ";;;;AAWA;;;AAGG;AACI,MAAM,uBAAuB,GAAG;IACrC,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU;IAC7C,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK;IACpC,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK;IACpC,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO;IACxC,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM;IACtC,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO;IACxC,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS;IAC5C,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI;IAClC,CAAC,OAAO,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU;IAC9C,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ;IAC1C,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,WAAW;IAChD,CAAC,OAAO,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU;IAC9C,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM;IACtC,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM;IACtC,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;IAChC,CAAC,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI;AAClC,IAAA,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG;CACrB;AAED;AACM,SAAU,gCAAgC,CAAC,SAAuB,EAAA;IACtE,MAAM,MAAM,GAAa,EAAE;AAC3B,IAAA,IAAI,SAAS,CAAC,OAAO,EAAE;QACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;;AAEpC,IAAA,IAAI,SAAS,CAAC,GAAG,EAAE;QACjB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC;;AAEhC,IAAA,IAAI,SAAS,CAAC,KAAK,EAAE;QACnB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;AAElC,IAAA,IAAI,SAAS,CAAC,IAAI,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;;AAEjC,IAAA,OAAO,MAAM;AACf;;AClDA;MACa,wBAAwB,CAAA;AAExB,IAAA,OAAA;AACD,IAAA,UAAA;IAFV,WACW,CAAA,OAAmC,EACpC,UAA+B,EAAA;QAD9B,IAAO,CAAA,OAAA,GAAP,OAAO;QACR,IAAU,CAAA,UAAA,GAAV,UAAU;;;AAIpB,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,OAAoB,KAAK,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACnF,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;AAIzB,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE;AAC5B,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;AAkBzB,IAAA,MAAM,KAAK,CACT,GAAG,IAAmF,EAAA;AAEtF,QAAA,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AACnE,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;AAUzB,IAAA,MAAM,UAAU,CACd,GAAG,IAAmF,EAAA;AAEtF,QAAA,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;AACpE,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;AAIzB,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,OAAoB,KAAK,OAAO,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACpF,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;IAIzB,MAAM,WAAW,CAAC,QAAgB,EAAA;AAChC,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;;;AAI7C,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE;AACzD,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;AAIzB,IAAA,MAAM,SAAS,GAAA;QACb,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,EAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAE;AACzE,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;AAazB,IAAA,MAAM,QAAQ,CAAC,GAAG,gBAAuB,EAAA;AACvC,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC;AACjC,QAAA,IAAI,SAAuB;AAC3B,QAAA,IAAI,IAA0B;AAC9B,QAAA,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACjF,SAAS,GAAG,KAAK;AACjB,YAAA,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;;aAC3B;YACL,SAAS,GAAG,EAAE;YACd,IAAI,GAAG,gBAAgB;;AAGzB,QAAA,MAAM,YAAY,GAAG,gCAAgC,CAAC,SAAS,CAAC;QAChE,MAAM,IAAI,GAAG;AACV,aAAA,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,aAAA,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;;;AAGpC,aAAA,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;;AAIpF,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,MAAM,uBAAuB,EAAE;;QAGjC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AACtC,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;AAGzB;;;AAGG;IACH,MAAM,IAAI,CAAC,OAAqB,EAAA;AAC9B,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAA,IAAI,OAAO,EAAE,OAAO,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,4BAA4B,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC;;;QAG3F,OAAO,IAAI,CAAC,cAAc,CACxB,CAAC,OAAgB,KAAK,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE,EACxD,IAAI,CAAC,OAAO,EAAE,CACf;;AAGH;;;AAGG;IACH,MAAM,uBAAuB,CAAC,KAAa,EAAA;QACzC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;QAEtE,IACE,mBAAmB,KAAK,EAAE;AAC1B,YAAA,mBAAmB,KAAK,MAAM;YAC9B,mBAAmB,KAAK,gBAAgB,EACxC;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC;;AAG/F,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC,cAAc,CACxB,CAAC,OAAgB,EAAE,UAAkB,MAAM,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,EAC5E,IAAI,CAAC,OAAO,EAAE,EACd,KAAK,CACN;;;IAIH,MAAM,YAAY,CAAC,IAAY,EAAA;AAC7B,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC,cAAc,CACxB,CAAC,OAAgB,EAAE,SAAiB,KAAK,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EACxE,IAAI,CAAC,OAAO,EAAE,EACd,IAAI,CACL;;;IAIH,MAAM,QAAQ,CAAC,IAAY,EAAA;AACzB,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE;QACxD,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;;;AAI/D,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAA,MAAM,EAAC,KAAK,EAAE,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE;AACtD,QAAA,MAAM,EAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE;QAC5D,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAC;;;IAInC,MAAM,WAAW,CAAU,IAAY,EAAA;AACrC,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC,cAAc,CACxB,CAAC,OAAgB,EAAE,QAAuB,KAAK,OAAO,CAAC,QAAQ,CAAC,EAChE,IAAI,CAAC,OAAO,EAAE,EACd,IAAI,CACL;;;IAIH,MAAM,aAAa,CAAC,QAAgB,EAAA;QAClC,MAAM,IAAI,CAAC,cAAc,CACvB,CAAC,OAAyB,EAAE,KAAa,MAAM,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,EACrE,IAAI,CAAC,OAAO,EAAE,EACd,QAAQ,CACT;AACD,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;AAIzB,IAAA,MAAM,aAAa,CAAC,GAAG,aAAuB,EAAA;AAC5C,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7E,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;QAEvC,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE;;;AAGlC,YAAA,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;AAE5B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,gBAAA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;;;AAGlB,oBAAA,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;AAC9D,oBAAA,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;AACxB,oBAAA,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;;;AAIhE,YAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;;IAK3B,MAAM,eAAe,CAAC,QAAgB,EAAA;AACpC,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC,cAAc,CACxB,CAAC,OAAgB,EAAE,CAAS,KAC1B,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,IAAK,OAAO,CAAC,SAAiB,CAAC,iBAAiB,EAAE,IAAI,CAC9E,OAAO,EACP,CAAC,CACF,EACH,IAAI,CAAC,OAAO,EAAE,EACd,QAAQ,CACT;;;AAIH,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;QACvB,OAAO,SAAS,CAAC,UAAU,CAAC,MAAM,CAChC,IAAI,CAAC,OAAO,EAAE,EACd,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,aAAa,EAAE,CACtD;;AAGH;;;AAGG;AACH,IAAA,MAAM,aAAa,CAAC,IAAY,EAAE,IAAgC,EAAA;AAChE,QAAA,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;AACpE,QAAA,MAAM,IAAI,CAAC,UAAU,EAAE;;;IAIjB,QAAQ,GAAA;QACd,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE;;;AAIrC,IAAA,MAAM,cAAc,CAAI,MAAgB,EAAE,GAAG,QAAe,EAAA;QAClE,OAAO,IAAI,CAAC,OAAO;AAChB,aAAA,SAAS;AACT,aAAA,aAAa,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC;;;AAI/B,IAAA,MAAM,2BAA2B,CACvC,IAAmF,EACnF,MAAc,EAAA;QAEd,IAAI,SAAS,GAAiB,EAAE;AAChC,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC5D,YAAA,SAAS,GAAG,IAAI,CAAC,GAAG,EAAkB;;AAExC,QAAA,MAAM,YAAY,GAAG,gCAAgC,CAAC,SAAS,CAAC;;;;AAKhE,QAAA,MAAM,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAE,CAEtE;AAED,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC;AAEtE,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AACtC,YAAA,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;;AAExC,QAAA,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/B,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AACtC,YAAA,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;;AAGtC,QAAA,MAAM,OAAO,CAAC,OAAO,EAAE;;AAE1B;AAED;;;AAGG;AACH,SAAS,aAAa,CAAC,IAAY,EAAE,OAAgB,EAAE,IAAgC,EAAA;IACrF,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC;AAC3C,IAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;;IAErB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC;AAChC,IAAA,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9B;;AClSA;AACA,MAAM,yBAAyB,GAAuC;IACpE,OAAO,EAAE,OAAO,QAAgB,EAAE,IAAgC,KAChE,IAAI,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;CAClD;AAED;;;AAGG;AACH,SAAS,UAAU,CAAC,QAAsC,EAAA;IACxD,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CACtF,QAAQ,CACT;AACH;AAEA;;;AAGG;AACH,SAAS,cAAc,GAAA;AACrB,IAAA,OAAO,CAAC,CAAC,MAAM,CAAC,oBAAoB;AACtC;AAEA;AACO,eAAe,mBAAmB,CAAC,EAAuB,EAAA;AAC/D,IAAA,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;AACrD,IAAA,MAAM,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;AACzC;AAEA;AACM,MAAO,mCAAoC,SAAQ,kBAExD,CAAA;;AAES,IAAA,QAAQ;;AAGR,IAAA,kBAAkB;IAE1B,WACE,CAAA,cAA0C,EAC1C,OAA4C,EAAA;QAE5C,KAAK,CAAC,cAAc,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,EAAC,GAAG,yBAAyB,EAAE,GAAG,OAAO,EAAC;QAC1D,IAAI,CAAC,kBAAkB,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;;;IAIvD,OAAO,gBAAgB,CAAC,EAAe,EAAA;AACrC,QAAA,IAAI,EAAE,YAAY,wBAAwB,EAAE;AAC1C,YAAA,OAAO,EAAE,CAAC,OAAO,EAAE;;AAErB,QAAA,MAAM,KAAK,CAAC,qEAAqE,CAAC;;;AAIpF,IAAA,OAAO,MAAM,CACX,MAA2B,EAC3B,OAA4C,EAAA;QAE5C,OAAO,IAAI,mCAAmC,CAC5C,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAClD,OAAO,CACR;;AAGH;;;;AAIG;AACH,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;;;AAIxE,IAAA,MAAM,0BAA0B,GAAA;;;;;IAMtB,eAAe,GAAA;QACvB,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;;AAI5E,IAAA,iBAAiB,CAAC,OAAmC,EAAA;QAC7D,OAAO,IAAI,wBAAwB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC;;;AAI7D,IAAA,iBAAiB,CACzB,OAAmC,EAAA;QAEnC,OAAO,IAAI,mCAAmC,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;;;;;AAMxE;;AAEG;IACO,MAAM,iBAAiB,CAAC,QAAgB,EAAA;AAChD,QAAA,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC;AACtE,QAAA,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAuB,KAAK,MAAM,CAAC,CAAC;;AAEvD;;;;"}