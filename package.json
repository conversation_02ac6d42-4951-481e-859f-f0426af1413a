{"name": "my-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "19.2.14", "@angular/cdk": "19.2.14", "@angular/common": "19.2.14", "@angular/compiler": "19.2.14", "@angular/core": "19.2.14", "@angular/forms": "19.2.14", "@angular/platform-browser": "19.2.14", "@angular/platform-browser-dynamic": "19.2.14", "@angular/router": "19.2.14", "ag-grid-angular": "^27.2.1", "ag-grid-community": "^27.2.1", "ag-grid-enterprise": "^27.2.1", "rxjs": "7.8.1", "tslib": "^2.3.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.14", "@angular/cli": "19.2.14", "@angular/compiler-cli": "19.2.14", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.5.2"}}