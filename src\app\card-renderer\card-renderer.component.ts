import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";

@Component({
  selector: "app-card-renderer",
  template: `
    <div class="card">
      <div><strong>ID:</strong> {{ params.data.id }}</div>
      <div><strong>Name:</strong> {{ params.data.name }}</div>
      <div><strong>Email:</strong> {{ params.data.email }}</div>
      <div><strong>Phone:</strong> {{ params.data.phone }}</div>
      <div><strong>City:</strong> {{ params.data.city }}</div>
    </div>
  `,
})
export class CardRendererComponent implements ICellRendererAngularComp {
  params: any;
  agInit(params: any): void {
    this.params = params;
  }
  refresh(): boolean {
    return false;
  }
}
