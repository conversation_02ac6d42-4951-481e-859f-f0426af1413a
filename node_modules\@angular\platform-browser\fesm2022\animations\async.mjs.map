{"version": 3, "file": "async.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/animations/async/src/async_animation_renderer.ts", "../../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/animations/async/src/providers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ɵAnimationEngine as AnimationEngine,\n  ɵAnimationRenderer as AnimationRenderer,\n  ɵAnimationRendererFactory as AnimationRendererFactory,\n} from '@angular/animations/browser';\nimport {\n  ɵAnimationRendererType as AnimationRendererType,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  inject,\n  Injectable,\n  InjectionToken,\n  Injector,\n  NgZone,\n  ɵNotificationSource as NotificationSource,\n  OnDestroy,\n  Renderer2,\n  RendererFactory2,\n  RendererStyleFlags2,\n  RendererType2,\n  ɵRuntimeError as RuntimeError,\n  type ListenerOptions,\n} from '@angular/core';\nimport {ɵRuntimeErrorCode as RuntimeErrorCode} from '../../../index';\n\nconst ANIMATION_PREFIX = '@';\n\n@Injectable()\nexport class AsyncAnimationRendererFactory implements OnDestroy, RendererFactory2 {\n  private _rendererFactoryPromise: Promise<AnimationRendererFactory> | null = null;\n  private scheduler: ChangeDetectionScheduler | null = null;\n  private readonly injector = inject(Injector);\n  private readonly loadingSchedulerFn = inject(ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN, {\n    optional: true,\n  });\n  private _engine?: AnimationEngine;\n\n  /**\n   *\n   * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)\n   */\n  constructor(\n    private doc: Document,\n    private delegate: RendererFactory2,\n    private zone: NgZone,\n    private animationType: 'animations' | 'noop',\n    private moduleImpl?: Promise<{\n      ɵcreateEngine: (type: 'animations' | 'noop', doc: Document) => AnimationEngine;\n      ɵAnimationRendererFactory: typeof AnimationRendererFactory;\n    }>,\n  ) {}\n\n  /** @docs-private */\n  ngOnDestroy(): void {\n    // When the root view is removed, the renderer defers the actual work to the\n    // `TransitionAnimationEngine` to do this, and the `TransitionAnimationEngine` doesn't actually\n    // remove the DOM node, but just calls `markElementAsRemoved()`. The actual DOM node is not\n    // removed until `TransitionAnimationEngine` \"flushes\".\n    // Note: we already flush on destroy within the `InjectableAnimationEngine`. The injectable\n    // engine is not provided when async animations are used.\n    this._engine?.flush();\n  }\n\n  /**\n   * @internal\n   */\n  private loadImpl(): Promise<AnimationRendererFactory> {\n    // Note on the `.then(m => m)` part below: Closure compiler optimizations in g3 require\n    // `.then` to be present for a dynamic import (or an import should be `await`ed) to detect\n    // the set of imported symbols.\n    const loadFn = () => this.moduleImpl ?? import('@angular/animations/browser').then((m) => m);\n\n    let moduleImplPromise: typeof this.moduleImpl;\n    if (this.loadingSchedulerFn) {\n      moduleImplPromise = this.loadingSchedulerFn(loadFn);\n    } else {\n      moduleImplPromise = loadFn();\n    }\n\n    return moduleImplPromise\n      .catch((e) => {\n        throw new RuntimeError(\n          RuntimeErrorCode.ANIMATION_RENDERER_ASYNC_LOADING_FAILURE,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            'Async loading for animations package was ' +\n              'enabled, but loading failed. Angular falls back to using regular rendering. ' +\n              \"No animations will be displayed and their styles won't be applied.\",\n        );\n      })\n      .then(({ɵcreateEngine, ɵAnimationRendererFactory}) => {\n        // We can't create the renderer yet because we might need the hostElement and the type\n        // Both are provided in createRenderer().\n        this._engine = ɵcreateEngine(this.animationType, this.doc);\n        const rendererFactory = new ɵAnimationRendererFactory(\n          this.delegate,\n          this._engine,\n          this.zone,\n        );\n        this.delegate = rendererFactory;\n        return rendererFactory;\n      });\n  }\n\n  /**\n   * This method is delegating the renderer creation to the factories.\n   * It uses default factory while the animation factory isn't loaded\n   * and will rely on the animation factory once it is loaded.\n   *\n   * Calling this method will trigger as side effect the loading of the animation module\n   * if the renderered component uses animations.\n   */\n  createRenderer(hostElement: any, rendererType: RendererType2): Renderer2 {\n    const renderer = this.delegate.createRenderer(hostElement, rendererType);\n\n    if ((renderer as AnimationRenderer).ɵtype === AnimationRendererType.Regular) {\n      // The factory is already loaded, this is an animation renderer\n      return renderer;\n    }\n\n    // We need to prevent the DomRenderer to throw an error because of synthetic properties\n    if (typeof (renderer as any).throwOnSyntheticProps === 'boolean') {\n      (renderer as any).throwOnSyntheticProps = false;\n    }\n\n    // Using a dynamic renderer to switch the renderer implementation once the module is loaded.\n    const dynamicRenderer = new DynamicDelegationRenderer(renderer);\n\n    // Kick off the module loading if the component uses animations but the module hasn't been\n    // loaded yet.\n    if (rendererType?.data?.['animation'] && !this._rendererFactoryPromise) {\n      this._rendererFactoryPromise = this.loadImpl();\n    }\n\n    this._rendererFactoryPromise\n      ?.then((animationRendererFactory) => {\n        const animationRenderer = animationRendererFactory.createRenderer(\n          hostElement,\n          rendererType,\n        );\n        dynamicRenderer.use(animationRenderer);\n        this.scheduler ??= this.injector.get(ChangeDetectionScheduler, null, {optional: true});\n        this.scheduler?.notify(NotificationSource.AsyncAnimationsLoaded);\n      })\n      .catch((e) => {\n        // Permanently use regular renderer when loading fails.\n        dynamicRenderer.use(renderer);\n      });\n\n    return dynamicRenderer;\n  }\n\n  begin(): void {\n    this.delegate.begin?.();\n  }\n\n  end(): void {\n    this.delegate.end?.();\n  }\n\n  whenRenderingDone?(): Promise<any> {\n    return this.delegate.whenRenderingDone?.() ?? Promise.resolve();\n  }\n\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  protected componentReplaced(componentId: string) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this._engine?.flush();\n    (this.delegate as {componentReplaced?: (id: string) => void}).componentReplaced?.(componentId);\n  }\n}\n\n/**\n * The class allows to dynamicly switch between different renderer implementations\n * by changing the delegate renderer.\n */\nexport class DynamicDelegationRenderer implements Renderer2 {\n  // List of callbacks that need to be replayed on the animation renderer once its loaded\n  private replay: ((renderer: Renderer2) => void)[] | null = [];\n  readonly ɵtype = AnimationRendererType.Delegated;\n\n  constructor(private delegate: Renderer2) {}\n\n  use(impl: Renderer2) {\n    this.delegate = impl;\n\n    if (this.replay !== null) {\n      // Replay queued actions using the animation renderer to apply\n      // all events and properties collected while loading was in progress.\n      for (const fn of this.replay) {\n        fn(impl);\n      }\n      // Set to `null` to indicate that the queue was processed\n      // and we no longer need to collect events and properties.\n      this.replay = null;\n    }\n  }\n\n  get data(): {[key: string]: any} {\n    return this.delegate.data;\n  }\n\n  destroy(): void {\n    this.replay = null;\n    this.delegate.destroy();\n  }\n\n  createElement(name: string, namespace?: string | null) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value: string): void {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value: string): any {\n    return this.delegate.createText(value);\n  }\n\n  get destroyNode(): ((node: any) => void) | null {\n    return this.delegate.destroyNode;\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any, isMove?: boolean | undefined): void {\n    this.delegate.insertBefore(parent, newChild, refChild, isMove);\n  }\n\n  removeChild(parent: any, oldChild: any, isHostElement?: boolean | undefined): void {\n    this.delegate.removeChild(parent, oldChild, isHostElement);\n  }\n\n  selectRootElement(selectorOrNode: any, preserveContent?: boolean | undefined): any {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node: any): any {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node: any): any {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string | null | undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string | null | undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el: any, name: string): void {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el: any, name: string): void {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el: any, style: string, flags?: RendererStyleFlags2 | undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    // We need to keep track of animation properties set on default renderer\n    // So we can also set them also on the animation renderer\n    if (this.shouldReplay(name)) {\n      this.replay!.push((renderer: Renderer2) => renderer.setProperty(el, name, value));\n    }\n    this.delegate.setProperty(el, name, value);\n  }\n\n  setValue(node: any, value: string): void {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(\n    target: any,\n    eventName: string,\n    callback: (event: any) => boolean | void,\n    options?: ListenerOptions,\n  ): () => void {\n    // We need to keep track of animation events registred by the default renderer\n    // So we can also register them against the animation renderer\n    if (this.shouldReplay(eventName)) {\n      this.replay!.push((renderer: Renderer2) =>\n        renderer.listen(target, eventName, callback, options),\n      );\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n\n  private shouldReplay(propOrEventName: string): boolean {\n    //`null` indicates that we no longer need to collect events and properties\n    return this.replay !== null && propOrEventName.startsWith(ANIMATION_PREFIX);\n  }\n}\n\n/**\n * Provides a custom scheduler function for the async loading of the animation package.\n *\n * Private token for investigation purposes\n */\nexport const ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN = new InjectionToken<<T>(loadFn: () => T) => T>(\n  ngDevMode ? 'async_animation_loading_scheduler_fn' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  EnvironmentProviders,\n  makeEnvironmentProviders,\n  NgZone,\n  RendererFactory2,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '../../../index';\n\nimport {AsyncAnimationRendererFactory} from './async_animation_renderer';\n\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * When you use this function instead of the eager `provideAnimations()`, animations won't be\n * rendered until the renderer is loaded.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimationsAsync()\n *   ]\n * });\n * ```\n *\n * @param type pass `'noop'` as argument to disable animations.\n *\n * @publicApi\n */\nexport function provideAnimationsAsync(\n  type: 'animations' | 'noop' = 'animations',\n): EnvironmentProviders {\n  performanceMarkFeature('NgAsyncAnimations');\n\n  // Animations don't work on the server so we switch them over to no-op automatically.\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    type = 'noop';\n  }\n\n  return makeEnvironmentProviders([\n    {\n      provide: RendererFactory2,\n      useFactory: (doc: Document, renderer: DomRendererFactory2, zone: NgZone) => {\n        return new AsyncAnimationRendererFactory(doc, renderer, zone, type);\n      },\n      deps: [DOCUMENT, DomRendererFactory2, NgZone],\n    },\n    {\n      provide: ANIMATION_MODULE_TYPE,\n      useValue: type === 'noop' ? 'NoopAnimations' : 'BrowserAnimations',\n    },\n  ]);\n}\n"], "names": ["RuntimeError", "ChangeDetectionScheduler", "performanceMarkFeature"], "mappings": ";;;;;;;;;;;AAgCA,MAAM,gBAAgB,GAAG,GAAG;MAGf,6BAA6B,CAAA;AAc9B,IAAA,GAAA;AACA,IAAA,QAAA;AACA,IAAA,IAAA;AACA,IAAA,aAAA;AACA,IAAA,UAAA;IAjBF,uBAAuB,GAA6C,IAAI;IACxE,SAAS,GAAoC,IAAI;AACxC,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC3B,IAAA,kBAAkB,GAAG,MAAM,CAAC,qCAAqC,EAAE;AAClF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AACM,IAAA,OAAO;AAEf;;;AAGG;IACH,WACU,CAAA,GAAa,EACb,QAA0B,EAC1B,IAAY,EACZ,aAAoC,EACpC,UAGN,EAAA;QAPM,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAa,CAAA,aAAA,GAAb,aAAa;QACb,IAAU,CAAA,UAAA,GAAV,UAAU;;;IAOpB,WAAW,GAAA;;;;;;;AAOT,QAAA,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE;;AAGvB;;AAEG;IACK,QAAQ,GAAA;;;;QAId,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,IAAI,OAAO,6BAA6B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAE5F,QAAA,IAAI,iBAAyC;AAC7C,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;;aAC9C;YACL,iBAAiB,GAAG,MAAM,EAAE;;AAG9B,QAAA,OAAO;AACJ,aAAA,KAAK,CAAC,CAAC,CAAC,KAAI;YACX,MAAM,IAAIA,aAAY,CAAA,IAAA,kEAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC5C,2CAA2C;oBACzC,8EAA8E;AAC9E,oBAAA,oEAAoE,CACzE;AACH,SAAC;aACA,IAAI,CAAC,CAAC,EAAC,aAAa,EAAE,yBAAyB,EAAC,KAAI;;;AAGnD,YAAA,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC;AAC1D,YAAA,MAAM,eAAe,GAAG,IAAI,yBAAyB,CACnD,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,CACV;AACD,YAAA,IAAI,CAAC,QAAQ,GAAG,eAAe;AAC/B,YAAA,OAAO,eAAe;AACxB,SAAC,CAAC;;AAGN;;;;;;;AAOG;IACH,cAAc,CAAC,WAAgB,EAAE,YAA2B,EAAA;AAC1D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC;AAExE,QAAA,IAAK,QAA8B,CAAC,KAAK,KAAA,CAAA,sCAAoC;;AAE3E,YAAA,OAAO,QAAQ;;;AAIjB,QAAA,IAAI,OAAQ,QAAgB,CAAC,qBAAqB,KAAK,SAAS,EAAE;AAC/D,YAAA,QAAgB,CAAC,qBAAqB,GAAG,KAAK;;;AAIjD,QAAA,MAAM,eAAe,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC;;;AAI/D,QAAA,IAAI,YAAY,EAAE,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACtE,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,EAAE;;AAGhD,QAAA,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,CAAC,wBAAwB,KAAI;YAClC,MAAM,iBAAiB,GAAG,wBAAwB,CAAC,cAAc,CAC/D,WAAW,EACX,YAAY,CACb;AACD,YAAA,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC;AACtC,YAAA,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,CAACC,yBAAwB,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACtF,YAAA,IAAI,CAAC,SAAS,EAAE,MAAM,mDAA0C;AAClE,SAAC;AACA,aAAA,KAAK,CAAC,CAAC,CAAC,KAAI;;AAEX,YAAA,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC/B,SAAC,CAAC;AAEJ,QAAA,OAAO,eAAe;;IAGxB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;;IAGzB,GAAG,GAAA;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;;IAGvB,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;;AAGjE;;;AAGG;AACO,IAAA,iBAAiB,CAAC,WAAmB,EAAA;;AAE7C,QAAA,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE;QACpB,IAAI,CAAC,QAAuD,CAAC,iBAAiB,GAAG,WAAW,CAAC;;kHA9IrF,6BAA6B,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAA7B,6BAA6B,EAAA,CAAA;;sGAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBADzC;;AAmJD;;;AAGG;MACU,yBAAyB,CAAA;AAKhB,IAAA,QAAA;;IAHZ,MAAM,GAA6C,EAAE;AACpD,IAAA,KAAK,GAAmC,CAAA;AAEjD,IAAA,WAAA,CAAoB,QAAmB,EAAA;QAAnB,IAAQ,CAAA,QAAA,GAAR,QAAQ;;AAE5B,IAAA,GAAG,CAAC,IAAe,EAAA;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;AAEpB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;;;AAGxB,YAAA,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC5B,EAAE,CAAC,IAAI,CAAC;;;;AAIV,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI;;;AAItB,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;;IAG3B,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAClB,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;;IAGzB,aAAa,CAAC,IAAY,EAAE,SAAyB,EAAA;QACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC;;AAGrD,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;;AAG3C,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;;AAGxC,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW;;IAGlC,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;QACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC;;AAG7C,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAE,MAA4B,EAAA;AAClF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;;AAGhE,IAAA,WAAW,CAAC,MAAW,EAAE,QAAa,EAAE,aAAmC,EAAA;QACzE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC;;IAG5D,iBAAiB,CAAC,cAAmB,EAAE,eAAqC,EAAA;QAC1E,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC;;AAGzE,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC;;AAGvC,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;;AAGxC,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAqC,EAAA;AACtF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC;;AAGxD,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAqC,EAAA;QAC1E,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC;;IAGpD,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC;;IAGlC,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC;;AAGrC,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAAuC,EAAA;AAClF,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;AAGjD,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAAuC,EAAA;QACzE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC;;AAG7C,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;;;AAG3C,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC,QAAmB,KAAK,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;QAEnF,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;;IAG5C,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC;;AAGrC,IAAA,MAAM,CACJ,MAAW,EACX,SAAiB,EACjB,QAAwC,EACxC,OAAyB,EAAA;;;AAIzB,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;YAChC,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC,QAAmB,KACpC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CACtD;;AAEH,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;;AAG3D,IAAA,YAAY,CAAC,eAAuB,EAAA;;AAE1C,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC;;AAE9E;AAED;;;;AAIG;AACU,MAAA,qCAAqC,GAAG,IAAI,cAAc,CACrE,SAAS,GAAG,sCAAsC,GAAG,EAAE;;AC5SzD;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACa,SAAA,sBAAsB,CACpC,IAAA,GAA8B,YAAY,EAAA;IAE1CC,uBAAsB,CAAC,mBAAmB,CAAC;;AAG3C,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;QACvD,IAAI,GAAG,MAAM;;AAGf,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE,CAAC,GAAa,EAAE,QAA6B,EAAE,IAAY,KAAI;gBACzE,OAAO,IAAI,6BAA6B,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;aACpE;AACD,YAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,CAAC;AAC9C,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,qBAAqB;YAC9B,QAAQ,EAAE,IAAI,KAAK,MAAM,GAAG,gBAAgB,GAAG,mBAAmB;AACnE,SAAA;AACF,KAAA,CAAC;AACJ;;;;"}