{"version": 3, "file": "element-x4z00URv.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/number-property.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Type describing the allowed values for a number input\n * @docs-private\n */\nexport type NumberInput = string | number | null | undefined;\n\n/** Coerces a data-bound value (typically a string) to a number. */\nexport function coerceNumberProperty(value: any): number;\nexport function coerceNumberProperty<D>(value: any, fallback: D): number | D;\nexport function coerceNumberProperty(value: any, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nexport function _isNumberValue(value: any): boolean {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value as any)) && !isNaN(Number(value));\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ElementRef} from '@angular/core';\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nexport function coerceElement<T>(elementOrRef: ElementRef<T> | T): T {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n"], "names": [], "mappings": ";;SAiBgB,oBAAoB,CAAC,KAAU,EAAE,aAAa,GAAG,CAAC,EAAA;AAChE,IAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AACzB,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC;;AAEtB,IAAA,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC;AACnD;AAEA;;;AAGG;AACG,SAAU,cAAc,CAAC,KAAU,EAAA;;;;AAIvC,IAAA,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,KAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClE;;ACvBA;;;AAGG;AACG,SAAU,aAAa,CAAI,YAA+B,EAAA;AAC9D,IAAA,OAAO,YAAY,YAAY,UAAU,GAAG,YAAY,CAAC,aAAa,GAAG,YAAY;AACvF;;;;"}