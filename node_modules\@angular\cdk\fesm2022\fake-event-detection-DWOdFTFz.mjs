/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */
function isFakeMousedownFromScreenReader(event) {
    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on
    // a clickable element. We can distinguish these events when `event.buttons` is zero, or
    // `event.detail` is zero depending on the browser:
    // - `event.buttons` works on Firefox, but fails on Chrome.
    // - `detail` works on Chrome, but fails on Firefox.
    return event.buttons === 0 || event.detail === 0;
}
/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */
function isFakeTouchstartFromScreenReader(event) {
    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);
    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`
    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,
    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10
    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.
    return (!!touch &&
        touch.identifier === -1 &&
        (touch.radiusX == null || touch.radiusX === 1) &&
        (touch.radiusY == null || touch.radiusY === 1));
}

export { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };
//# sourceMappingURL=fake-event-detection-DWOdFTFz.mjs.map
