{"version": 3, "file": "fake-event-detection-DWOdFTFz.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/fake-event-detection.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nexport function isFakeMousedownFromScreenReader(event: MouseEvent): boolean {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nexport function isFakeTouchstartFromScreenReader(event: TouchEvent): boolean {\n  const touch: Touch | undefined =\n    (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return (\n    !!touch &&\n    touch.identifier === -1 &&\n    (touch.radiusX == null || touch.radiusX === 1) &&\n    (touch.radiusY == null || touch.radiusY === 1)\n  );\n}\n"], "names": [], "mappings": "AAQA;AACM,SAAU,+BAA+B,CAAC,KAAiB,EAAA;;;;;;IAM/D,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;AAClD;AAEA;AACM,SAAU,gCAAgC,CAAC,KAAiB,EAAA;IAChE,MAAM,KAAK,GACT,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;;;;;IAM1F,QACE,CAAC,CAAC,KAAK;AACP,QAAA,KAAK,CAAC,UAAU,KAAK,CAAC,CAAC;SACtB,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;AAC9C,SAAC,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,CAAC;AAElD;;;;"}